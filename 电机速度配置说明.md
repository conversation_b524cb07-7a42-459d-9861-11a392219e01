# 电机速度配置说明

## 速度等级定义

根据要求，电机运行速度已调整为以下四个等级：

### 1. 最快速度 (Motor_Fast_Speed = 1)
- **步进频率**：约3000步/秒
- **中断周期**：1ms
- **用途**：快速启动、方向切换时的加速阶段

### 2. 中等速度 (Motor_Middle_Speed = 3) 
- **步进频率**：约333步/秒 (1000ms/3ms)
- **中断周期**：3ms
- **用途**：正常运行速度、自转模式

### 3. 慢速 (Motor_Slow_Speed = 17)
- **步进频率**：约59步/秒 (1000ms/17ms)
- **中断周期**：17ms
- **用途**：精确定位、按键控制

### 4. 最慢速 (Motor_Slowest_Speed = 20)
- **步进频率**：50步/秒 (1000ms/20ms)
- **中断周期**：20ms
- **用途**：超精确定位、特殊UART命令

## 速度应用场景

### 自转模式
- **使用速度**：Motor_Auto_Rotate_Speed = 3 (中等速度)
- **特点**：平衡速度与精度

### 按键控制
- **使用速度**：Motor_Key_Control_Speed = 17 (慢速)
- **特点**：便于用户精确控制

### UART命令速度映射

#### 中等速度命令
- **0xC2**：顺时针中等速度
- **0xC4**：逆时针中等速度  
- **0xCA**：顺时针中等速度(长按)
- **0xCC**：逆时针中等速度(长按)

#### 最慢速度命令
- **0xC5**：逆时针最慢速度
- **0xC6**：顺时针最慢速度
- **0xCD**：逆时针最慢速度(长按)
- **0xCE**：顺时针最慢速度(长按)

## 速度控制机制

### 定时器中断控制
系统使用TMR0的1ms中断来控制电机步进频率：

```c
// 在TMR0中断中
Num++;
if (Num == Motor_Speed_Data)
{
    Bit_N_ms_Buff = 1;  // 触发步进
    Num = 0;
}
```

### 加速机制
- **启动加速**：方向切换时自动使用最快速度
- **加速时长**：由dly参数控制（默认200ms）
- **正常速度**：加速结束后恢复到设定速度

## 性能参数对比

| 速度等级 | 定义值 | 步进频率 | 转速(步/秒) | 适用场景 |
|---------|--------|----------|-------------|----------|
| 最快速度 | 1 | 1000Hz | ~3000 | 加速阶段 |
| 中等速度 | 3 | 333Hz | ~333 | 正常运行 |
| 慢速 | 17 | 59Hz | ~59 | 精确控制 |
| 最慢速 | 20 | 50Hz | 50 | 超精确 |

## 注意事项

### 1. 速度切换
- 方向改变时会自动进入加速模式
- 加速模式使用最快速度，持续时间由dly控制
- 加速结束后恢复到目标速度

### 2. 中断负载
- 最快速度时中断频率最高，CPU负载较大
- 建议仅在必要时使用最快速度
- 长时间运行建议使用中等速度或慢速

### 3. 精度考虑
- 速度越慢，定位精度越高
- 按键控制和精确定位建议使用慢速或最慢速
- 自转模式使用中等速度平衡效率和精度

### 4. 功耗影响
- 速度越快，功耗越高
- 电池供电时建议优先使用较慢速度
- 可根据电池电量动态调整速度

## 修改历史

- **原始配置**：
  - Motor_Fast_Speed = 5
  - Motor_Middle_Speed = 18  
  - Motor_Slow_Speed = 152

- **新配置**：
  - Motor_Fast_Speed = 1 (最快速度，约3000步/秒)
  - Motor_Middle_Speed = 3 (中等速度，约333步/秒)
  - Motor_Slow_Speed = 17 (慢速，约59步/秒)
  - Motor_Slowest_Speed = 20 (最慢速，50步/秒)

这样的配置提供了更合理的速度梯度，满足不同应用场景的需求。
