/****************************************************************************/
/** \file isr.c
**	History:
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "cms8s6990.h"
#include "define.h"
#include "UART_Function.h"

/****************************************************************************/
/*	Local pre-processor symbols('#define')
****************************************************************************/
#define stop_RF_cnts		1

/****************************************************************************/
/*	Global variable definitions(declared in header file with 'extern')
****************************************************************************/

/****************************************************************************/
/*	Local type definitions('typedef')
****************************************************************************/

/****************************************************************************/
/*	Local variable  definitions('static')
****************************************************************************/
extern int No_Step,Speed;
extern bit Direction,Get_String_Buff;
extern int Led_G_Toggle,Led_R_Toggle;
extern int R_STRING;
extern int Len_string;
extern int U_string[5];
int Num_Forward_Pulse,Num_Reverse_Pulse;
extern uint8_t K1_cnt,K2_cnt,K3_cnt;
extern bit K1_cnt_EN;
extern bit K2_cnt_EN;
extern bit K3_cnt_EN;
extern volatile bit longhit;
extern volatile bit speedup; 
extern uint16_t longhit_cnt;
extern bit direction_changed;
extern uint8_t last_direction;
extern uint16_t speedup_cnt;
extern uint16_t dly;
extern volatile bit key1_handle;
extern volatile bit key3_handle;
extern volatile uint16_t key1_duration;
extern volatile uint16_t key3_duration;
extern volatile uint16_t timer_1ms_count;
extern volatile uint16_t key1_press_time;
extern volatile uint16_t key3_press_time;
extern volatile bit key1_pressed;
extern volatile bit key3_pressed;
extern volatile uint16_t precise_k2_timer;  // ???K2?????
extern bit use_precise_timer;  // ?????????????

/****************************************************************************/
/*	Local function prototypes('static')
****************************************************************************/

/****************************************************************************/
/*	Function implementation - global ('extern') and local('static')
****************************************************************************/

/******************************************************************************
 ** \brief	 INT0 interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void INT0_IRQHandler(void) interrupt INT0_VECTOR
{
    ;
}

/******************************************************************************
 ** \brief	 Timer 0 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer0_IRQHandler(void) interrupt TMR0_VECTOR 
{
    TH0 = Time_1ms_Offset >> 8;
    TL0 = Time_1ms_Offset;

    Bit_1_ms_Buff = 1;
    timer_1ms_count++;  
    Num++;
    if (Num > Motor_Slowest_Speed + 1) Num = 0;
    
    switch (Motor_Speed_Data)
    {
        case Motor_Fast_Speed:
		{
            if (Num == Motor_Fast_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
		}
            break;
        case Motor_Slow_Speed:
            if (Num == Motor_Slow_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
            break;
        case Motor_Middle_Speed:
		{
            if (Num == Motor_Middle_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
		}
            break;
        case Motor_Auto_Rotate_Speed:
		{
            if (Num == Motor_Auto_Rotate_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
		}
            break;
        case Motor_Key_Control_Speed:
		{
            if (Num == Motor_Key_Control_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
		}
            break;
        case Motor_Slowest_Speed:
		{
            if (Num == Motor_Slowest_Speed)
            {
                Bit_N_ms_Buff = 1;
                Num = 0;
            }
		}
            break;
        default:
            break;
    }
    
    if(K2_cnt_EN)
    {
        if(K2 == 0) K2_cnt++;
    }
    else
    {
        K2_cnt = 0;
    }
    
    if(K2_cnt > 150)
    {
        longhit = 1;
        longhit_cnt++;
    }
    else
    {
        longhit = 0;
        longhit_cnt = 0;
    }

	if(speedup == 1)
	{
		speedup_cnt++;
		if(speedup_cnt >= dly)
		{
			speedup = 0;
			speedup_cnt = 0;
		}
	}
	else
	{
		speedup_cnt = 0;
	}


}

/******************************************************************************
 ** \brief	 INT0 interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void INT1_IRQHandler(void) interrupt INT1_VECTOR
{
    ;
}

/******************************************************************************
 ** \brief	 Timer 1 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer1_IRQHandler(void) interrupt TMR1_VECTOR
{
    // ??????????????2ms?????
    TH1 = (65536-4000)>>8;
    TL1 = 65536-4000;

    // ???K2???????
    if(use_precise_timer)
    {
        precise_k2_timer++;
    }
}

/******************************************************************************
 ** \brief	 UART 0 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void UART0_IRQHandler(void) interrupt UART0_VECTOR 
{
    if(UART_GetReceiveIntFlag(UART0))
    {
        Get_String_Buff = 1;
        Get_String_Wait_Time = 0;
        UART_Data_Copy(UART_Get_String, UART_GetBuff(UART0));
        UART_ClearReceiveIntFlag(UART0);	
    }
}

/******************************************************************************
 ** \brief	 Timer 2 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer2_IRQHandler(void) interrupt TMR2_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 UART 1 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void UART1_IRQHandler(void) interrupt UART1_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 GPIO 0 interrupt service function
 **	
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void P0EI_IRQHandler(void) interrupt P0EI_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 GPIO 1 interrupt service function
 **
 ** \param [in]  none
 **
 ** \return none
 ******************************************************************************/
void P1EI_IRQHandler(void) interrupt P1EI_VECTOR 
{
    // ???P1???????????????6???????0x40??
    P1EXTIF &= ~0x40;  

    // ???K1????????????????????
    if(K1 == 0)  
    {
        // ????????????key1_pressed?0??
        if(key1_pressed == 0)
        {
            // ???????????????
            key1_press_time = timer_1ms_count;
            // ??????????????
            key1_pressed = 1;
            // ??????????????
            key1_handle = 0; 
        }
    }
    else  // ???????
    {
        // ????????????
        if(key1_pressed == 1) 
        {
            // ?????????????????ms??
            key1_duration = timer_1ms_count - key1_press_time;
            // ????????????10-5000ms??????
            if(key1_duration >= 10 && key1_duration <= 5000)  
            {
                // ??????????????????????????????????
                key1_handle = 1; 
            }
            // ??????????????
            key1_pressed = 0;
        }
    }
}

/******************************************************************************
 ** \brief	 GPIO 2 interrupt service function
 **
 ** \param [in]  none
 **
 ** \return none
 ******************************************************************************/
void P2EI_IRQHandler(void) interrupt P2EI_VECTOR
{
    P2EXTIF &= ~0x02;  

    if(K3 == 0)  
    {
        
        if(key3_pressed == 0)
        {
            key3_press_time = timer_1ms_count;
            key3_pressed = 1;
            key3_handle = 0; 
        }
    }
    else  
    {
        if(key3_pressed == 1) 
        {
            key3_duration = timer_1ms_count - key3_press_time;
            if(key3_duration >= 10 && key3_duration <= 5000)  
            {
                key3_handle = 1;  
            }
            key3_pressed = 0;
        }
    }
}

/******************************************************************************
 ** \brief	 GPIO 3 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void P3EI_IRQHandler(void) interrupt P3EI_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 LVD interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void LVD_IRQHandler(void) interrupt LVD_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 LSE interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void LSE_IRQHandler(void) interrupt LSE_VECTOR 
{
    ;
}

/********************************************************************************
 ** \brief	 ACMP interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void ACMP_IRQHandler(void) interrupt ACMP_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 Timer 3 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer3_IRQHandler(void) interrupt TMR3_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 Timer 4 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer4_IRQHandler(void) interrupt TMR4_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 EPWM interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void EPWM_IRQHandler(void) interrupt EPWM_VECTOR
{
    ;
}

/******************************************************************************
 ** \brief	 ADC interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void ADC_IRQHandler(void) interrupt ADC_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	 WDT interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void WDT_IRQHandler(void) interrupt WDT_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	I2C interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void I2C_IRQHandler(void) interrupt I2C_VECTOR 
{
    ;
}

/******************************************************************************
 ** \brief	SPI interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void SPI_IRQHandler(void) interrupt SPI_VECTOR 
{
    ;
}