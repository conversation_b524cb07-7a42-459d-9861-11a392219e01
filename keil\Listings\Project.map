LX51 LINKER/LOCATER V4.66.97.0                                                          07/22/2025  14:57:02  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   0022A3H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000A4H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000006H.3 BIT
C:000000H   C:000000H   C:00FFFFH   00000DH   CONST
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000024H.5 000004H.6 BIT    UNIT     BIT            ?BI?MAIN
000024H.6 000025H.2 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000025H.3 000025H.6 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000025H.7 000026H.2 000000H.4 BIT    UNIT     BIT            _BIT_GROUP_
000026H.3 000026H   000000H.5 ---    ---      **GAP**
000027H   000027H   000001H   BYTE   UNIT     IDATA          ?STACK

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 3


* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000028H   000003H   BYTE   UNIT     CODE           ?PR?ADC_GETRESULT?ADC_USED
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000038H   000038H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000039H   000039H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
00003AH   00003AH   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   00003EH   000001H   BYTE   UNIT     CODE           ?PR?ADC_CLEARCONVERTINTFLAG?ADC_USED
00003FH   000042H   000004H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008FH   00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCONVERTINTFLAG?ADC_USED
000090H   000092H   000003H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   0009A4H   0008EFH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0009A5H   0011B6H   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
0011B7H   001321H   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
001322H   001477H   000156H   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
001478H   0015A1H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 4


0015A2H   0016BAH   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
0016BBH   0017CAH   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
0017CBH   0018C2H   0000F8H   BYTE   UNIT     CODE           ?C_INITSEG
0018C3H   00198DH   0000CBH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
00198EH   001A27H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001A28H   001ABAH   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001ABBH   001B32H   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001B33H   001BAAH   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001BABH   001C1DH   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
001C1EH   001C8DH   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
001C8EH   001CECH   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
001CEDH   001D49H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
001D4AH   001DA4H   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
001DA5H   001DF3H   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
001DF4H   001E3FH   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
001E40H   001E89H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001E8AH   001ED0H   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
001ED1H   001F0FH   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
001F10H   001F49H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
001F4AH   001F7EH   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001F7FH   001FB2H   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001FB3H   001FE3H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
001FE4H   002010H   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
002011H   00203AH   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
00203BH   002063H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
002064H   00208AH   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
00208BH   0020B0H   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
0020B1H   0020D5H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
0020D6H   0020FAH   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
0020FBH   00211EH   000024H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
00211FH   00213EH   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
00213FH   00215EH   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
00215FH   00217EH   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
00217FH   00219DH   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
00219EH   0021BCH   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
0021BDH   0021DBH   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
0021DCH   0021F9H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0021FAH   002216H   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
002217H   002230H   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
002231H   002246H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
002247H   00225AH   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
00225BH   00226DH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
00226EH   002280H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
002281H   002293H   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
002294H   0022A4H   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
0022A5H   0022ADH   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
0022AEH   0022B6H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
0022B7H   0022BFH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
0022C0H   0022C8H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_STARTCONVERT?ADC_USED
0022C9H   0022D0H   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
0022D1H   0022D6H   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
0022D7H   0022DCH   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
0022DDH   0022E9H   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000034H   000035H   BYTE   UNIT     XDATA          ?XD?MAIN
000035H   000058H   000024H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000059H   000079H   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
00007AH   00008EH   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
00008FH   000099H   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
00009AH   00009FH   000006H   BYTE   UNIT     XDATA          ?XD?KEY
0000A0H   0000A3H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 7


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
   *DEL*:           00000FH   BYTE   UNIT     XDATA          ?XD?_ADC?ADC_USED
   *DEL*:           00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
   *DEL*:           00002AH   BYTE   UNIT     CODE           ?PR?TIMER_ACCURACY_TEST?MAIN
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP
======================================================================
?C_C51STARTUP                                 ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     25H.7 26H.2  0035H 0045H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _TMR_STOP/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_START/TIMER
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 8



_DELAY1MS/MAIN                                ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----

_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  0046H 004BH
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  0046H 004BH
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 9


TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  0046H 0047H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  0046H 0047H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  0046H 0053H
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  0054H 0058H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----
  +--> _ADC_STARTCONVERT/ADC_USED
  +--> ADC_GETCONVERTINTFLAG/ADC_USED
  +--> ADC_GETRESULT/ADC_USED
  +--> ADC_CLEARCONVERTINTFLAG/ADC_USED

_ADC_STARTCONVERT/ADC_USED                    ----- -----  ----- -----
  +--> _ADC_ENABLECHANNEL/ADC

ADC_GETCONVERTINTFLAG/ADC_USED                ----- -----  ----- -----

ADC_GETRESULT/ADC_USED                        ----- -----  ----- -----
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----

ADC_CLEARCONVERTINTFLAG/ADC_USED              ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----

_TMR_STOP/TIMER                               ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 10


KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 11


P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      02000054H   XDATA    BYTE      ?_UART_Send_String?BYTE
      01001191H   CODE     ---       ?C?CCASE
      01000F8CH   CODE     ---       ?C?CLDOPTR
      01000F73H   CODE     ---       ?C?CLDPTR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 12


      00000000H   NUMBER   ---       ?C?CODESEG
      01000F4DH   CODE     ---       ?C?COPY
      01000FCBH   CODE     ---       ?C?CSTOPTR
      01000FB9H   CODE     ---       ?C?CSTPTR
      01000B55H   CODE     ---       ?C?FCASTC
      01000B50H   CODE     ---       ?C?FCASTI
      01000B4BH   CODE     ---       ?C?FCASTL
      01000D1CH   CODE     ---       ?C?FPADD
      01000C10H   CODE     ---       ?C?FPCONVERT
      01000AAEH   CODE     ---       ?C?FPDIV
      01000B89H   CODE     ---       ?C?FPGETOPN2
      010009A5H   CODE     ---       ?C?FPMUL
      01000BBEH   CODE     ---       ?C?FPNANRESULT
      01000BC8H   CODE     ---       ?C?FPOVERFLOW
      01000BA0H   CODE     ---       ?C?FPRESULT
      01000BB4H   CODE     ---       ?C?FPRESULT2
      01000BD3H   CODE     ---       ?C?FPROUND
      01000D18H   CODE     ---       ?C?FPSUB
      01000BC5H   CODE     ---       ?C?FPUNDERFLOW
      01000E3DH   CODE     ---       ?C?FTNPWR
      01001042H   CODE     ---       ?C?ILDIX
      01001126H   CODE     ---       ?C?LNEG
      01001140H   CODE     ---       ?C?LSTKXDATA
      01001134H   CODE     ---       ?C?LSTXDATA
      01001171H   CODE     ---       ?C?PLDIXDATA
      01001188H   CODE     ---       ?C?PSTXDATA
      01000FEDH   CODE     ---       ?C?UIDIV
      01001094H   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010019E3H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      010022A5H   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      01002231H   CODE     ---       _ADC_ConfigRunMode
      010021DCH   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      010022C0H   CODE     ---       _ADC_StartConvert
      0100215FH   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 13


*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01001FB3H   CODE     ---       _FLASH_Erase
      01001F7FH   CODE     ---       _FLASH_Read
      01001F4AH   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001BABH   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01001E8AH   CODE     ---       _Key_Function_Switch_System
      01001A28H   CODE     ---       _Motor_Step_Control
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
      01001FE4H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01001DF4H   CODE     ---       _TMR_ConfigRunMode
      01001E40H   CODE     ---       _TMR_ConfigTimerClk
      01002064H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010021FAH   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      0100217FH   CODE     ---       _TMR_Start
      0100219EH   CODE     ---       _TMR_Stop
      010021BDH   CODE     ---       _UART_ClearReceiveIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      010022AEH   CODE     ---       _UART_ConfigBRTClk
      010022B7H   CODE     ---       _UART_ConfigBRTPeriod
      01001C8EH   CODE     ---       _UART_ConfigRunMode
      0100211FH   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      0100225BH   CODE     ---       _UART_EnableDoubleFrequency
      01002294H   CODE     ---       _UART_EnableInt
      0100226EH   CODE     ---       _UART_EnableReceive
      01002247H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      010020D6H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01001CEDH   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000030H   CODE     ---       ACMP_IRQHandler
      0100003EH   CODE     ---       ADC_ClearConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      01002217H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      010020B1H   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
      01000086H   CODE     ---       ADC_GetConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000026H   CODE     ---       ADC_GetResult
      01000037H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.3 BIT      BIT       auto_rotate_flash
      02000014H   XDATA    WORD      auto_rotate_flash_timer
      00000022H.0 BIT      BIT       auto_rotate_mode
      00000020H.7 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000022H.1 BIT      BIT       batlow1
      0200002FH   XDATA    BYTE      batlow1_cnt
      0200001DH   XDATA    BYTE      batlow_cnt
      0200008AH   XDATA    WORD      Battery_ADC_Wait_Time
      010018C3H   CODE     ---       Battery_Check
      0200001CH   XDATA    BYTE      battery_check_divider
      02000030H   XDATA    WORD      BatV
      00000023H.1 BIT      BIT       Bit_1_ms_Buff
      00000024H.1 BIT      BIT       Bit_N_ms_Buff
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 15


      00000023H.5 BIT      BIT       Bit_Toggle
*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000025H.4 BIT      BIT       Center_Line_Control
      00000021H.7 BIT      BIT       Charg_State_Buff
      00000023H.3 BIT      BIT       charge_flash
      02000005H   XDATA    WORD      charge_flash_cnt
      00000022H.7 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      010022D7H   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      0200001EH   XDATA    INT       Count_1_Degree_Pulse
      02000085H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      02000079H   XDATA    BYTE      Data_Length
      00000023H.4 BIT      BIT       Delay_Open
      00000025H.6 BIT      BIT       Delay_Over
      02000087H   XDATA    WORD      Delay_Time
      02000083H   XDATA    WORD      Delay_Time_Count
      00000024H.2 BIT      BIT       direction_changed
      02000024H   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000036H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000025H.5 BIT      BIT       Get_String_Buff
      02000089H   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01001C1EH   CODE     ---       GPIO_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 16


      01002281H   CODE     ---       GPIO_Key_Interrupt_Config
      01000039H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200007CH   XDATA    BYTE      K1_cnt
      00000021H.0 BIT      BIT       K1_cnt_EN
      0200009BH   XDATA    BYTE      K1_Count
      00000024H.6 BIT      BIT       K1_Press
      0200007DH   XDATA    BYTE      K2_cnt
      00000021H.1 BIT      BIT       K2_cnt_EN
      0200009CH   XDATA    BYTE      K2_Count
      00000023H.6 BIT      BIT       k2_long_press_detected
      02000020H   XDATA    WORD      k2_long_press_timer
      00000024H.7 BIT      BIT       K2_Press
      00000022H.2 BIT      BIT       k2_released
      0200007EH   XDATA    BYTE      K3_cnt
      00000021H.2 BIT      BIT       K3_cnt_EN
      0200009DH   XDATA    BYTE      K3_Count
      00000025H.0 BIT      BIT       K3_Press
      00000022H.3 BIT      BIT       k3_released
      0200009EH   XDATA    BYTE      K4_Count
      00000025H.1 BIT      BIT       K4_Press
      0200009FH   XDATA    BYTE      K5_Count
      00000025H.2 BIT      BIT       K5_Press
      02000022H   XDATA    WORD      key1_duration
      00000022H.4 BIT      BIT       key1_handle
      00000023H.7 BIT      BIT       key1_long_started
      0200000CH   XDATA    WORD      key1_press_time
      00000023H.0 BIT      BIT       key1_pressed
      02000026H   XDATA    WORD      key3_duration
      00000022H.5 BIT      BIT       key3_handle
      00000024H.0 BIT      BIT       key3_long_started
      0200000EH   XDATA    WORD      key3_press_time
      00000023H.2 BIT      BIT       key3_pressed
      0200009AH   XDATA    BYTE      Key_Buff
      01001F10H   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010011B7H   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001478H   CODE     ---       Key_Scan
      0200000BH   XDATA    BYTE      key_scan_divider
      00000024H.3 BIT      BIT       key_short_press_mode
      0200002EH   XDATA    BYTE      last_direction
      010016BBH   CODE     ---       LED_Control
      00000021H.6 BIT      BIT       led_flash_state
      0200001AH   XDATA    WORD      led_flash_timer
      00000022H.6 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 17


      02000017H   XDATA    BYTE      ledonoff1_cnt
      02000034H   XDATA    BYTE      ledonoff_cnt
      00000024H.4 BIT      BIT       longhit
      0200007AH   XDATA    WORD      longhit_cnt
      0100002FH   CODE     ---       LSE_IRQHandler
      0100002EH   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
      02000016H   XDATA    BYTE      main_loop_counter
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      0200008EH   XDATA    BYTE      Motor_Direction_Data
      00000020H.6 BIT      BIT       MOTOR_RUNNING_FLAG
      02000081H   XDATA    WORD      Motor_Speed_Data
      00000021H.5 BIT      BIT       need_led_flash
      0200007FH   XDATA    INT       Num
      020000A0H   XDATA    INT       Num_Forward_Pulse
      020000A2H   XDATA    INT       Num_Reverse_Pulse
      0200002BH   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000029H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001ABBH   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001B33H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 18


*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      0100002AH   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000025H.3 BIT      BIT       Power_count_clean
      0200008CH   XDATA    WORD      Power_Off_Wait_Time
      02000032H   XDATA    WORD      precise_k2_timer
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      0100208BH   CODE     ---       Restore_dly
      010022D1H   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000028H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.5 BIT      BIT       speedup
      02000010H   XDATA    WORD      speedup_cnt
      0100003AH   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      0200002DH   XDATA    BYTE      System_Mode_Before_Charge
      0200002AH   XDATA    BYTE      System_Mode_Data
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 19


      02000007H   XDATA    DWORD     Systemclock
*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01001322H   CODE     ---       Timer0_IRQHandler
      010020FBH   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      01000031H   CODE     ---       Timer3_IRQHandler
      01000032H   CODE     ---       Timer4_IRQHandler
      02000012H   XDATA    WORD      timer_1ms_count
*DEL*:00000000H   CODE     ---       Timer_Accuracy_Test
      02000018H   XDATA    WORD      timer_test_counter
      00000020H.5 BIT      BIT       timer_test_enable
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01002011H   CODE     ---       TMR0_Config
      0100203BH   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      01001D4AH   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01001DA5H   CODE     ---       UART_0_Config
      01001ED1H   CODE     ---       UART_1_Config
      0100213FH   CODE     ---       UART_Data_Init
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 20


      010015A2H   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      010022C9H   CODE     ---       UART_EnableBRT
      02000059H   XDATA    ---       UART_Get_String
      00000021H.4 BIT      BIT       use_precise_timer
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000038H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001991H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      0100198EH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      0100199CH   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      0100198EH   LINE      CODE     ---       #133
      01001990H   LINE      CODE     ---       #134
      01001991H   LINE      CODE     ---       #135
      01001992H   LINE      CODE     ---       #136
      01001994H   LINE      CODE     ---       #140
      01001997H   LINE      CODE     ---       #141
      01001999H   LINE      CODE     ---       #145
      0100199BH   LINE      CODE     ---       #147
      0100199CH   LINE      CODE     ---       #148
      0100199DH   LINE      CODE     ---       #149
      0100199EH   LINE      CODE     ---       #150
      010019A0H   LINE      CODE     ---       #151
      010019A2H   LINE      CODE     ---       #185
      010019A5H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      010022A5H   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      010020B1H   PUBLIC    CODE     ---       ADC_GetADCResult
      010021DCH   PUBLIC    CODE     ---       _ADC_EnableChannel
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 21


      01002231H   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 22


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 23


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      01002231H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      01002231H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002231H   LINE      CODE     ---       #88
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 24


      01002231H   LINE      CODE     ---       #89
      01002231H   LINE      CODE     ---       #90
      01002231H   LINE      CODE     ---       #92
      01002233H   LINE      CODE     ---       #93
      01002236H   LINE      CODE     ---       #94
      01002237H   LINE      CODE     ---       #95
      01002239H   LINE      CODE     ---       #97
      0100223BH   LINE      CODE     ---       #98
      0100223FH   LINE      CODE     ---       #99
      01002244H   LINE      CODE     ---       #100
      01002246H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      010021DCH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      010021DCH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021DCH   LINE      CODE     ---       #154
      010021DCH   LINE      CODE     ---       #155
      010021DCH   LINE      CODE     ---       #156
      010021DCH   LINE      CODE     ---       #158
      010021DEH   LINE      CODE     ---       #159
      010021E2H   LINE      CODE     ---       #160
      010021EBH   LINE      CODE     ---       #161
      010021EDH   LINE      CODE     ---       #163
      010021EFH   LINE      CODE     ---       #164
      010021F3H   LINE      CODE     ---       #165
      010021F7H   LINE      CODE     ---       #166
      010021F9H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      010020B1H   BLOCK     CODE     ---       LVL=0
      010020B1H   LINE      CODE     ---       #258
      010020B1H   LINE      CODE     ---       #259
      010020B1H   LINE      CODE     ---       #260
      010020B8H   LINE      CODE     ---       #261
      010020B8H   LINE      CODE     ---       #262
      010020CBH   LINE      CODE     ---       #263
      010020CBH   LINE      CODE     ---       #264
      010020D5H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      010022A5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      010022A5H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022A5H   LINE      CODE     ---       #344
      010022A5H   LINE      CODE     ---       #345
      010022A5H   LINE      CODE     ---       #346
      010022A5H   LINE      CODE     ---       #348
      010022A9H   LINE      CODE     ---       #349
      010022ABH   LINE      CODE     ---       #350
      010022ACH   LINE      CODE     ---       #351
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 25


      010022ADH   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 26


      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 27


      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 28


      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 29


      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 30


      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 31


      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 32


      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 33


      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 34


      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #358
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000057H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005BH   LINE      CODE     ---       #363
      0100005CH   LINE      CODE     ---       #364
      0100005DH   LINE      CODE     ---       #365
      0100005EH   LINE      CODE     ---       #366
      0100005FH   LINE      CODE     ---       #367
      01000060H   LINE      CODE     ---       #368
      01000061H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      0100219EH   PUBLIC    CODE     ---       _TMR_Stop
      0100217FH   PUBLIC    CODE     ---       _TMR_Start
      010021FAH   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      01002064H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      01001E40H   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01001DF4H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 35


      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 36


      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 37


      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DF4H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01001DF4H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DF4H   LINE      CODE     ---       #74
      01001DF4H   LINE      CODE     ---       #75
      01001DF4H   LINE      CODE     ---       #76
      01001DF4H   LINE      CODE     ---       #78
      01001E03H   LINE      CODE     ---       #79
      01001E03H   LINE      CODE     ---       #80
      01001E03H   LINE      CODE     ---       #81
      01001E05H   LINE      CODE     ---       #82
      01001E09H   LINE      CODE     ---       #83
      01001E0FH   LINE      CODE     ---       #84
      01001E0FH   LINE      CODE     ---       #85
      01001E11H   LINE      CODE     ---       #86
      01001E11H   LINE      CODE     ---       #87
      01001E13H   LINE      CODE     ---       #88
      01001E17H   LINE      CODE     ---       #89
      01001E24H   LINE      CODE     ---       #90
      01001E26H   LINE      CODE     ---       #91
      01001E27H   LINE      CODE     ---       #92
      01001E27H   LINE      CODE     ---       #93
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 38


      01001E29H   LINE      CODE     ---       #94
      01001E2CH   LINE      CODE     ---       #95
      01001E2DH   LINE      CODE     ---       #96
      01001E2FH   LINE      CODE     ---       #97
      01001E30H   LINE      CODE     ---       #98
      01001E30H   LINE      CODE     ---       #99
      01001E32H   LINE      CODE     ---       #100
      01001E36H   LINE      CODE     ---       #101
      01001E3DH   LINE      CODE     ---       #102
      01001E3FH   LINE      CODE     ---       #103
      01001E3FH   LINE      CODE     ---       #104
      01001E3FH   LINE      CODE     ---       #105
      01001E3FH   LINE      CODE     ---       #106
      01001E3FH   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      01001E40H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      01001E40H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001E40H   LINE      CODE     ---       #117
      01001E40H   LINE      CODE     ---       #118
      01001E40H   LINE      CODE     ---       #119
      01001E40H   LINE      CODE     ---       #121
      01001E4FH   LINE      CODE     ---       #122
      01001E4FH   LINE      CODE     ---       #123
      01001E4FH   LINE      CODE     ---       #124
      01001E51H   LINE      CODE     ---       #125
      01001E55H   LINE      CODE     ---       #126
      01001E5BH   LINE      CODE     ---       #127
      01001E5BH   LINE      CODE     ---       #128
      01001E5DH   LINE      CODE     ---       #129
      01001E5DH   LINE      CODE     ---       #130
      01001E5FH   LINE      CODE     ---       #131
      01001E63H   LINE      CODE     ---       #132
      01001E68H   LINE      CODE     ---       #133
      01001E6AH   LINE      CODE     ---       #134
      01001E6BH   LINE      CODE     ---       #135
      01001E6BH   LINE      CODE     ---       #136
      01001E6DH   LINE      CODE     ---       #137
      01001E71H   LINE      CODE     ---       #138
      01001E76H   LINE      CODE     ---       #139
      01001E76H   LINE      CODE     ---       #140
      01001E78H   LINE      CODE     ---       #141
      01001E78H   LINE      CODE     ---       #142
      01001E7AH   LINE      CODE     ---       #143
      01001E7EH   LINE      CODE     ---       #144
      01001E87H   LINE      CODE     ---       #145
      01001E89H   LINE      CODE     ---       #146
      01001E89H   LINE      CODE     ---       #147
      01001E89H   LINE      CODE     ---       #148
      01001E89H   LINE      CODE     ---       #149
      01001E89H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01002064H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      01002064H   LINE      CODE     ---       #160
      01002064H   LINE      CODE     ---       #161
      01002064H   LINE      CODE     ---       #162
      01002073H   LINE      CODE     ---       #163
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 39


      01002073H   LINE      CODE     ---       #164
      01002073H   LINE      CODE     ---       #165
      01002075H   LINE      CODE     ---       #166
      01002077H   LINE      CODE     ---       #167
      01002078H   LINE      CODE     ---       #168
      01002078H   LINE      CODE     ---       #169
      0100207AH   LINE      CODE     ---       #170
      0100207CH   LINE      CODE     ---       #171
      0100207DH   LINE      CODE     ---       #172
      0100207DH   LINE      CODE     ---       #173
      0100207FH   LINE      CODE     ---       #174
      01002081H   LINE      CODE     ---       #175
      01002082H   LINE      CODE     ---       #176
      01002082H   LINE      CODE     ---       #177
      01002086H   LINE      CODE     ---       #178
      0100208AH   LINE      CODE     ---       #179
      0100208AH   LINE      CODE     ---       #180
      0100208AH   LINE      CODE     ---       #181
      0100208AH   LINE      CODE     ---       #182
      0100208AH   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010021FAH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010021FAH   LINE      CODE     ---       #256
      010021FAH   LINE      CODE     ---       #257
      010021FAH   LINE      CODE     ---       #258
      01002209H   LINE      CODE     ---       #259
      01002209H   LINE      CODE     ---       #260
      01002209H   LINE      CODE     ---       #261
      0100220BH   LINE      CODE     ---       #262
      0100220CH   LINE      CODE     ---       #263
      0100220CH   LINE      CODE     ---       #264
      0100220EH   LINE      CODE     ---       #265
      0100220FH   LINE      CODE     ---       #266
      0100220FH   LINE      CODE     ---       #267
      01002212H   LINE      CODE     ---       #268
      01002213H   LINE      CODE     ---       #269
      01002213H   LINE      CODE     ---       #270
      01002216H   LINE      CODE     ---       #271
      01002216H   LINE      CODE     ---       #272
      01002216H   LINE      CODE     ---       #273
      01002216H   LINE      CODE     ---       #274
      01002216H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      0100217FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      0100217FH   LINE      CODE     ---       #368
      0100217FH   LINE      CODE     ---       #369
      0100217FH   LINE      CODE     ---       #370
      0100218EH   LINE      CODE     ---       #371
      0100218EH   LINE      CODE     ---       #372
      0100218EH   LINE      CODE     ---       #373
      01002191H   LINE      CODE     ---       #374
      01002192H   LINE      CODE     ---       #375
      01002192H   LINE      CODE     ---       #376
      01002195H   LINE      CODE     ---       #377
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 40


      01002196H   LINE      CODE     ---       #378
      01002196H   LINE      CODE     ---       #379
      01002199H   LINE      CODE     ---       #380
      0100219AH   LINE      CODE     ---       #381
      0100219AH   LINE      CODE     ---       #382
      0100219DH   LINE      CODE     ---       #383
      0100219DH   LINE      CODE     ---       #384
      0100219DH   LINE      CODE     ---       #385
      0100219DH   LINE      CODE     ---       #386
      0100219DH   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      0100219EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      0100219EH   LINE      CODE     ---       #395
      0100219EH   LINE      CODE     ---       #396
      0100219EH   LINE      CODE     ---       #397
      010021ADH   LINE      CODE     ---       #398
      010021ADH   LINE      CODE     ---       #399
      010021ADH   LINE      CODE     ---       #400
      010021B0H   LINE      CODE     ---       #401
      010021B1H   LINE      CODE     ---       #402
      010021B1H   LINE      CODE     ---       #403
      010021B4H   LINE      CODE     ---       #404
      010021B5H   LINE      CODE     ---       #405
      010021B5H   LINE      CODE     ---       #406
      010021B8H   LINE      CODE     ---       #407
      010021B9H   LINE      CODE     ---       #408
      010021B9H   LINE      CODE     ---       #409
      010021BCH   LINE      CODE     ---       #410
      010021BCH   LINE      CODE     ---       #411
      010021BCH   LINE      CODE     ---       #412
      010021BCH   LINE      CODE     ---       #413
      010021BCH   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 41


      ---         MODULE    ---      ---       UART
      010022B7H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      010022AEH   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      010022C9H   PUBLIC    CODE     ---       UART_EnableBRT
      01002247H   PUBLIC    CODE     ---       _UART_GetBuff
      010021BDH   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      010020D6H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      01002294H   PUBLIC    CODE     ---       _UART_EnableInt
      0100226EH   PUBLIC    CODE     ---       _UART_EnableReceive
      0100225BH   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      01001C8EH   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 42


      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 43


      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C8EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      01001C8EH   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 44


      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001C8EH   LINE      CODE     ---       #70
      01001C8EH   LINE      CODE     ---       #71
      01001C8EH   LINE      CODE     ---       #72
      01001C8EH   LINE      CODE     ---       #74
      01001C91H   LINE      CODE     ---       #75
      01001C91H   LINE      CODE     ---       #76
      01001C93H   LINE      CODE     ---       #77
      01001C97H   LINE      CODE     ---       #78
      01001C9EH   LINE      CODE     ---       #79
      01001CA0H   LINE      CODE     ---       #81
      01001CA3H   LINE      CODE     ---       #82
      01001CAFH   LINE      CODE     ---       #83
      01001CAFH   LINE      CODE     ---       #84
      01001CAFH   LINE      CODE     ---       #85
      01001CAFH   LINE      CODE     ---       #86
      01001CAFH   LINE      CODE     ---       #87
      01001CB2H   LINE      CODE     ---       #88
      01001CB4H   LINE      CODE     ---       #89
      01001CB4H   LINE      CODE     ---       #90
      01001CB7H   LINE      CODE     ---       #91
      01001CB9H   LINE      CODE     ---       #92
      01001CB9H   LINE      CODE     ---       #93
      01001CBCH   LINE      CODE     ---       #94
      01001CBCH   LINE      CODE     ---       #95
      01001CBCH   LINE      CODE     ---       #96
      01001CBCH   LINE      CODE     ---       #97
      01001CBCH   LINE      CODE     ---       #99
      01001CBCH   LINE      CODE     ---       #100
      01001CC1H   LINE      CODE     ---       #101
      01001CC1H   LINE      CODE     ---       #102
      01001CC3H   LINE      CODE     ---       #103
      01001CC7H   LINE      CODE     ---       #104
      01001CD0H   LINE      CODE     ---       #105
      01001CD2H   LINE      CODE     ---       #107
      01001CD5H   LINE      CODE     ---       #108
      01001CE1H   LINE      CODE     ---       #109
      01001CE1H   LINE      CODE     ---       #110
      01001CE1H   LINE      CODE     ---       #111
      01001CE1H   LINE      CODE     ---       #112
      01001CE1H   LINE      CODE     ---       #113
      01001CE4H   LINE      CODE     ---       #114
      01001CE5H   LINE      CODE     ---       #115
      01001CE5H   LINE      CODE     ---       #116
      01001CE8H   LINE      CODE     ---       #117
      01001CE9H   LINE      CODE     ---       #118
      01001CE9H   LINE      CODE     ---       #119
      01001CECH   LINE      CODE     ---       #120
      01001CECH   LINE      CODE     ---       #121
      01001CECH   LINE      CODE     ---       #122
      01001CECH   LINE      CODE     ---       #123
      01001CECH   LINE      CODE     ---       #124
      01001CECH   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      0100225BH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100225BH   LINE      CODE     ---       #133
      0100225BH   LINE      CODE     ---       #134
      0100225BH   LINE      CODE     ---       #135
      01002261H   LINE      CODE     ---       #136
      01002261H   LINE      CODE     ---       #137
      01002264H   LINE      CODE     ---       #138
      01002264H   LINE      CODE     ---       #139
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 45


      0100226AH   LINE      CODE     ---       #140
      0100226AH   LINE      CODE     ---       #141
      0100226DH   LINE      CODE     ---       #142
      0100226DH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100226EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100226EH   LINE      CODE     ---       #280
      0100226EH   LINE      CODE     ---       #281
      0100226EH   LINE      CODE     ---       #282
      01002274H   LINE      CODE     ---       #283
      01002274H   LINE      CODE     ---       #284
      01002277H   LINE      CODE     ---       #285
      01002277H   LINE      CODE     ---       #286
      0100227DH   LINE      CODE     ---       #287
      0100227DH   LINE      CODE     ---       #288
      01002280H   LINE      CODE     ---       #289
      01002280H   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002294H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002294H   LINE      CODE     ---       #317
      01002294H   LINE      CODE     ---       #318
      01002294H   LINE      CODE     ---       #319
      0100229AH   LINE      CODE     ---       #320
      0100229AH   LINE      CODE     ---       #321
      0100229CH   LINE      CODE     ---       #322
      0100229CH   LINE      CODE     ---       #323
      010022A2H   LINE      CODE     ---       #324
      010022A2H   LINE      CODE     ---       #325
      010022A4H   LINE      CODE     ---       #326
      010022A4H   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010020D6H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      010020D6H   LINE      CODE     ---       #353
      010020D8H   LINE      CODE     ---       #354
      010020D8H   LINE      CODE     ---       #355
      010020DEH   LINE      CODE     ---       #356
      010020DEH   LINE      CODE     ---       #357
      010020E8H   LINE      CODE     ---       #358
      010020E8H   LINE      CODE     ---       #359
      010020EEH   LINE      CODE     ---       #360
      010020EEH   LINE      CODE     ---       #361
      010020F8H   LINE      CODE     ---       #362
      010020F8H   LINE      CODE     ---       #363
      010020FAH   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      010021BDH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010021BDH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021BDH   LINE      CODE     ---       #373
      010021BDH   LINE      CODE     ---       #374
      010021BDH   LINE      CODE     ---       #377
      010021C3H   LINE      CODE     ---       #378
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 46


      010021C3H   LINE      CODE     ---       #379
      010021C5H   LINE      CODE     ---       #380
      010021C8H   LINE      CODE     ---       #381
      010021CCH   LINE      CODE     ---       #382
      010021CCH   LINE      CODE     ---       #383
      010021D2H   LINE      CODE     ---       #384
      010021D2H   LINE      CODE     ---       #385
      010021D4H   LINE      CODE     ---       #386
      010021D7H   LINE      CODE     ---       #387
      010021DBH   LINE      CODE     ---       #388
      010021DBH   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      01002247H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002247H   LINE      CODE     ---       #443
      01002247H   LINE      CODE     ---       #444
      01002247H   LINE      CODE     ---       #445
      0100224DH   LINE      CODE     ---       #446
      0100224DH   LINE      CODE     ---       #447
      01002250H   LINE      CODE     ---       #448
      01002250H   LINE      CODE     ---       #449
      01002258H   LINE      CODE     ---       #450
      01002258H   LINE      CODE     ---       #451
      0100225AH   LINE      CODE     ---       #452
      0100225AH   LINE      CODE     ---       #453
      0100225AH   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010022C9H   BLOCK     CODE     ---       LVL=0
      010022C9H   LINE      CODE     ---       #534
      010022C9H   LINE      CODE     ---       #535
      010022C9H   LINE      CODE     ---       #536
      010022D0H   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      010022AEH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      010022AEH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022AEH   LINE      CODE     ---       #544
      010022AEH   LINE      CODE     ---       #545
      010022AEH   LINE      CODE     ---       #546
      010022AEH   LINE      CODE     ---       #548
      010022B2H   LINE      CODE     ---       #549
      010022B4H   LINE      CODE     ---       #550
      010022B5H   LINE      CODE     ---       #551
      010022B6H   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      010022B7H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      010022B7H   LINE      CODE     ---       #560
      010022B7H   LINE      CODE     ---       #561
      010022B7H   LINE      CODE     ---       #562
      010022BCH   LINE      CODE     ---       #563
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 47


      010022BFH   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 48


      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 49


      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01001FB3H   PUBLIC    CODE     ---       _FLASH_Erase
      01001F7FH   PUBLIC    CODE     ---       _FLASH_Read
      01001F4AH   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 50


      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 51


      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 52


      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      01001F4AH   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      01001F4AH   LINE      CODE     ---       #95
      01001F4CH   LINE      CODE     ---       #96
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 53


      01001F4CH   LINE      CODE     ---       #97
      01001F50H   LINE      CODE     ---       #98
      01001F52H   LINE      CODE     ---       #99
      01001F55H   LINE      CODE     ---       #101
      01001F58H   LINE      CODE     ---       #102
      01001F58H   LINE      CODE     ---       #103
      01001F5AH   LINE      CODE     ---       #104
      01001F5BH   LINE      CODE     ---       #105
      01001F60H   LINE      CODE     ---       #106
      01001F61H   LINE      CODE     ---       #107
      01001F62H   LINE      CODE     ---       #108
      01001F63H   LINE      CODE     ---       #109
      01001F64H   LINE      CODE     ---       #110
      01001F65H   LINE      CODE     ---       #111
      01001F66H   LINE      CODE     ---       #112
      01001F6BH   LINE      CODE     ---       #113
      01001F6DH   LINE      CODE     ---       #114
      01001F6EH   LINE      CODE     ---       #116
      01001F6EH   LINE      CODE     ---       #117
      01001F73H   LINE      CODE     ---       #118
      01001F74H   LINE      CODE     ---       #119
      01001F75H   LINE      CODE     ---       #120
      01001F76H   LINE      CODE     ---       #121
      01001F77H   LINE      CODE     ---       #122
      01001F78H   LINE      CODE     ---       #123
      01001F79H   LINE      CODE     ---       #124
      01001F7EH   LINE      CODE     ---       #125
      01001F7EH   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      01001F7FH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F7FH   LINE      CODE     ---       #138
      01001F81H   LINE      CODE     ---       #139
      01001F81H   LINE      CODE     ---       #140
      01001F83H   LINE      CODE     ---       #141
      01001F86H   LINE      CODE     ---       #142
      01001F89H   LINE      CODE     ---       #143
      01001F89H   LINE      CODE     ---       #144
      01001F8BH   LINE      CODE     ---       #145
      01001F8CH   LINE      CODE     ---       #146
      01001F91H   LINE      CODE     ---       #147
      01001F92H   LINE      CODE     ---       #148
      01001F93H   LINE      CODE     ---       #149
      01001F94H   LINE      CODE     ---       #150
      01001F95H   LINE      CODE     ---       #151
      01001F96H   LINE      CODE     ---       #152
      01001F97H   LINE      CODE     ---       #153
      01001F9CH   LINE      CODE     ---       #154
      01001F9EH   LINE      CODE     ---       #155
      01001FA0H   LINE      CODE     ---       #157
      01001FA0H   LINE      CODE     ---       #158
      01001FA5H   LINE      CODE     ---       #159
      01001FA6H   LINE      CODE     ---       #160
      01001FA7H   LINE      CODE     ---       #161
      01001FA8H   LINE      CODE     ---       #162
      01001FA9H   LINE      CODE     ---       #163
      01001FAAH   LINE      CODE     ---       #164
      01001FABH   LINE      CODE     ---       #165
      01001FB0H   LINE      CODE     ---       #166
      01001FB0H   LINE      CODE     ---       #167
      01001FB2H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 54


      01001FB3H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001FB3H   LINE      CODE     ---       #179
      01001FB5H   LINE      CODE     ---       #180
      01001FB5H   LINE      CODE     ---       #181
      01001FB7H   LINE      CODE     ---       #182
      01001FBAH   LINE      CODE     ---       #183
      01001FBDH   LINE      CODE     ---       #184
      01001FBDH   LINE      CODE     ---       #185
      01001FBFH   LINE      CODE     ---       #186
      01001FC0H   LINE      CODE     ---       #187
      01001FC5H   LINE      CODE     ---       #188
      01001FC6H   LINE      CODE     ---       #189
      01001FC7H   LINE      CODE     ---       #190
      01001FC8H   LINE      CODE     ---       #191
      01001FC9H   LINE      CODE     ---       #192
      01001FCAH   LINE      CODE     ---       #193
      01001FCBH   LINE      CODE     ---       #194
      01001FD0H   LINE      CODE     ---       #195
      01001FD2H   LINE      CODE     ---       #196
      01001FD3H   LINE      CODE     ---       #198
      01001FD3H   LINE      CODE     ---       #199
      01001FD8H   LINE      CODE     ---       #200
      01001FD9H   LINE      CODE     ---       #201
      01001FDAH   LINE      CODE     ---       #202
      01001FDBH   LINE      CODE     ---       #203
      01001FDCH   LINE      CODE     ---       #204
      01001FDDH   LINE      CODE     ---       #205
      01001FDEH   LINE      CODE     ---       #206
      01001FE3H   LINE      CODE     ---       #207
      01001FE3H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      01002217H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 55


      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 56


      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 57


      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002217H   BLOCK     CODE     ---       LVL=0
      01002217H   LINE      CODE     ---       #65
      01002217H   LINE      CODE     ---       #66
      01002217H   LINE      CODE     ---       #68
      0100221EH   LINE      CODE     ---       #71
      01002223H   LINE      CODE     ---       #72
      01002229H   LINE      CODE     ---       #75
      0100222EH   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      0200008EH   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      0200008CH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      0200008AH   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000025H.6 PUBLIC    BIT      BIT       Delay_Over
      02000089H   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      02000087H   PUBLIC    XDATA    WORD      Delay_Time
      00000025H.5 PUBLIC    BIT      BIT       Get_String_Buff
      02000085H   PUBLIC    XDATA    INT       Count_Toggle
      02000083H   PUBLIC    XDATA    WORD      Delay_Time_Count
      02000081H   PUBLIC    XDATA    WORD      Motor_Speed_Data
      0200007FH   PUBLIC    XDATA    INT       Num
      0200007EH   PUBLIC    XDATA    BYTE      K3_cnt
      0200007DH   PUBLIC    XDATA    BYTE      K2_cnt
      0200007CH   PUBLIC    XDATA    BYTE      K1_cnt
      0200007AH   PUBLIC    XDATA    WORD      longhit_cnt
      00000025H.4 PUBLIC    BIT      BIT       Center_Line_Control
      00000025H.3 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 58


      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 59


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 60


      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      01002281H   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01001C1EH   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 61


      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 62


      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 63


      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C1EH   BLOCK     CODE     ---       LVL=0
      01001C1EH   LINE      CODE     ---       #42
      01001C1EH   LINE      CODE     ---       #43
      01001C1EH   LINE      CODE     ---       #44
      01001C21H   LINE      CODE     ---       #45
      01001C24H   LINE      CODE     ---       #46
      01001C27H   LINE      CODE     ---       #47
      01001C2AH   LINE      CODE     ---       #48
      01001C2DH   LINE      CODE     ---       #49
      01001C30H   LINE      CODE     ---       #50
      01001C33H   LINE      CODE     ---       #51
      01001C36H   LINE      CODE     ---       #77
      01001C3BH   LINE      CODE     ---       #78
      01001C3EH   LINE      CODE     ---       #79
      01001C45H   LINE      CODE     ---       #81
      01001C4AH   LINE      CODE     ---       #82
      01001C4DH   LINE      CODE     ---       #83
      01001C54H   LINE      CODE     ---       #85
      01001C59H   LINE      CODE     ---       #86
      01001C5CH   LINE      CODE     ---       #87
      01001C63H   LINE      CODE     ---       #90
      01001C68H   LINE      CODE     ---       #91
      01001C6BH   LINE      CODE     ---       #92
      01001C72H   LINE      CODE     ---       #94
      01001C77H   LINE      CODE     ---       #95
      01001C7AH   LINE      CODE     ---       #96
      01001C81H   LINE      CODE     ---       #100
      01001C86H   LINE      CODE     ---       #101
      01001C89H   LINE      CODE     ---       #102
      01001C8BH   LINE      CODE     ---       #105
      01001C8DH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      01002281H   BLOCK     CODE     ---       LVL=0
      01002281H   LINE      CODE     ---       #131
      01002281H   LINE      CODE     ---       #132
      01002281H   LINE      CODE     ---       #134
      01002287H   LINE      CODE     ---       #135
      0100228AH   LINE      CODE     ---       #138
      0100228EH   LINE      CODE     ---       #139
      01002291H   LINE      CODE     ---       #142
      01002293H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      0100203BH   PUBLIC    CODE     ---       TMR1_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 64


      01002011H   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 65


      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 66


      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002011H   BLOCK     CODE     ---       LVL=0
      01002011H   LINE      CODE     ---       #11
      01002011H   LINE      CODE     ---       #12
      01002011H   LINE      CODE     ---       #16
      01002019H   LINE      CODE     ---       #20
      0100201FH   LINE      CODE     ---       #24
      01002028H   LINE      CODE     ---       #29
      0100202DH   LINE      CODE     ---       #34
      01002033H   LINE      CODE     ---       #35
      01002036H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      0100203BH   BLOCK     CODE     ---       LVL=0
      0100203BH   LINE      CODE     ---       #50
      0100203BH   LINE      CODE     ---       #51
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 67


      0100203BH   LINE      CODE     ---       #55
      01002044H   LINE      CODE     ---       #59
      0100204BH   LINE      CODE     ---       #63
      01002054H   LINE      CODE     ---       #68
      01002059H   LINE      CODE     ---       #73
      0100205CH   LINE      CODE     ---       #74
      0100205FH   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      02000054H   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01001CEDH   PUBLIC    CODE     ---       _UART_Send_String
      01001ED1H   PUBLIC    CODE     ---       UART_1_Config
      01001DA5H   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 68


      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 69


      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DA5H   BLOCK     CODE     ---       LVL=0
      01001DA5H   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 70


      02000046H   SYMBOL    XDATA    WORD      BRTValue
      02000048H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001DA5H   LINE      CODE     ---       #42
      01001DA5H   LINE      CODE     ---       #43
      01001DA5H   LINE      CODE     ---       #44
      01001DAFH   LINE      CODE     ---       #45
      01001DB7H   LINE      CODE     ---       #143
      01001DC0H   LINE      CODE     ---       #144
      01001DC5H   LINE      CODE     ---       #147
      01001DCAH   LINE      CODE     ---       #148
      01001DCFH   LINE      CODE     ---       #152
      01001DDAH   LINE      CODE     ---       #153
      01001DDDH   LINE      CODE     ---       #156
      01001DE3H   LINE      CODE     ---       #157
      01001DE8H   LINE      CODE     ---       #159
      01001DEDH   LINE      CODE     ---       #160
      01001DF0H   LINE      CODE     ---       #161
      01001DF3H   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01001ED1H   BLOCK     CODE     ---       LVL=0
      01001ED1H   BLOCK     CODE     NEAR LAB  LVL=1
      02000046H   SYMBOL    XDATA    WORD      BRTValue
      02000048H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001ED1H   LINE      CODE     ---       #223
      01001ED1H   LINE      CODE     ---       #224
      01001ED1H   LINE      CODE     ---       #225
      01001EDBH   LINE      CODE     ---       #226
      01001EE3H   LINE      CODE     ---       #324
      01001EECH   LINE      CODE     ---       #325
      01001EF1H   LINE      CODE     ---       #328
      01001EF6H   LINE      CODE     ---       #329
      01001EFBH   LINE      CODE     ---       #333
      01001F06H   LINE      CODE     ---       #334
      01001F09H   LINE      CODE     ---       #337
      01001F0FH   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      00000001H   SYMBOL    DATA     ---       s

      01001CEDH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000055H   SYMBOL    XDATA    ---       String
      02000058H   SYMBOL    XDATA    BYTE      Length
      01001CF8H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01001CEDH   LINE      CODE     ---       #408
      01001CF8H   LINE      CODE     ---       #409
      01001CF8H   LINE      CODE     ---       #410
      01001CFAH   LINE      CODE     ---       #411
      01001D04H   LINE      CODE     ---       #412
      01001D04H   LINE      CODE     ---       #413
      01001D07H   LINE      CODE     ---       #414
      01001D07H   LINE      CODE     ---       #415
      01001D1CH   LINE      CODE     ---       #416
      01001D21H   LINE      CODE     ---       #417
      01001D24H   LINE      CODE     ---       #418
      01001D24H   LINE      CODE     ---       #419
      01001D29H   LINE      CODE     ---       #420
      01001D29H   LINE      CODE     ---       #421
      01001D3EH   LINE      CODE     ---       #422
      01001D43H   LINE      CODE     ---       #423
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 71


      01001D46H   LINE      CODE     ---       #424
      01001D46H   LINE      CODE     ---       #425
      01001D49H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ISR
      020000A2H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      020000A0H   PUBLIC    XDATA    INT       Num_Forward_Pulse
      0100003AH   PUBLIC    CODE     ---       SPI_IRQHandler
      01000039H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000038H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000037H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000036H   PUBLIC    CODE     ---       EPWM_IRQHandler
      01000032H   PUBLIC    CODE     ---       Timer4_IRQHandler
      01000031H   PUBLIC    CODE     ---       Timer3_IRQHandler
      01000030H   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002FH   PUBLIC    CODE     ---       LSE_IRQHandler
      0100002EH   PUBLIC    CODE     ---       LVD_IRQHandler
      0100002AH   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001B33H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001ABBH   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000029H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      01001D4AH   PUBLIC    CODE     ---       UART0_IRQHandler
      010020FBH   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01001322H   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 72


      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 73


      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 74


      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #74
      0100000AH   LINE      CODE     ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      01001322H   BLOCK     CODE     ---       LVL=0
      01001322H   LINE      CODE     ---       #86
      01001331H   LINE      CODE     ---       #88
      01001334H   LINE      CODE     ---       #89
      01001337H   LINE      CODE     ---       #91
      01001339H   LINE      CODE     ---       #92
      01001347H   LINE      CODE     ---       #93
      01001355H   LINE      CODE     ---       #94
      0100136AH   LINE      CODE     ---       #96
      0100138AH   LINE      CODE     ---       #97
      0100138AH   LINE      CODE     ---       #98
      0100138AH   LINE      CODE     ---       #99
      0100138AH   LINE      CODE     ---       #100
      01001396H   LINE      CODE     ---       #101
      01001396H   LINE      CODE     ---       #102
      01001396H   LINE      CODE     ---       #103
      01001396H   LINE      CODE     ---       #104
      01001396H   LINE      CODE     ---       #105
      01001396H   LINE      CODE     ---       #106
      01001398H   LINE      CODE     ---       #107
      01001398H   LINE      CODE     ---       #108
      010013A4H   LINE      CODE     ---       #109
      010013A4H   LINE      CODE     ---       #110
      010013A4H   LINE      CODE     ---       #111
      010013A4H   LINE      CODE     ---       #112
      010013A4H   LINE      CODE     ---       #113
      010013A6H   LINE      CODE     ---       #114
      010013A6H   LINE      CODE     ---       #115
      010013A6H   LINE      CODE     ---       #116
      010013B2H   LINE      CODE     ---       #117
      010013B2H   LINE      CODE     ---       #118
      010013B2H   LINE      CODE     ---       #119
      010013B2H   LINE      CODE     ---       #120
      010013B2H   LINE      CODE     ---       #121
      010013B2H   LINE      CODE     ---       #122
      010013B4H   LINE      CODE     ---       #123
      010013B4H   LINE      CODE     ---       #124
      010013B4H   LINE      CODE     ---       #125
      010013C0H   LINE      CODE     ---       #126
      010013C0H   LINE      CODE     ---       #127
      010013C0H   LINE      CODE     ---       #128
      010013C0H   LINE      CODE     ---       #129
      010013C0H   LINE      CODE     ---       #130
      010013C0H   LINE      CODE     ---       #131
      010013C2H   LINE      CODE     ---       #132
      010013C2H   LINE      CODE     ---       #133
      010013C2H   LINE      CODE     ---       #134
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 75


      010013CEH   LINE      CODE     ---       #135
      010013CEH   LINE      CODE     ---       #136
      010013D0H   LINE      CODE     ---       #137
      010013D6H   LINE      CODE     ---       #138
      010013D6H   LINE      CODE     ---       #139
      010013D6H   LINE      CODE     ---       #140
      010013D6H   LINE      CODE     ---       #141
      010013D6H   LINE      CODE     ---       #142
      010013D6H   LINE      CODE     ---       #143
      010013D6H   LINE      CODE     ---       #145
      010013D9H   LINE      CODE     ---       #146
      010013D9H   LINE      CODE     ---       #147
      010013E2H   LINE      CODE     ---       #148
      010013E4H   LINE      CODE     ---       #150
      010013E4H   LINE      CODE     ---       #151
      010013E9H   LINE      CODE     ---       #152
      010013E9H   LINE      CODE     ---       #154
      010013F6H   LINE      CODE     ---       #155
      010013F6H   LINE      CODE     ---       #156
      010013F8H   LINE      CODE     ---       #157
      01001406H   LINE      CODE     ---       #158
      01001408H   LINE      CODE     ---       #160
      01001408H   LINE      CODE     ---       #161
      0100140AH   LINE      CODE     ---       #162
      01001411H   LINE      CODE     ---       #163
      01001411H   LINE      CODE     ---       #165
      01001414H   LINE      CODE     ---       #166
      01001414H   LINE      CODE     ---       #167
      01001422H   LINE      CODE     ---       #168
      01001437H   LINE      CODE     ---       #169
      01001437H   LINE      CODE     ---       #170
      01001439H   LINE      CODE     ---       #171
      01001439H   LINE      CODE     ---       #172
      01001439H   LINE      CODE     ---       #173
      0100143BH   LINE      CODE     ---       #175
      0100143BH   LINE      CODE     ---       #176
      01001442H   LINE      CODE     ---       #177
      01001442H   LINE      CODE     ---       #180
      01001445H   LINE      CODE     ---       #181
      01001445H   LINE      CODE     ---       #182
      01001453H   LINE      CODE     ---       #183
      01001462H   LINE      CODE     ---       #184
      01001462H   LINE      CODE     ---       #185
      01001466H   LINE      CODE     ---       #186
      0100146BH   LINE      CODE     ---       #187
      0100146BH   LINE      CODE     ---       #188
      0100146BH   LINE      CODE     ---       #189
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #198
      01000012H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      010020FBH   BLOCK     CODE     ---       LVL=0
      010020FBH   LINE      CODE     ---       #210
      01002101H   LINE      CODE     ---       #213
      01002104H   LINE      CODE     ---       #214
      01002107H   LINE      CODE     ---       #217
      0100210AH   LINE      CODE     ---       #218
      0100210AH   LINE      CODE     ---       #219
      01002118H   LINE      CODE     ---       #220
      01002118H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 76


      01001D4AH   BLOCK     CODE     ---       LVL=0
      01001D4AH   LINE      CODE     ---       #230
      01001D67H   LINE      CODE     ---       #232
      01001D6FH   LINE      CODE     ---       #233
      01001D6FH   LINE      CODE     ---       #234
      01001D71H   LINE      CODE     ---       #235
      01001D76H   LINE      CODE     ---       #236
      01001D85H   LINE      CODE     ---       #237
      01001D8AH   LINE      CODE     ---       #238
      01001D8AH   LINE      CODE     ---       #239
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #248
      0100001AH   LINE      CODE     ---       #251
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #260
      01000022H   LINE      CODE     ---       #263
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #272
      01000029H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0

      01001ABBH   BLOCK     CODE     ---       LVL=0
      01001ABBH   LINE      CODE     ---       #284
      01001ACAH   LINE      CODE     ---       #287
      01001ACDH   LINE      CODE     ---       #290
      01001AD0H   LINE      CODE     ---       #291
      01001AD0H   LINE      CODE     ---       #293
      01001AD3H   LINE      CODE     ---       #294
      01001AD3H   LINE      CODE     ---       #296
      01001AE2H   LINE      CODE     ---       #298
      01001AE4H   LINE      CODE     ---       #300
      01001AE6H   LINE      CODE     ---       #301
      01001AE6H   LINE      CODE     ---       #302
      01001AE8H   LINE      CODE     ---       #304
      01001AE8H   LINE      CODE     ---       #306
      01001AEBH   LINE      CODE     ---       #307
      01001AEBH   LINE      CODE     ---       #309
      01001B06H   LINE      CODE     ---       #311
      01001B22H   LINE      CODE     ---       #312
      01001B22H   LINE      CODE     ---       #314
      01001B24H   LINE      CODE     ---       #315
      01001B24H   LINE      CODE     ---       #317
      01001B26H   LINE      CODE     ---       #318
      01001B26H   LINE      CODE     ---       #319
      01001B26H   LINE      CODE     ---       #320
      ---         BLOCKEND  ---      ---       LVL=0

      01001B33H   BLOCK     CODE     ---       LVL=0
      01001B33H   LINE      CODE     ---       #329
      01001B42H   LINE      CODE     ---       #331
      01001B45H   LINE      CODE     ---       #333
      01001B48H   LINE      CODE     ---       #334
      01001B48H   LINE      CODE     ---       #336
      01001B4BH   LINE      CODE     ---       #337
      01001B4BH   LINE      CODE     ---       #338
      01001B5AH   LINE      CODE     ---       #339
      01001B5CH   LINE      CODE     ---       #340
      01001B5EH   LINE      CODE     ---       #341
      01001B5EH   LINE      CODE     ---       #342
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 77


      01001B60H   LINE      CODE     ---       #344
      01001B60H   LINE      CODE     ---       #345
      01001B63H   LINE      CODE     ---       #346
      01001B63H   LINE      CODE     ---       #347
      01001B7EH   LINE      CODE     ---       #348
      01001B9AH   LINE      CODE     ---       #349
      01001B9AH   LINE      CODE     ---       #350
      01001B9CH   LINE      CODE     ---       #351
      01001B9CH   LINE      CODE     ---       #352
      01001B9EH   LINE      CODE     ---       #353
      01001B9EH   LINE      CODE     ---       #354
      01001B9EH   LINE      CODE     ---       #355
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #364
      0100002AH   LINE      CODE     ---       #367
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #376
      0100002EH   LINE      CODE     ---       #379
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #388
      0100002FH   LINE      CODE     ---       #391
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #400
      01000030H   LINE      CODE     ---       #403
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #412
      01000031H   LINE      CODE     ---       #415
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #424
      01000032H   LINE      CODE     ---       #427
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #436
      01000036H   LINE      CODE     ---       #439
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #448
      01000037H   LINE      CODE     ---       #451
      ---         BLOCKEND  ---      ---       LVL=0

      01000038H   BLOCK     CODE     ---       LVL=0
      01000038H   LINE      CODE     ---       #460
      01000038H   LINE      CODE     ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      01000039H   BLOCK     CODE     ---       LVL=0
      01000039H   LINE      CODE     ---       #472
      01000039H   LINE      CODE     ---       #475
      ---         BLOCKEND  ---      ---       LVL=0

      0100003AH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 78


      0100003AH   LINE      CODE     ---       #484
      0100003AH   LINE      CODE     ---       #487
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      02000079H   PUBLIC    XDATA    BYTE      Data_Length
      02000059H   PUBLIC    XDATA    ---       UART_Get_String
      010015A2H   PUBLIC    CODE     ---       UART_Data_Process
      01001BABH   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      0100213FH   PUBLIC    CODE     ---       UART_Data_Init
      010022D7H   PUBLIC    CODE     ---       Clean_UART_Data_Length
      010022D1H   PUBLIC    CODE     ---       Return_UART_Data_Length
      0100211FH   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 79


      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 80


      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      010022DDH   SYMBOL    CONST    ---       _?ix1000
      010022E0H   SYMBOL    CONST    ---       _?ix1001
      010022E3H   SYMBOL    CONST    ---       _?ix1002

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 81


      0100211FH   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      0100211FH   LINE      CODE     ---       #12
      0100211FH   LINE      CODE     ---       #13
      0100211FH   LINE      CODE     ---       #14
      0100212CH   LINE      CODE     ---       #15
      01002132H   LINE      CODE     ---       #16
      0100213CH   LINE      CODE     ---       #17
      0100213CH   LINE      CODE     ---       #18
      0100213EH   LINE      CODE     ---       #19
      0100213EH   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      010022D1H   BLOCK     CODE     ---       LVL=0
      010022D1H   LINE      CODE     ---       #24
      010022D1H   LINE      CODE     ---       #25
      010022D1H   LINE      CODE     ---       #26
      010022D6H   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      010022D7H   BLOCK     CODE     ---       LVL=0
      010022D7H   LINE      CODE     ---       #30
      010022D7H   LINE      CODE     ---       #31
      010022D7H   LINE      CODE     ---       #32
      010022DCH   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      0100213FH   BLOCK     CODE     ---       LVL=0
      0100213FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100213FH   LINE      CODE     ---       #36
      0100213FH   LINE      CODE     ---       #37
      0100213FH   LINE      CODE     ---       #39
      01002144H   LINE      CODE     ---       #40
      0100214FH   LINE      CODE     ---       #41
      0100214FH   LINE      CODE     ---       #42
      0100215BH   LINE      CODE     ---       #43
      0100215EH   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001BABH   BLOCK     CODE     ---       LVL=0
      02000046H   SYMBOL    XDATA    BYTE      CMD_No
      01001BB0H   BLOCK     CODE     NEAR LAB  LVL=1
      02000047H   SYMBOL    XDATA    ---       UART_PASS_Data
      0200004AH   SYMBOL    XDATA    ---       UART_Error_Data
      0200004DH   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001BABH   LINE      CODE     ---       #61
      01001BB0H   LINE      CODE     ---       #62
      01001BB0H   LINE      CODE     ---       #63
      01001BC3H   LINE      CODE     ---       #64
      01001BD6H   LINE      CODE     ---       #65
      01001BE9H   LINE      CODE     ---       #66
      01001BF7H   LINE      CODE     ---       #67
      01001BF7H   LINE      CODE     ---       #69
      01001BF7H   LINE      CODE     ---       #70
      01001BF7H   LINE      CODE     ---       #71
      01001BFDH   LINE      CODE     ---       #72
      01001BFDH   LINE      CODE     ---       #73
      01001BFFH   LINE      CODE     ---       #75
      01001BFFH   LINE      CODE     ---       #76
      01001BFFH   LINE      CODE     ---       #77
      01001C0AH   LINE      CODE     ---       #78
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 82


      01001C0AH   LINE      CODE     ---       #79
      01001C0CH   LINE      CODE     ---       #81
      01001C0CH   LINE      CODE     ---       #82
      01001C0CH   LINE      CODE     ---       #83
      01001C1DH   LINE      CODE     ---       #86
      01001C1DH   LINE      CODE     ---       #87
      01001C1DH   LINE      CODE     ---       #88
      01001C1DH   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      010015A2H   BLOCK     CODE     ---       LVL=0
      010015A2H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      010015A2H   LINE      CODE     ---       #92
      010015A2H   LINE      CODE     ---       #93
      010015A2H   LINE      CODE     ---       #96
      010015A5H   LINE      CODE     ---       #98
      010015AAH   LINE      CODE     ---       #99
      010015AAH   LINE      CODE     ---       #100
      010015ADH   LINE      CODE     ---       #101
      010015B0H   LINE      CODE     ---       #102
      010015B0H   LINE      CODE     ---       #104
      010015BBH   LINE      CODE     ---       #105
      010015BBH   LINE      CODE     ---       #106
      010015CAH   LINE      CODE     ---       #107
      010015CAH   LINE      CODE     ---       #109
      010015CCH   LINE      CODE     ---       #110
      010015CFH   LINE      CODE     ---       #111
      010015DDH   LINE      CODE     ---       #112
      010015DDH   LINE      CODE     ---       #114
      010015DFH   LINE      CODE     ---       #115
      010015E2H   LINE      CODE     ---       #116
      010015F0H   LINE      CODE     ---       #117
      010015F0H   LINE      CODE     ---       #119
      010015F2H   LINE      CODE     ---       #120
      010015F5H   LINE      CODE     ---       #121
      01001606H   LINE      CODE     ---       #122
      01001606H   LINE      CODE     ---       #124
      01001608H   LINE      CODE     ---       #125
      0100160BH   LINE      CODE     ---       #126
      01001619H   LINE      CODE     ---       #127
      01001619H   LINE      CODE     ---       #129
      0100161BH   LINE      CODE     ---       #130
      0100161EH   LINE      CODE     ---       #131
      0100162CH   LINE      CODE     ---       #132
      0100162CH   LINE      CODE     ---       #134
      0100162EH   LINE      CODE     ---       #135
      01001631H   LINE      CODE     ---       #136
      01001642H   LINE      CODE     ---       #137
      01001642H   LINE      CODE     ---       #139
      01001644H   LINE      CODE     ---       #140
      01001646H   LINE      CODE     ---       #141
      01001654H   LINE      CODE     ---       #142
      01001654H   LINE      CODE     ---       #144
      01001656H   LINE      CODE     ---       #145
      01001658H   LINE      CODE     ---       #146
      01001666H   LINE      CODE     ---       #147
      01001666H   LINE      CODE     ---       #149
      01001668H   LINE      CODE     ---       #150
      0100166AH   LINE      CODE     ---       #151
      0100167BH   LINE      CODE     ---       #152
      0100167BH   LINE      CODE     ---       #154
      0100167DH   LINE      CODE     ---       #155
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 83


      0100167FH   LINE      CODE     ---       #156
      0100168DH   LINE      CODE     ---       #157
      0100168DH   LINE      CODE     ---       #159
      0100168FH   LINE      CODE     ---       #160
      01001691H   LINE      CODE     ---       #161
      0100169FH   LINE      CODE     ---       #162
      0100169FH   LINE      CODE     ---       #164
      010016A1H   LINE      CODE     ---       #165
      010016A3H   LINE      CODE     ---       #166
      010016AFH   LINE      CODE     ---       #167
      010016AFH   LINE      CODE     ---       #169
      010016B1H   LINE      CODE     ---       #170
      010016B3H   LINE      CODE     ---       #172
      010016B3H   LINE      CODE     ---       #174
      010016B5H   LINE      CODE     ---       #175
      010016B5H   LINE      CODE     ---       #176
      010016B5H   LINE      CODE     ---       #178
      010016B8H   LINE      CODE     ---       #180
      010016BAH   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000025H.2 PUBLIC    BIT      BIT       K5_Press
      00000025H.1 PUBLIC    BIT      BIT       K4_Press
      00000025H.0 PUBLIC    BIT      BIT       K3_Press
      00000024H.7 PUBLIC    BIT      BIT       K2_Press
      00000024H.6 PUBLIC    BIT      BIT       K1_Press
      0200009FH   PUBLIC    XDATA    BYTE      K5_Count
      0200009EH   PUBLIC    XDATA    BYTE      K4_Count
      0200009DH   PUBLIC    XDATA    BYTE      K3_Count
      0200009CH   PUBLIC    XDATA    BYTE      K2_Count
      0200009BH   PUBLIC    XDATA    BYTE      K1_Count
      0200009AH   PUBLIC    XDATA    BYTE      Key_Buff
      01001F10H   PUBLIC    CODE     ---       Key_Buff_Return
      01001478H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 84


      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 85


      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 86


      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001478H   BLOCK     CODE     ---       LVL=0
      01001478H   LINE      CODE     ---       #20
      01001478H   LINE      CODE     ---       #21
      01001478H   LINE      CODE     ---       #23
      0100147BH   LINE      CODE     ---       #24
      0100147BH   LINE      CODE     ---       #25
      0100147EH   LINE      CODE     ---       #26
      0100147EH   LINE      CODE     ---       #27
      0100148BH   LINE      CODE     ---       #28
      0100148BH   LINE      CODE     ---       #29
      0100148DH   LINE      CODE     ---       #30
      0100148FH   LINE      CODE     ---       #31
      01001491H   LINE      CODE     ---       #43
      01001491H   LINE      CODE     ---       #44
      01001494H   LINE      CODE     ---       #45
      01001494H   LINE      CODE     ---       #46
      010014A1H   LINE      CODE     ---       #47
      010014A1H   LINE      CODE     ---       #48
      010014A3H   LINE      CODE     ---       #49
      010014A5H   LINE      CODE     ---       #50
      010014A7H   LINE      CODE     ---       #52
      010014A7H   LINE      CODE     ---       #53
      010014ADH   LINE      CODE     ---       #54
      010014ADH   LINE      CODE     ---       #55
      010014AFH   LINE      CODE     ---       #57
      010014AFH   LINE      CODE     ---       #58
      010014B4H   LINE      CODE     ---       #59
      010014B4H   LINE      CODE     ---       #60
      010014B4H   LINE      CODE     ---       #63
      010014B7H   LINE      CODE     ---       #64
      010014B7H   LINE      CODE     ---       #65
      010014BAH   LINE      CODE     ---       #66
      010014BAH   LINE      CODE     ---       #67
      010014C7H   LINE      CODE     ---       #68
      010014C7H   LINE      CODE     ---       #69
      010014C9H   LINE      CODE     ---       #70
      010014CBH   LINE      CODE     ---       #71
      010014CDH   LINE      CODE     ---       #83
      010014CDH   LINE      CODE     ---       #84
      010014D0H   LINE      CODE     ---       #85
      010014D0H   LINE      CODE     ---       #86
      010014DDH   LINE      CODE     ---       #87
      010014DDH   LINE      CODE     ---       #88
      010014DFH   LINE      CODE     ---       #89
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 87


      010014E1H   LINE      CODE     ---       #90
      010014E3H   LINE      CODE     ---       #92
      010014E3H   LINE      CODE     ---       #93
      010014E9H   LINE      CODE     ---       #94
      010014E9H   LINE      CODE     ---       #95
      010014EBH   LINE      CODE     ---       #97
      010014EBH   LINE      CODE     ---       #98
      010014F0H   LINE      CODE     ---       #99
      010014F0H   LINE      CODE     ---       #100
      010014F0H   LINE      CODE     ---       #103
      010014F3H   LINE      CODE     ---       #104
      010014F3H   LINE      CODE     ---       #105
      010014F6H   LINE      CODE     ---       #106
      010014F6H   LINE      CODE     ---       #107
      01001503H   LINE      CODE     ---       #108
      01001503H   LINE      CODE     ---       #109
      01001505H   LINE      CODE     ---       #110
      01001507H   LINE      CODE     ---       #111
      01001509H   LINE      CODE     ---       #123
      01001509H   LINE      CODE     ---       #124
      0100150CH   LINE      CODE     ---       #125
      0100150CH   LINE      CODE     ---       #126
      01001519H   LINE      CODE     ---       #127
      01001519H   LINE      CODE     ---       #128
      0100151BH   LINE      CODE     ---       #129
      0100151DH   LINE      CODE     ---       #130
      0100151FH   LINE      CODE     ---       #132
      0100151FH   LINE      CODE     ---       #133
      01001525H   LINE      CODE     ---       #134
      01001525H   LINE      CODE     ---       #135
      01001527H   LINE      CODE     ---       #137
      01001527H   LINE      CODE     ---       #138
      0100152CH   LINE      CODE     ---       #139
      0100152CH   LINE      CODE     ---       #140
      0100152CH   LINE      CODE     ---       #143
      0100152FH   LINE      CODE     ---       #144
      0100152FH   LINE      CODE     ---       #145
      01001532H   LINE      CODE     ---       #146
      01001532H   LINE      CODE     ---       #147
      0100153FH   LINE      CODE     ---       #148
      0100153FH   LINE      CODE     ---       #149
      01001541H   LINE      CODE     ---       #150
      01001543H   LINE      CODE     ---       #151
      01001545H   LINE      CODE     ---       #163
      01001545H   LINE      CODE     ---       #164
      01001548H   LINE      CODE     ---       #165
      01001548H   LINE      CODE     ---       #166
      01001555H   LINE      CODE     ---       #167
      01001555H   LINE      CODE     ---       #168
      01001557H   LINE      CODE     ---       #169
      01001559H   LINE      CODE     ---       #170
      0100155BH   LINE      CODE     ---       #172
      0100155BH   LINE      CODE     ---       #173
      01001561H   LINE      CODE     ---       #174
      01001561H   LINE      CODE     ---       #175
      01001563H   LINE      CODE     ---       #177
      01001563H   LINE      CODE     ---       #178
      01001568H   LINE      CODE     ---       #179
      01001568H   LINE      CODE     ---       #180
      01001568H   LINE      CODE     ---       #183
      0100156BH   LINE      CODE     ---       #184
      0100156BH   LINE      CODE     ---       #185
      0100156EH   LINE      CODE     ---       #186
      0100156EH   LINE      CODE     ---       #187
      0100157BH   LINE      CODE     ---       #188
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 88


      0100157BH   LINE      CODE     ---       #189
      0100157DH   LINE      CODE     ---       #190
      0100157FH   LINE      CODE     ---       #191
      01001580H   LINE      CODE     ---       #203
      01001580H   LINE      CODE     ---       #204
      01001583H   LINE      CODE     ---       #205
      01001583H   LINE      CODE     ---       #206
      01001590H   LINE      CODE     ---       #207
      01001590H   LINE      CODE     ---       #208
      01001592H   LINE      CODE     ---       #209
      01001594H   LINE      CODE     ---       #210
      01001595H   LINE      CODE     ---       #212
      01001595H   LINE      CODE     ---       #213
      0100159BH   LINE      CODE     ---       #214
      0100159BH   LINE      CODE     ---       #215
      0100159CH   LINE      CODE     ---       #217
      0100159CH   LINE      CODE     ---       #218
      010015A1H   LINE      CODE     ---       #219
      010015A1H   LINE      CODE     ---       #220
      010015A1H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      01001F10H   BLOCK     CODE     ---       LVL=0
      01001F10H   LINE      CODE     ---       #224
      01001F10H   LINE      CODE     ---       #225
      01001F10H   LINE      CODE     ---       #226
      01001F15H   LINE      CODE     ---       #228
      01001F1CH   LINE      CODE     ---       #229
      01001F26H   LINE      CODE     ---       #230
      01001F30H   LINE      CODE     ---       #231
      01001F3AH   LINE      CODE     ---       #232
      01001F44H   LINE      CODE     ---       #234
      01001F49H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      01000026H   PUBLIC    CODE     ---       ADC_GetResult
      0100003EH   PUBLIC    CODE     ---       ADC_ClearConvertIntFlag
      01000086H   PUBLIC    CODE     ---       ADC_GetConvertIntFlag
      010022C0H   PUBLIC    CODE     ---       _ADC_StartConvert
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 89


      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 90


      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 91


      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0200008FH   SYMBOL    XDATA    ---       filter_buffer
      02000099H   SYMBOL    XDATA    BYTE      filter_index

      010022C0H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      010022C0H   LINE      CODE     ---       #43
      010022C0H   LINE      CODE     ---       #44
      010022C0H   LINE      CODE     ---       #45
      010022C2H   LINE      CODE     ---       #46
      010022C5H   LINE      CODE     ---       #47
      010022C8H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #50
      01000086H   LINE      CODE     ---       #51
      01000086H   LINE      CODE     ---       #52
      0100008FH   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      0100003EH   BLOCK     CODE     ---       LVL=0
      0100003EH   LINE      CODE     ---       #55
      0100003EH   LINE      CODE     ---       #56
      0100003EH   LINE      CODE     ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #60
      01000026H   LINE      CODE     ---       #61
      01000026H   LINE      CODE     ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.5 PUBLIC    BIT      BIT       speedup
      00000024H.4 PUBLIC    BIT      BIT       longhit
      02000034H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.3 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.2 PUBLIC    BIT      BIT       direction_changed
      00000024H.1 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000032H   PUBLIC    XDATA    WORD      precise_k2_timer
      02000030H   PUBLIC    XDATA    WORD      BatV
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 92


      00000024H.0 PUBLIC    BIT      BIT       key3_long_started
      00000023H.7 PUBLIC    BIT      BIT       key1_long_started
      00000023H.6 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.5 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.4 PUBLIC    BIT      BIT       Delay_Open
      0200002FH   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.3 PUBLIC    BIT      BIT       charge_flash
      0200002EH   PUBLIC    XDATA    BYTE      last_direction
      00000023H.2 PUBLIC    BIT      BIT       key3_pressed
      00000023H.1 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000023H.0 PUBLIC    BIT      BIT       key1_pressed
      0200002DH   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.7 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.6 PUBLIC    BIT      BIT       ledonoff
      0200002BH   PUBLIC    XDATA    WORD      original_speed
      00000022H.5 PUBLIC    BIT      BIT       key3_handle
      0200002AH   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000028H   PUBLIC    XDATA    INT       Self_Check
      00000022H.4 PUBLIC    BIT      BIT       key1_handle
      02000026H   PUBLIC    XDATA    WORD      key3_duration
      02000024H   PUBLIC    XDATA    WORD      dly
      00000022H.3 PUBLIC    BIT      BIT       k3_released
      02000022H   PUBLIC    XDATA    WORD      key1_duration
      00000022H.2 PUBLIC    BIT      BIT       k2_released
      02000020H   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000022H.1 PUBLIC    BIT      BIT       batlow1
      0200001EH   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000022H.0 PUBLIC    BIT      BIT       auto_rotate_mode
      0200001DH   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.7 PUBLIC    BIT      BIT       Charg_State_Buff
      0200001CH   PUBLIC    XDATA    BYTE      battery_check_divider
      00000021H.6 PUBLIC    BIT      BIT       led_flash_state
      0200001AH   PUBLIC    XDATA    WORD      led_flash_timer
      02000018H   PUBLIC    XDATA    WORD      timer_test_counter
      02000017H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      02000016H   PUBLIC    XDATA    BYTE      main_loop_counter
      00000021H.5 PUBLIC    BIT      BIT       need_led_flash
      02000014H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      02000012H   PUBLIC    XDATA    WORD      timer_1ms_count
      00000021H.4 PUBLIC    BIT      BIT       use_precise_timer
      02000010H   PUBLIC    XDATA    WORD      speedup_cnt
      0200000EH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.3 PUBLIC    BIT      BIT       auto_rotate_flash
      0200000CH   PUBLIC    XDATA    WORD      key1_press_time
      0200000BH   PUBLIC    XDATA    BYTE      key_scan_divider
      00000021H.2 PUBLIC    BIT      BIT       K3_cnt_EN
      02000007H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.1 PUBLIC    BIT      BIT       K2_cnt_EN
      00000021H.0 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.6 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.5 PUBLIC    BIT      BIT       timer_test_enable
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000005H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010011B7H   PUBLIC    CODE     ---       Key_Interrupt_Process
      010016BBH   PUBLIC    CODE     ---       LED_Control
      0100208BH   PUBLIC    CODE     ---       Restore_dly
      01001FE4H   PUBLIC    CODE     ---       _Store_dly
      010018C3H   PUBLIC    CODE     ---       Battery_Check
      01001E8AH   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001A28H   PUBLIC    CODE     ---       _Motor_Step_Control
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 93


      0100215FH   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 94


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 95


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000025H.7 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000035H   SYMBOL    XDATA    INT       Key_Input
      02000037H   SYMBOL    XDATA    INT       Charge_Input
      02000039H   SYMBOL    XDATA    INT       Key_State
      0200003BH   SYMBOL    XDATA    INT       Key_State_Save
      0200003DH   SYMBOL    XDATA    INT       Charge_State_Save
      0200003FH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000026H.0 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000026H.1 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      02000041H   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      02000043H   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000026H.2 SYMBOL    BIT      BIT       Voltage_Low
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 96


      02000044H   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #149
      010000B6H   LINE      CODE     ---       #150
      010000B6H   LINE      CODE     ---       #156
      010000B8H   LINE      CODE     ---       #159
      010000BAH   LINE      CODE     ---       #160
      010000C1H   LINE      CODE     ---       #163
      010000C4H   LINE      CODE     ---       #164
      010000CBH   LINE      CODE     ---       #166
      010000CEH   LINE      CODE     ---       #168
      010000D1H   LINE      CODE     ---       #169
      010000D4H   LINE      CODE     ---       #171
      010000D7H   LINE      CODE     ---       #172
      010000DAH   LINE      CODE     ---       #175
      010000DCH   LINE      CODE     ---       #177
      010000DEH   LINE      CODE     ---       #178
      010000E5H   LINE      CODE     ---       #179
      010000E9H   LINE      CODE     ---       #180
      010000EBH   LINE      CODE     ---       #181
      010000F1H   LINE      CODE     ---       #182
      010000F7H   LINE      CODE     ---       #183
      010000FDH   LINE      CODE     ---       #184
      010000FFH   LINE      CODE     ---       #185
      0100010AH   LINE      CODE     ---       #186
      0100011EH   LINE      CODE     ---       #187
      0100011EH   LINE      CODE     ---       #188
      01000127H   LINE      CODE     ---       #189
      0100012DH   LINE      CODE     ---       #190
      0100012DH   LINE      CODE     ---       #191
      0100012FH   LINE      CODE     ---       #194
      01000132H   LINE      CODE     ---       #196
      01000138H   LINE      CODE     ---       #197
      01000138H   LINE      CODE     ---       #198
      0100013BH   LINE      CODE     ---       #199
      0100013DH   LINE      CODE     ---       #201
      01000140H   LINE      CODE     ---       #202
      0100014BH   LINE      CODE     ---       #205
      01000151H   LINE      CODE     ---       #206
      01000151H   LINE      CODE     ---       #207
      0100015FH   LINE      CODE     ---       #208
      01000171H   LINE      CODE     ---       #209
      01000171H   LINE      CODE     ---       #210
      01000175H   LINE      CODE     ---       #211
      01000177H   LINE      CODE     ---       #212
      0100017DH   LINE      CODE     ---       #213
      0100017FH   LINE      CODE     ---       #214
      01000181H   LINE      CODE     ---       #215
      01000183H   LINE      CODE     ---       #216
      0100018AH   LINE      CODE     ---       #217
      01000190H   LINE      CODE     ---       #218
      01000192H   LINE      CODE     ---       #219
      01000194H   LINE      CODE     ---       #220
      0100019CH   LINE      CODE     ---       #221
      0100019EH   LINE      CODE     ---       #222
      010001A0H   LINE      CODE     ---       #223
      010001A2H   LINE      CODE     ---       #224
      010001A2H   LINE      CODE     ---       #225
      010001A5H   LINE      CODE     ---       #227
      010001A5H   LINE      CODE     ---       #228
      010001ACH   LINE      CODE     ---       #230
      010001C8H   LINE      CODE     ---       #231
      010001C8H   LINE      CODE     ---       #232
      010001CEH   LINE      CODE     ---       #233
      010001CEH   LINE      CODE     ---       #234
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 97


      010001DCH   LINE      CODE     ---       #235
      010001EDH   LINE      CODE     ---       #236
      010001EDH   LINE      CODE     ---       #237
      010001F1H   LINE      CODE     ---       #238
      010001F3H   LINE      CODE     ---       #239
      010001F5H   LINE      CODE     ---       #240
      010001FAH   LINE      CODE     ---       #241
      010001FAH   LINE      CODE     ---       #242
      010001FCH   LINE      CODE     ---       #243
      01000207H   LINE      CODE     ---       #244
      01000207H   LINE      CODE     ---       #245
      01000215H   LINE      CODE     ---       #246
      01000226H   LINE      CODE     ---       #247
      01000226H   LINE      CODE     ---       #248
      0100022AH   LINE      CODE     ---       #249
      0100022CH   LINE      CODE     ---       #250
      01000230H   LINE      CODE     ---       #251
      01000232H   LINE      CODE     ---       #252
      01000238H   LINE      CODE     ---       #253
      01000238H   LINE      CODE     ---       #254
      0100023AH   LINE      CODE     ---       #255
      01000244H   LINE      CODE     ---       #256
      01000244H   LINE      CODE     ---       #257
      0100024AH   LINE      CODE     ---       #258
      0100024CH   LINE      CODE     ---       #259
      01000250H   LINE      CODE     ---       #260
      01000256H   LINE      CODE     ---       #261
      01000258H   LINE      CODE     ---       #262
      01000258H   LINE      CODE     ---       #263
      0100025AH   LINE      CODE     ---       #265
      01000264H   LINE      CODE     ---       #266
      01000264H   LINE      CODE     ---       #267
      01000269H   LINE      CODE     ---       #268
      01000269H   LINE      CODE     ---       #269
      01000269H   LINE      CODE     ---       #271
      01000278H   LINE      CODE     ---       #272
      01000290H   LINE      CODE     ---       #273
      01000293H   LINE      CODE     ---       #275
      01000295H   LINE      CODE     ---       #276
      0100029BH   LINE      CODE     ---       #277
      010002A4H   LINE      CODE     ---       #278
      010002A7H   LINE      CODE     ---       #281
      010002B0H   LINE      CODE     ---       #282
      010002B0H   LINE      CODE     ---       #284
      010002B6H   LINE      CODE     ---       #285
      010002B6H   LINE      CODE     ---       #287
      010002B6H   LINE      CODE     ---       #289
      010002B6H   LINE      CODE     ---       #291
      010002B6H   LINE      CODE     ---       #292
      010002B6H   LINE      CODE     ---       #293
      010002B9H   LINE      CODE     ---       #294
      010002BBH   LINE      CODE     ---       #296
      010002C1H   LINE      CODE     ---       #299
      010002D0H   LINE      CODE     ---       #300
      010002D0H   LINE      CODE     ---       #301
      010002D2H   LINE      CODE     ---       #302
      010002D5H   LINE      CODE     ---       #303
      010002D5H   LINE      CODE     ---       #304
      010002E0H   LINE      CODE     ---       #306
      010002F1H   LINE      CODE     ---       #307
      01000309H   LINE      CODE     ---       #310
      01000318H   LINE      CODE     ---       #311
      01000318H   LINE      CODE     ---       #312
      0100031AH   LINE      CODE     ---       #313
      0100031DH   LINE      CODE     ---       #314
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 98


      0100031DH   LINE      CODE     ---       #317
      01000320H   LINE      CODE     ---       #320
      0100032DH   LINE      CODE     ---       #321
      0100032DH   LINE      CODE     ---       #323
      01000330H   LINE      CODE     ---       #324
      01000330H   LINE      CODE     ---       #326
      01000333H   LINE      CODE     ---       #327
      01000333H   LINE      CODE     ---       #328
      0100033BH   LINE      CODE     ---       #329
      0100033DH   LINE      CODE     ---       #330
      0100033DH   LINE      CODE     ---       #331
      0100033DH   LINE      CODE     ---       #332
      0100033FH   LINE      CODE     ---       #333
      01000341H   LINE      CODE     ---       #334
      01000347H   LINE      CODE     ---       #335
      01000347H   LINE      CODE     ---       #337
      0100034AH   LINE      CODE     ---       #338
      0100034AH   LINE      CODE     ---       #340
      0100034DH   LINE      CODE     ---       #341
      0100034DH   LINE      CODE     ---       #342
      01000355H   LINE      CODE     ---       #343
      01000357H   LINE      CODE     ---       #344
      01000357H   LINE      CODE     ---       #345
      01000357H   LINE      CODE     ---       #346
      01000359H   LINE      CODE     ---       #347
      0100035BH   LINE      CODE     ---       #349
      0100035BH   LINE      CODE     ---       #351
      01000361H   LINE      CODE     ---       #352
      01000361H   LINE      CODE     ---       #354
      01000369H   LINE      CODE     ---       #355
      0100036BH   LINE      CODE     ---       #359
      0100036BH   LINE      CODE     ---       #360
      0100036DH   LINE      CODE     ---       #361
      0100036DH   LINE      CODE     ---       #364
      01000378H   LINE      CODE     ---       #365
      01000378H   LINE      CODE     ---       #367
      0100037BH   LINE      CODE     ---       #368
      0100037BH   LINE      CODE     ---       #370
      0100037EH   LINE      CODE     ---       #371
      0100037EH   LINE      CODE     ---       #372
      01000380H   LINE      CODE     ---       #373
      01000380H   LINE      CODE     ---       #375
      01000383H   LINE      CODE     ---       #376
      01000383H   LINE      CODE     ---       #377
      01000385H   LINE      CODE     ---       #378
      01000385H   LINE      CODE     ---       #381
      0100038EH   LINE      CODE     ---       #382
      0100038EH   LINE      CODE     ---       #383
      01000390H   LINE      CODE     ---       #384
      01000392H   LINE      CODE     ---       #385
      01000394H   LINE      CODE     ---       #386
      0100039CH   LINE      CODE     ---       #387
      0100039EH   LINE      CODE     ---       #388
      0100039EH   LINE      CODE     ---       #389
      010003A0H   LINE      CODE     ---       #391
      010003A0H   LINE      CODE     ---       #394
      010003ACH   LINE      CODE     ---       #395
      010003ACH   LINE      CODE     ---       #396
      010003AFH   LINE      CODE     ---       #397
      010003AFH   LINE      CODE     ---       #399
      010003B1H   LINE      CODE     ---       #400
      010003B7H   LINE      CODE     ---       #402
      010003BCH   LINE      CODE     ---       #403
      010003C5H   LINE      CODE     ---       #404
      010003CAH   LINE      CODE     ---       #405
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 99


      010003CAH   LINE      CODE     ---       #408
      010003D9H   LINE      CODE     ---       #409
      010003D9H   LINE      CODE     ---       #410
      010003DBH   LINE      CODE     ---       #411
      010003E0H   LINE      CODE     ---       #412
      010003E2H   LINE      CODE     ---       #413
      010003E4H   LINE      CODE     ---       #414
      010003E6H   LINE      CODE     ---       #415
      010003EEH   LINE      CODE     ---       #416
      010003F0H   LINE      CODE     ---       #417
      010003F2H   LINE      CODE     ---       #418
      010003F7H   LINE      CODE     ---       #419
      010003F7H   LINE      CODE     ---       #420
      010003F9H   LINE      CODE     ---       #422
      010003F9H   LINE      CODE     ---       #424
      010003FCH   LINE      CODE     ---       #425
      010003FCH   LINE      CODE     ---       #426
      010003FEH   LINE      CODE     ---       #427
      01000403H   LINE      CODE     ---       #428
      0100040AH   LINE      CODE     ---       #429
      0100040AH   LINE      CODE     ---       #430
      0100040CH   LINE      CODE     ---       #431
      0100040CH   LINE      CODE     ---       #432
      0100040CH   LINE      CODE     ---       #435
      01000412H   LINE      CODE     ---       #436
      01000412H   LINE      CODE     ---       #437
      0100042FH   LINE      CODE     ---       #438
      0100042FH   LINE      CODE     ---       #439
      01000432H   LINE      CODE     ---       #440
      01000432H   LINE      CODE     ---       #442
      01000432H   LINE      CODE     ---       #443
      01000432H   LINE      CODE     ---       #444
      01000432H   LINE      CODE     ---       #445
      01000432H   LINE      CODE     ---       #446
      01000432H   LINE      CODE     ---       #447
      01000432H   LINE      CODE     ---       #448
      01000434H   LINE      CODE     ---       #449
      0100043FH   LINE      CODE     ---       #450
      0100043FH   LINE      CODE     ---       #452
      0100043FH   LINE      CODE     ---       #453
      0100043FH   LINE      CODE     ---       #454
      0100043FH   LINE      CODE     ---       #455
      0100043FH   LINE      CODE     ---       #457
      01000441H   LINE      CODE     ---       #458
      01000447H   LINE      CODE     ---       #459
      01000447H   LINE      CODE     ---       #461
      0100044CH   LINE      CODE     ---       #462
      0100044CH   LINE      CODE     ---       #463
      0100044CH   LINE      CODE     ---       #464
      0100044CH   LINE      CODE     ---       #465
      0100044CH   LINE      CODE     ---       #466
      0100044CH   LINE      CODE     ---       #467
      0100044CH   LINE      CODE     ---       #468
      0100044EH   LINE      CODE     ---       #469
      0100046BH   LINE      CODE     ---       #470
      0100046BH   LINE      CODE     ---       #471
      0100046EH   LINE      CODE     ---       #472
      0100046EH   LINE      CODE     ---       #474
      01000473H   LINE      CODE     ---       #475
      01000473H   LINE      CODE     ---       #476
      01000473H   LINE      CODE     ---       #477
      01000473H   LINE      CODE     ---       #478
      01000473H   LINE      CODE     ---       #479
      01000473H   LINE      CODE     ---       #480
      01000475H   LINE      CODE     ---       #481
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 100


      01000480H   LINE      CODE     ---       #482
      01000480H   LINE      CODE     ---       #484
      01000482H   LINE      CODE     ---       #485
      01000484H   LINE      CODE     ---       #486
      0100048CH   LINE      CODE     ---       #487
      0100048EH   LINE      CODE     ---       #488
      01000490H   LINE      CODE     ---       #489
      01000496H   LINE      CODE     ---       #490
      01000496H   LINE      CODE     ---       #492
      0100049CH   LINE      CODE     ---       #493
      010004A5H   LINE      CODE     ---       #494
      010004A7H   LINE      CODE     ---       #495
      010004A9H   LINE      CODE     ---       #496
      010004ABH   LINE      CODE     ---       #497
      010004B2H   LINE      CODE     ---       #498
      010004B2H   LINE      CODE     ---       #499
      010004B2H   LINE      CODE     ---       #500
      010004B2H   LINE      CODE     ---       #503
      010004B8H   LINE      CODE     ---       #504
      010004B8H   LINE      CODE     ---       #505
      010004BAH   LINE      CODE     ---       #506
      010004C8H   LINE      CODE     ---       #508
      010004D9H   LINE      CODE     ---       #509
      010004D9H   LINE      CODE     ---       #510
      010004E1H   LINE      CODE     ---       #511
      010004E3H   LINE      CODE     ---       #513
      010004E3H   LINE      CODE     ---       #514
      010004EAH   LINE      CODE     ---       #515
      010004EAH   LINE      CODE     ---       #516
      010004EAH   LINE      CODE     ---       #519
      010004EDH   LINE      CODE     ---       #520
      010004EDH   LINE      CODE     ---       #522
      010004FBH   LINE      CODE     ---       #523
      0100050DH   LINE      CODE     ---       #524
      0100050DH   LINE      CODE     ---       #525
      01000511H   LINE      CODE     ---       #526
      01000513H   LINE      CODE     ---       #527
      01000513H   LINE      CODE     ---       #528
      01000516H   LINE      CODE     ---       #530
      01000516H   LINE      CODE     ---       #532
      01000518H   LINE      CODE     ---       #533
      0100051CH   LINE      CODE     ---       #534
      0100051CH   LINE      CODE     ---       #535
      0100051FH   LINE      CODE     ---       #538
      0100051FH   LINE      CODE     ---       #539
      01000522H   LINE      CODE     ---       #541
      01000542H   LINE      CODE     ---       #542
      01000542H   LINE      CODE     ---       #543
      01000544H   LINE      CODE     ---       #544
      01000547H   LINE      CODE     ---       #545
      01000564H   LINE      CODE     ---       #546
      01000564H   LINE      CODE     ---       #547
      0100056CH   LINE      CODE     ---       #548
      0100056EH   LINE      CODE     ---       #549
      0100057DH   LINE      CODE     ---       #550
      0100057DH   LINE      CODE     ---       #552
      01000580H   LINE      CODE     ---       #553
      01000580H   LINE      CODE     ---       #554
      01000582H   LINE      CODE     ---       #555
      01000588H   LINE      CODE     ---       #556
      0100058DH   LINE      CODE     ---       #557
      01000596H   LINE      CODE     ---       #558
      0100059BH   LINE      CODE     ---       #559
      0100059BH   LINE      CODE     ---       #561
      010005AAH   LINE      CODE     ---       #562
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 101


      010005AAH   LINE      CODE     ---       #563
      010005ACH   LINE      CODE     ---       #564
      010005B1H   LINE      CODE     ---       #565
      010005BAH   LINE      CODE     ---       #566
      010005BCH   LINE      CODE     ---       #567
      010005BEH   LINE      CODE     ---       #568
      010005C3H   LINE      CODE     ---       #569
      010005C3H   LINE      CODE     ---       #570
      010005C3H   LINE      CODE     ---       #571
      010005D2H   LINE      CODE     ---       #572
      010005D2H   LINE      CODE     ---       #574
      010005D4H   LINE      CODE     ---       #575
      010005D9H   LINE      CODE     ---       #576
      010005E0H   LINE      CODE     ---       #577
      010005E0H   LINE      CODE     ---       #578
      010005E0H   LINE      CODE     ---       #580
      010005EFH   LINE      CODE     ---       #583
      010005FAH   LINE      CODE     ---       #584
      010005FAH   LINE      CODE     ---       #585
      01000600H   LINE      CODE     ---       #586
      01000600H   LINE      CODE     ---       #587
      0100060DH   LINE      CODE     ---       #588
      0100060DH   LINE      CODE     ---       #589
      0100060FH   LINE      CODE     ---       #590
      01000611H   LINE      CODE     ---       #591
      01000619H   LINE      CODE     ---       #592
      0100061BH   LINE      CODE     ---       #594
      0100061BH   LINE      CODE     ---       #595
      01000621H   LINE      CODE     ---       #596
      01000621H   LINE      CODE     ---       #598
      01000629H   LINE      CODE     ---       #599
      01000629H   LINE      CODE     ---       #600
      0100062AH   LINE      CODE     ---       #601
      0100062EH   LINE      CODE     ---       #602
      01000631H   LINE      CODE     ---       #603
      0100063AH   LINE      CODE     ---       #604
      0100063AH   LINE      CODE     ---       #605
      0100063FH   LINE      CODE     ---       #607
      01000688H   LINE      CODE     ---       #608
      01000688H   LINE      CODE     ---       #609
      01000688H   LINE      CODE     ---       #610
      01000688H   LINE      CODE     ---       #611
      01000696H   LINE      CODE     ---       #612
      010006A3H   LINE      CODE     ---       #613
      010006A3H   LINE      CODE     ---       #614
      010006AAH   LINE      CODE     ---       #615
      010006ACH   LINE      CODE     ---       #617
      010006ACH   LINE      CODE     ---       #618
      010006AEH   LINE      CODE     ---       #619
      010006B5H   LINE      CODE     ---       #620
      010006B5H   LINE      CODE     ---       #621
      010006B5H   LINE      CODE     ---       #622
      010006B5H   LINE      CODE     ---       #623
      010006B5H   LINE      CODE     ---       #624
      010006B7H   LINE      CODE     ---       #625
      010006B7H   LINE      CODE     ---       #626
      010006C0H   LINE      CODE     ---       #627
      010006CBH   LINE      CODE     ---       #628
      010006CBH   LINE      CODE     ---       #629
      010006CDH   LINE      CODE     ---       #630
      010006CDH   LINE      CODE     ---       #631
      010006CDH   LINE      CODE     ---       #632
      010006CDH   LINE      CODE     ---       #633
      010006CDH   LINE      CODE     ---       #634
      010006CDH   LINE      CODE     ---       #635
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 102


      010006D0H   LINE      CODE     ---       #636
      010006D0H   LINE      CODE     ---       #637
      010006D0H   LINE      CODE     ---       #638
      010006DEH   LINE      CODE     ---       #639
      010006EBH   LINE      CODE     ---       #640
      010006EBH   LINE      CODE     ---       #641
      010006F1H   LINE      CODE     ---       #642
      010006F3H   LINE      CODE     ---       #644
      010006F3H   LINE      CODE     ---       #645
      010006F5H   LINE      CODE     ---       #646
      010006FCH   LINE      CODE     ---       #647
      010006FCH   LINE      CODE     ---       #648
      01000707H   LINE      CODE     ---       #649
      0100070CH   LINE      CODE     ---       #650
      0100070CH   LINE      CODE     ---       #651
      0100070FH   LINE      CODE     ---       #652
      0100070FH   LINE      CODE     ---       #653
      01000718H   LINE      CODE     ---       #654
      01000720H   LINE      CODE     ---       #655
      01000720H   LINE      CODE     ---       #656
      01000722H   LINE      CODE     ---       #657
      01000722H   LINE      CODE     ---       #658
      01000722H   LINE      CODE     ---       #659
      01000722H   LINE      CODE     ---       #660
      01000722H   LINE      CODE     ---       #661
      01000722H   LINE      CODE     ---       #662
      01000724H   LINE      CODE     ---       #663
      01000724H   LINE      CODE     ---       #664
      0100072DH   LINE      CODE     ---       #665
      01000735H   LINE      CODE     ---       #666
      01000735H   LINE      CODE     ---       #667
      01000737H   LINE      CODE     ---       #668
      01000737H   LINE      CODE     ---       #669
      0100073CH   LINE      CODE     ---       #670
      0100073CH   LINE      CODE     ---       #671
      0100073CH   LINE      CODE     ---       #672
      0100073CH   LINE      CODE     ---       #673
      0100073EH   LINE      CODE     ---       #674
      0100073EH   LINE      CODE     ---       #675
      01000747H   LINE      CODE     ---       #676
      0100074FH   LINE      CODE     ---       #677
      0100074FH   LINE      CODE     ---       #678
      01000751H   LINE      CODE     ---       #679
      01000751H   LINE      CODE     ---       #680
      01000757H   LINE      CODE     ---       #681
      01000759H   LINE      CODE     ---       #682
      01000759H   LINE      CODE     ---       #683
      01000759H   LINE      CODE     ---       #684
      0100075CH   LINE      CODE     ---       #685
      0100075CH   LINE      CODE     ---       #686
      01000762H   LINE      CODE     ---       #687
      01000764H   LINE      CODE     ---       #688
      0100076CH   LINE      CODE     ---       #689
      0100076EH   LINE      CODE     ---       #690
      01000771H   LINE      CODE     ---       #691
      01000771H   LINE      CODE     ---       #692
      01000771H   LINE      CODE     ---       #693
      0100077AH   LINE      CODE     ---       #694
      01000782H   LINE      CODE     ---       #695
      01000782H   LINE      CODE     ---       #696
      01000784H   LINE      CODE     ---       #697
      01000784H   LINE      CODE     ---       #698
      0100078AH   LINE      CODE     ---       #699
      0100078CH   LINE      CODE     ---       #700
      0100078CH   LINE      CODE     ---       #701
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 103


      0100078CH   LINE      CODE     ---       #702
      0100078CH   LINE      CODE     ---       #703
      0100078EH   LINE      CODE     ---       #704
      0100078EH   LINE      CODE     ---       #705
      0100078EH   LINE      CODE     ---       #706
      01000797H   LINE      CODE     ---       #707
      0100079FH   LINE      CODE     ---       #708
      0100079FH   LINE      CODE     ---       #709
      010007A1H   LINE      CODE     ---       #710
      010007A1H   LINE      CODE     ---       #711
      010007A7H   LINE      CODE     ---       #712
      010007A9H   LINE      CODE     ---       #713
      010007A9H   LINE      CODE     ---       #714
      010007A9H   LINE      CODE     ---       #715
      010007A9H   LINE      CODE     ---       #716
      010007ABH   LINE      CODE     ---       #717
      010007ABH   LINE      CODE     ---       #718
      010007ABH   LINE      CODE     ---       #719
      010007B4H   LINE      CODE     ---       #720
      010007BCH   LINE      CODE     ---       #721
      010007BCH   LINE      CODE     ---       #722
      010007BEH   LINE      CODE     ---       #723
      010007BEH   LINE      CODE     ---       #724
      010007C4H   LINE      CODE     ---       #725
      010007C6H   LINE      CODE     ---       #726
      010007C6H   LINE      CODE     ---       #727
      010007C6H   LINE      CODE     ---       #728
      010007C6H   LINE      CODE     ---       #729
      010007C8H   LINE      CODE     ---       #730
      010007C8H   LINE      CODE     ---       #731
      010007C8H   LINE      CODE     ---       #732
      010007D1H   LINE      CODE     ---       #733
      010007D9H   LINE      CODE     ---       #734
      010007D9H   LINE      CODE     ---       #735
      010007DBH   LINE      CODE     ---       #736
      010007DBH   LINE      CODE     ---       #737
      010007E1H   LINE      CODE     ---       #738
      010007E3H   LINE      CODE     ---       #739
      010007E5H   LINE      CODE     ---       #740
      010007EBH   LINE      CODE     ---       #741
      010007EBH   LINE      CODE     ---       #742
      010007EDH   LINE      CODE     ---       #743
      010007EDH   LINE      CODE     ---       #744
      010007EDH   LINE      CODE     ---       #745
      010007F3H   LINE      CODE     ---       #746
      010007F5H   LINE      CODE     ---       #747
      010007F5H   LINE      CODE     ---       #748
      010007F5H   LINE      CODE     ---       #749
      010007F5H   LINE      CODE     ---       #750
      010007F5H   LINE      CODE     ---       #751
      010007F5H   LINE      CODE     ---       #752
      010007F5H   LINE      CODE     ---       #753
      010007F5H   LINE      CODE     ---       #754
      010007F5H   LINE      CODE     ---       #757
      01000806H   LINE      CODE     ---       #758
      01000806H   LINE      CODE     ---       #759
      01000808H   LINE      CODE     ---       #760
      0100080AH   LINE      CODE     ---       #761
      01000818H   LINE      CODE     ---       #763
      01000824H   LINE      CODE     ---       #764
      01000824H   LINE      CODE     ---       #765
      01000826H   LINE      CODE     ---       #766
      01000828H   LINE      CODE     ---       #767
      0100082AH   LINE      CODE     ---       #768
      0100082AH   LINE      CODE     ---       #769
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 104


      01000830H   LINE      CODE     ---       #770
      01000830H   LINE      CODE     ---       #771
      01000833H   LINE      CODE     ---       #772
      0100083AH   LINE      CODE     ---       #773
      0100083CH   LINE      CODE     ---       #774
      0100083CH   LINE      CODE     ---       #775
      0100083FH   LINE      CODE     ---       #776
      0100083FH   LINE      CODE     ---       #777
      01000848H   LINE      CODE     ---       #778
      01000848H   LINE      CODE     ---       #779
      0100084BH   LINE      CODE     ---       #780
      0100084BH   LINE      CODE     ---       #782
      0100084EH   LINE      CODE     ---       #783
      0100084EH   LINE      CODE     ---       #784
      01000857H   LINE      CODE     ---       #785
      01000859H   LINE      CODE     ---       #787
      01000859H   LINE      CODE     ---       #789
      01000866H   LINE      CODE     ---       #790
      01000866H   LINE      CODE     ---       #791
      0100086EH   LINE      CODE     ---       #792
      01000870H   LINE      CODE     ---       #793
      01000876H   LINE      CODE     ---       #794
      01000876H   LINE      CODE     ---       #795
      0100087EH   LINE      CODE     ---       #796
      0100087EH   LINE      CODE     ---       #797
      0100087EH   LINE      CODE     ---       #798
      0100087EH   LINE      CODE     ---       #799
      01000881H   LINE      CODE     ---       #800
      01000881H   LINE      CODE     ---       #801
      01000887H   LINE      CODE     ---       #802
      01000887H   LINE      CODE     ---       #803
      01000898H   LINE      CODE     ---       #804
      01000898H   LINE      CODE     ---       #805
      01000898H   LINE      CODE     ---       #806
      0100089AH   LINE      CODE     ---       #808
      0100089AH   LINE      CODE     ---       #809
      010008A2H   LINE      CODE     ---       #810
      010008A9H   LINE      CODE     ---       #811
      010008AFH   LINE      CODE     ---       #812
      010008B1H   LINE      CODE     ---       #813
      010008B3H   LINE      CODE     ---       #814
      010008B5H   LINE      CODE     ---       #815
      010008B7H   LINE      CODE     ---       #816
      010008B9H   LINE      CODE     ---       #817
      010008B9H   LINE      CODE     ---       #818
      010008BCH   LINE      CODE     ---       #820
      010008BCH   LINE      CODE     ---       #821
      010008C4H   LINE      CODE     ---       #822
      010008CBH   LINE      CODE     ---       #823
      010008D1H   LINE      CODE     ---       #824
      010008D3H   LINE      CODE     ---       #825
      010008D5H   LINE      CODE     ---       #826
      010008D7H   LINE      CODE     ---       #827
      010008D9H   LINE      CODE     ---       #828
      010008D9H   LINE      CODE     ---       #829
      010008DCH   LINE      CODE     ---       #830
      010008EDH   LINE      CODE     ---       #831
      010008EDH   LINE      CODE     ---       #832
      010008F5H   LINE      CODE     ---       #833
      010008F8H   LINE      CODE     ---       #835
      010008F8H   LINE      CODE     ---       #836
      010008FEH   LINE      CODE     ---       #837
      010008FEH   LINE      CODE     ---       #838
      010008FEH   LINE      CODE     ---       #839
      010008FEH   LINE      CODE     ---       #840
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 105


      010008FEH   LINE      CODE     ---       #841
      010008FEH   LINE      CODE     ---       #842
      010008FEH   LINE      CODE     ---       #843
      010008FEH   LINE      CODE     ---       #844
      01000900H   LINE      CODE     ---       #846
      01000900H   LINE      CODE     ---       #847
      01000906H   LINE      CODE     ---       #848
      01000906H   LINE      CODE     ---       #849
      0100090DH   LINE      CODE     ---       #850
      01000910H   LINE      CODE     ---       #852
      01000910H   LINE      CODE     ---       #853
      01000918H   LINE      CODE     ---       #854
      0100091FH   LINE      CODE     ---       #855
      01000925H   LINE      CODE     ---       #856
      01000927H   LINE      CODE     ---       #857
      01000929H   LINE      CODE     ---       #858
      0100092BH   LINE      CODE     ---       #859
      0100092BH   LINE      CODE     ---       #860
      0100092BH   LINE      CODE     ---       #861
      0100092BH   LINE      CODE     ---       #862
      0100092EH   LINE      CODE     ---       #863
      01000939H   LINE      CODE     ---       #864
      01000939H   LINE      CODE     ---       #865
      0100093BH   LINE      CODE     ---       #866
      0100093DH   LINE      CODE     ---       #867
      0100093FH   LINE      CODE     ---       #868
      01000941H   LINE      CODE     ---       #869
      01000943H   LINE      CODE     ---       #870
      01000946H   LINE      CODE     ---       #871
      0100094FH   LINE      CODE     ---       #872
      0100094FH   LINE      CODE     ---       #873
      0100094FH   LINE      CODE     ---       #874
      01000956H   LINE      CODE     ---       #875
      01000956H   LINE      CODE     ---       #876
      01000958H   LINE      CODE     ---       #877
      0100095AH   LINE      CODE     ---       #878
      0100095CH   LINE      CODE     ---       #879
      0100095EH   LINE      CODE     ---       #880
      01000960H   LINE      CODE     ---       #881
      01000962H   LINE      CODE     ---       #882
      01000964H   LINE      CODE     ---       #885
      01000966H   LINE      CODE     ---       #886
      01000968H   LINE      CODE     ---       #887
      0100096AH   LINE      CODE     ---       #888
      01000970H   LINE      CODE     ---       #889
      01000976H   LINE      CODE     ---       #890
      01000978H   LINE      CODE     ---       #891
      0100097AH   LINE      CODE     ---       #892
      0100097CH   LINE      CODE     ---       #893
      0100097EH   LINE      CODE     ---       #895
      01000981H   LINE      CODE     ---       #897
      01000984H   LINE      CODE     ---       #899
      01000987H   LINE      CODE     ---       #901
      0100098AH   LINE      CODE     ---       #902
      0100098DH   LINE      CODE     ---       #903
      01000990H   LINE      CODE     ---       #904
      01000993H   LINE      CODE     ---       #905
      01000996H   LINE      CODE     ---       #906
      01000999H   LINE      CODE     ---       #907
      0100099CH   LINE      CODE     ---       #909
      010009A2H   LINE      CODE     ---       #910
      010009A2H   LINE      CODE     ---       #911
      ---         BLOCKEND  ---      ---       LVL=0

      0100215FH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 106


      00000006H   SYMBOL    DATA     WORD      z
      0100215FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      0100215FH   LINE      CODE     ---       #915
      0100215FH   LINE      CODE     ---       #916
      0100215FH   LINE      CODE     ---       #918
      01002169H   LINE      CODE     ---       #919
      0100217EH   LINE      CODE     ---       #920
      ---         BLOCKEND  ---      ---       LVL=0

      01001A28H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001A28H   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001A28H   LINE      CODE     ---       #922
      01001A28H   LINE      CODE     ---       #923
      01001A28H   LINE      CODE     ---       #926
      01001A2DH   LINE      CODE     ---       #927
      01001A2DH   LINE      CODE     ---       #928
      01001A33H   LINE      CODE     ---       #929
      01001A3DH   LINE      CODE     ---       #930
      01001A3DH   LINE      CODE     ---       #931
      01001A3FH   LINE      CODE     ---       #932
      01001A3FH   LINE      CODE     ---       #933
      01001A41H   LINE      CODE     ---       #935
      01001A41H   LINE      CODE     ---       #936
      01001A4EH   LINE      CODE     ---       #937
      01001A4EH   LINE      CODE     ---       #938
      01001A51H   LINE      CODE     ---       #939
      01001A51H   LINE      CODE     ---       #940
      01001A57H   LINE      CODE     ---       #941
      01001A57H   LINE      CODE     ---       #943
      01001A7FH   LINE      CODE     ---       #944
      01001A7FH   LINE      CODE     ---       #945
      01001A83H   LINE      CODE     ---       #946
      01001A87H   LINE      CODE     ---       #947
      01001A8DH   LINE      CODE     ---       #948
      01001A93H   LINE      CODE     ---       #949
      01001A9CH   LINE      CODE     ---       #950
      01001AA5H   LINE      CODE     ---       #951
      01001AA9H   LINE      CODE     ---       #952
      01001AB2H   LINE      CODE     ---       #953
      01001AB2H   LINE      CODE     ---       #954
      01001ABAH   LINE      CODE     ---       #956
      01001ABAH   LINE      CODE     ---       #957
      01001ABAH   LINE      CODE     ---       #958
      ---         BLOCKEND  ---      ---       LVL=0

      01001E8AH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01001E8AH   LINE      CODE     ---       #960
      01001E8AH   LINE      CODE     ---       #961
      01001E8AH   LINE      CODE     ---       #962
      01001E9AH   LINE      CODE     ---       #963
      01001E9AH   LINE      CODE     ---       #964
      01001E9AH   LINE      CODE     ---       #965
      01001EA3H   LINE      CODE     ---       #966
      01001EA9H   LINE      CODE     ---       #967
      01001EAAH   LINE      CODE     ---       #968
      01001EAAH   LINE      CODE     ---       #969
      01001EADH   LINE      CODE     ---       #970
      01001EADH   LINE      CODE     ---       #971
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 107


      01001EB2H   LINE      CODE     ---       #972
      01001EB3H   LINE      CODE     ---       #974
      01001EB3H   LINE      CODE     ---       #975
      01001EB9H   LINE      CODE     ---       #976
      01001EB9H   LINE      CODE     ---       #977
      01001EBAH   LINE      CODE     ---       #978
      01001EBAH   LINE      CODE     ---       #979
      01001EC3H   LINE      CODE     ---       #980
      01001EC9H   LINE      CODE     ---       #981
      01001ECAH   LINE      CODE     ---       #982
      01001ECAH   LINE      CODE     ---       #983
      01001ED0H   LINE      CODE     ---       #984
      01001ED0H   LINE      CODE     ---       #985
      01001ED0H   LINE      CODE     ---       #986
      01001ED0H   LINE      CODE     ---       #987
      01001ED0H   LINE      CODE     ---       #988
      ---         BLOCKEND  ---      ---       LVL=0

      010018C3H   BLOCK     CODE     ---       LVL=0
      010018C3H   BLOCK     CODE     NEAR LAB  LVL=1
      02000001H   SYMBOL    XDATA    BYTE      adc_state
      02000002H   SYMBOL    XDATA    BYTE      sample_count
      02000003H   SYMBOL    XDATA    WORD      adc_sum
      ---         BLOCKEND  ---      ---       LVL=1
      010018C3H   LINE      CODE     ---       #991
      010018C3H   LINE      CODE     ---       #992
      010018C3H   LINE      CODE     ---       #998
      010018D4H   LINE      CODE     ---       #999
      010018D4H   LINE      CODE     ---       #1000
      010018D4H   LINE      CODE     ---       #1001
      010018D9H   LINE      CODE     ---       #1002
      010018DFH   LINE      CODE     ---       #1003
      010018E0H   LINE      CODE     ---       #1005
      010018E0H   LINE      CODE     ---       #1006
      010018E9H   LINE      CODE     ---       #1007
      010018E9H   LINE      CODE     ---       #1008
      010018F8H   LINE      CODE     ---       #1009
      010018FEH   LINE      CODE     ---       #1010
      01001901H   LINE      CODE     ---       #1012
      0100190EH   LINE      CODE     ---       #1013
      0100190EH   LINE      CODE     ---       #1014
      01001925H   LINE      CODE     ---       #1015
      0100192CH   LINE      CODE     ---       #1016
      01001930H   LINE      CODE     ---       #1017
      01001936H   LINE      CODE     ---       #1018
      01001937H   LINE      CODE     ---       #1020
      01001937H   LINE      CODE     ---       #1021
      01001937H   LINE      CODE     ---       #1022
      01001937H   LINE      CODE     ---       #1023
      01001937H   LINE      CODE     ---       #1024
      01001939H   LINE      CODE     ---       #1026
      01001939H   LINE      CODE     ---       #1027
      01001949H   LINE      CODE     ---       #1028
      01001949H   LINE      CODE     ---       #1029
      0100194FH   LINE      CODE     ---       #1030
      0100195AH   LINE      CODE     ---       #1031
      0100195CH   LINE      CODE     ---       #1033
      0100195CH   LINE      CODE     ---       #1034
      0100195EH   LINE      CODE     ---       #1035
      01001963H   LINE      CODE     ---       #1036
      01001963H   LINE      CODE     ---       #1038
      0100196CH   LINE      CODE     ---       #1039
      0100196CH   LINE      CODE     ---       #1040
      01001972H   LINE      CODE     ---       #1041
      0100197DH   LINE      CODE     ---       #1042
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 108


      0100197FH   LINE      CODE     ---       #1044
      0100197FH   LINE      CODE     ---       #1045
      01001984H   LINE      CODE     ---       #1046
      01001986H   LINE      CODE     ---       #1047
      01001986H   LINE      CODE     ---       #1048
      01001986H   LINE      CODE     ---       #1049
      01001988H   LINE      CODE     ---       #1051
      01001988H   LINE      CODE     ---       #1052
      0100198DH   LINE      CODE     ---       #1053
      0100198DH   LINE      CODE     ---       #1054
      0100198DH   LINE      CODE     ---       #1055
      ---         BLOCKEND  ---      ---       LVL=0

      01001FE4H   BLOCK     CODE     ---       LVL=0
      02000046H   SYMBOL    XDATA    WORD      dly1
      01001FE4H   LINE      CODE     ---       #1057
      01001FECH   LINE      CODE     ---       #1058
      01001FECH   LINE      CODE     ---       #1059
      01001FEFH   LINE      CODE     ---       #1060
      01001FF2H   LINE      CODE     ---       #1061
      01001FFBH   LINE      CODE     ---       #1062
      0100200AH   LINE      CODE     ---       #1063
      0100200DH   LINE      CODE     ---       #1064
      01002010H   LINE      CODE     ---       #1065
      ---         BLOCKEND  ---      ---       LVL=0

      0100208BH   BLOCK     CODE     ---       LVL=0
      0100208BH   BLOCK     CODE     NEAR LAB  LVL=1
      02000046H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100208BH   LINE      CODE     ---       #1067
      0100208BH   LINE      CODE     ---       #1068
      0100208BH   LINE      CODE     ---       #1071
      0100208EH   LINE      CODE     ---       #1072
      01002091H   LINE      CODE     ---       #1073
      010020A2H   LINE      CODE     ---       #1074
      010020A5H   LINE      CODE     ---       #1075
      010020A8H   LINE      CODE     ---       #1077
      010020B0H   LINE      CODE     ---       #1078
      ---         BLOCKEND  ---      ---       LVL=0

      010016BBH   BLOCK     CODE     ---       LVL=0
      010016BBH   LINE      CODE     ---       #1080
      010016BBH   LINE      CODE     ---       #1081
      010016BBH   LINE      CODE     ---       #1083
      010016C6H   LINE      CODE     ---       #1084
      010016C6H   LINE      CODE     ---       #1086
      010016C9H   LINE      CODE     ---       #1087
      010016C9H   LINE      CODE     ---       #1089
      010016CBH   LINE      CODE     ---       #1090
      010016CEH   LINE      CODE     ---       #1091
      010016CEH   LINE      CODE     ---       #1093
      010016D5H   LINE      CODE     ---       #1094
      010016D7H   LINE      CODE     ---       #1095
      010016D9H   LINE      CODE     ---       #1097
      010016D9H   LINE      CODE     ---       #1098
      010016DBH   LINE      CODE     ---       #1099
      010016DBH   LINE      CODE     ---       #1100
      010016DDH   LINE      CODE     ---       #1102
      010016DDH   LINE      CODE     ---       #1104
      010016DFH   LINE      CODE     ---       #1105
      010016E2H   LINE      CODE     ---       #1106
      010016E2H   LINE      CODE     ---       #1108
      010016E5H   LINE      CODE     ---       #1109
      010016E5H   LINE      CODE     ---       #1110
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 109


      010016E7H   LINE      CODE     ---       #1111
      010016E9H   LINE      CODE     ---       #1113
      010016E9H   LINE      CODE     ---       #1114
      010016EBH   LINE      CODE     ---       #1115
      010016EBH   LINE      CODE     ---       #1116
      010016EDH   LINE      CODE     ---       #1118
      010016EDH   LINE      CODE     ---       #1120
      010016EFH   LINE      CODE     ---       #1121
      010016EFH   LINE      CODE     ---       #1122
      010016EFH   LINE      CODE     ---       #1123
      010016F1H   LINE      CODE     ---       #1125
      010016F1H   LINE      CODE     ---       #1127
      010016F4H   LINE      CODE     ---       #1128
      010016F4H   LINE      CODE     ---       #1129
      010016F6H   LINE      CODE     ---       #1130
      010016F9H   LINE      CODE     ---       #1131
      010016F9H   LINE      CODE     ---       #1133
      01001700H   LINE      CODE     ---       #1134
      01001702H   LINE      CODE     ---       #1135
      01001704H   LINE      CODE     ---       #1137
      01001704H   LINE      CODE     ---       #1138
      01001706H   LINE      CODE     ---       #1139
      01001706H   LINE      CODE     ---       #1140
      01001708H   LINE      CODE     ---       #1142
      01001708H   LINE      CODE     ---       #1143
      0100170BH   LINE      CODE     ---       #1144
      0100170BH   LINE      CODE     ---       #1145
      0100170DH   LINE      CODE     ---       #1146
      01001717H   LINE      CODE     ---       #1148
      01001717H   LINE      CODE     ---       #1149
      0100171EH   LINE      CODE     ---       #1150
      01001720H   LINE      CODE     ---       #1151
      01001720H   LINE      CODE     ---       #1152
      01001722H   LINE      CODE     ---       #1154
      01001722H   LINE      CODE     ---       #1155
      01001724H   LINE      CODE     ---       #1156
      0100172EH   LINE      CODE     ---       #1158
      0100172EH   LINE      CODE     ---       #1159
      01001735H   LINE      CODE     ---       #1160
      01001737H   LINE      CODE     ---       #1161
      01001737H   LINE      CODE     ---       #1162
      01001737H   LINE      CODE     ---       #1163
      01001737H   LINE      CODE     ---       #1164
      01001737H   LINE      CODE     ---       #1167
      0100173AH   LINE      CODE     ---       #1168
      0100173AH   LINE      CODE     ---       #1169
      01001748H   LINE      CODE     ---       #1170
      01001757H   LINE      CODE     ---       #1171
      01001757H   LINE      CODE     ---       #1172
      01001759H   LINE      CODE     ---       #1173
      0100175BH   LINE      CODE     ---       #1175
      0100175BH   LINE      CODE     ---       #1176
      0100175DH   LINE      CODE     ---       #1177
      0100175FH   LINE      CODE     ---       #1178
      01001766H   LINE      CODE     ---       #1179
      01001766H   LINE      CODE     ---       #1180
      01001766H   LINE      CODE     ---       #1183
      0100176CH   LINE      CODE     ---       #1184
      01001776H   LINE      CODE     ---       #1185
      01001776H   LINE      CODE     ---       #1186
      01001778H   LINE      CODE     ---       #1187
      0100177DH   LINE      CODE     ---       #1188
      0100177DH   LINE      CODE     ---       #1191
      0100178BH   LINE      CODE     ---       #1192
      0100179AH   LINE      CODE     ---       #1193
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 110


      0100179AH   LINE      CODE     ---       #1194
      0100179EH   LINE      CODE     ---       #1195
      010017A3H   LINE      CODE     ---       #1196
      010017A3H   LINE      CODE     ---       #1199
      010017ABH   LINE      CODE     ---       #1200
      010017ABH   LINE      CODE     ---       #1201
      010017B1H   LINE      CODE     ---       #1202
      010017BBH   LINE      CODE     ---       #1203
      010017BBH   LINE      CODE     ---       #1204
      010017BDH   LINE      CODE     ---       #1205
      010017C2H   LINE      CODE     ---       #1206
      010017C2H   LINE      CODE     ---       #1207
      010017C3H   LINE      CODE     ---       #1209
      010017C3H   LINE      CODE     ---       #1210
      010017C8H   LINE      CODE     ---       #1211
      010017CAH   LINE      CODE     ---       #1212
      010017CAH   LINE      CODE     ---       #1213
      ---         BLOCKEND  ---      ---       LVL=0

      010011B7H   BLOCK     CODE     ---       LVL=0
      010011B7H   LINE      CODE     ---       #1222
      010011B7H   LINE      CODE     ---       #1223
      010011B7H   LINE      CODE     ---       #1225
      010011C3H   LINE      CODE     ---       #1226
      010011C3H   LINE      CODE     ---       #1227
      010011C6H   LINE      CODE     ---       #1228
      010011C6H   LINE      CODE     ---       #1229
      010011C8H   LINE      CODE     ---       #1230
      010011CAH   LINE      CODE     ---       #1231
      010011CCH   LINE      CODE     ---       #1232
      010011CEH   LINE      CODE     ---       #1233
      010011D0H   LINE      CODE     ---       #1234
      010011D6H   LINE      CODE     ---       #1235
      010011D6H   LINE      CODE     ---       #1236
      010011D6H   LINE      CODE     ---       #1239
      010011F7H   LINE      CODE     ---       #1240
      010011F7H   LINE      CODE     ---       #1242
      010011FDH   LINE      CODE     ---       #1243
      010011FDH   LINE      CODE     ---       #1244
      01001206H   LINE      CODE     ---       #1245
      0100120CH   LINE      CODE     ---       #1246
      0100120EH   LINE      CODE     ---       #1247
      01001210H   LINE      CODE     ---       #1248
      01001212H   LINE      CODE     ---       #1249
      01001214H   LINE      CODE     ---       #1250
      01001218H   LINE      CODE     ---       #1251
      01001218H   LINE      CODE     ---       #1252
      01001218H   LINE      CODE     ---       #1255
      0100121BH   LINE      CODE     ---       #1256
      0100121BH   LINE      CODE     ---       #1257
      0100121DH   LINE      CODE     ---       #1259
      01001238H   LINE      CODE     ---       #1260
      01001238H   LINE      CODE     ---       #1262
      0100123AH   LINE      CODE     ---       #1263
      0100123CH   LINE      CODE     ---       #1264
      0100123EH   LINE      CODE     ---       #1267
      01001247H   LINE      CODE     ---       #1268
      0100124DH   LINE      CODE     ---       #1269
      0100124FH   LINE      CODE     ---       #1270
      01001251H   LINE      CODE     ---       #1271
      01001255H   LINE      CODE     ---       #1272
      0100125CH   LINE      CODE     ---       #1273
      0100125EH   LINE      CODE     ---       #1274
      0100126DH   LINE      CODE     ---       #1275
      0100126DH   LINE      CODE     ---       #1277
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 111


      01001273H   LINE      CODE     ---       #1278
      01001275H   LINE      CODE     ---       #1279
      01001277H   LINE      CODE     ---       #1280
      01001279H   LINE      CODE     ---       #1281
      0100127BH   LINE      CODE     ---       #1282
      0100127BH   LINE      CODE     ---       #1283
      0100127BH   LINE      CODE     ---       #1286
      0100129CH   LINE      CODE     ---       #1287
      0100129CH   LINE      CODE     ---       #1289
      010012A2H   LINE      CODE     ---       #1290
      010012A2H   LINE      CODE     ---       #1291
      010012ABH   LINE      CODE     ---       #1292
      010012B1H   LINE      CODE     ---       #1293
      010012B3H   LINE      CODE     ---       #1294
      010012B5H   LINE      CODE     ---       #1295
      010012B7H   LINE      CODE     ---       #1296
      010012B9H   LINE      CODE     ---       #1297
      010012BEH   LINE      CODE     ---       #1298
      010012BEH   LINE      CODE     ---       #1299
      010012BEH   LINE      CODE     ---       #1302
      010012C1H   LINE      CODE     ---       #1303
      010012C1H   LINE      CODE     ---       #1304
      010012C3H   LINE      CODE     ---       #1306
      010012DEH   LINE      CODE     ---       #1307
      010012DEH   LINE      CODE     ---       #1309
      010012E0H   LINE      CODE     ---       #1310
      010012E2H   LINE      CODE     ---       #1311
      010012E4H   LINE      CODE     ---       #1314
      010012EDH   LINE      CODE     ---       #1315
      010012F3H   LINE      CODE     ---       #1316
      010012F5H   LINE      CODE     ---       #1317
      010012F7H   LINE      CODE     ---       #1318
      010012FCH   LINE      CODE     ---       #1319
      01001303H   LINE      CODE     ---       #1320
      01001304H   LINE      CODE     ---       #1321
      01001313H   LINE      CODE     ---       #1322
      01001313H   LINE      CODE     ---       #1324
      01001319H   LINE      CODE     ---       #1325
      0100131BH   LINE      CODE     ---       #1326
      0100131DH   LINE      CODE     ---       #1327
      0100131FH   LINE      CODE     ---       #1328
      01001321H   LINE      CODE     ---       #1329
      01001321H   LINE      CODE     ---       #1330
      01001321H   LINE      CODE     ---       #1331
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      010009A5H   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000AAEH   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000B55H   PUBLIC    CODE     ---       ?C?FCASTC
      01000B50H   PUBLIC    CODE     ---       ?C?FCASTI
      01000B4BH   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000B89H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000BBEH   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000BC8H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000BA0H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000BB4H   PUBLIC    CODE     ---       ?C?FPRESULT2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  14:57:02  PAGE 112


      01000BC5H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000BD3H   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000C10H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000D1CH   PUBLIC    CODE     ---       ?C?FPADD
      01000D18H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000E3DH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      010019E3H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000F4DH   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000F73H   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000F8CH   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000FB9H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000FCBH   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01000FEDH   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01001042H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      01001094H   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      01001126H   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      01001134H   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      01001140H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      01001171H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      01001188H   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      01001191H   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=15.3 xdata=164 const=13 code=8867
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
