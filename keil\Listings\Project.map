LX51 LINKER/LOCATER V4.66.97.0                                                          07/22/2025  15:27:02  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   0022C0H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000A4H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000006H.3 BIT
C:000000H   C:000000H   C:00FFFFH   00000DH   CONST
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000024H.5 000004H.6 BIT    UNIT     BIT            ?BI?MAIN
000024H.6 000025H.2 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000025H.3 000025H.6 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000025H.7 000026H.2 000000H.4 BIT    UNIT     BIT            _BIT_GROUP_
000026H.3 000026H   000000H.5 ---    ---      **GAP**
000027H   000027H   000001H   BYTE   UNIT     IDATA          ?STACK

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 3


* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000028H   000003H   BYTE   UNIT     CODE           ?PR?ADC_GETRESULT?ADC_USED
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000038H   000038H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000039H   000039H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
00003AH   00003AH   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   00003EH   000001H   BYTE   UNIT     CODE           ?PR?ADC_CLEARCONVERTINTFLAG?ADC_USED
00003FH   000042H   000004H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008FH   00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCONVERTINTFLAG?ADC_USED
000090H   000092H   000003H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   0009C1H   00090CH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0009C2H   0011D3H   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
0011D4H   00133EH   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
00133FH   001494H   000156H   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
001495H   0015BEH   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 4


0015BFH   0016D7H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
0016D8H   0017E7H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
0017E8H   0018DFH   0000F8H   BYTE   UNIT     CODE           ?C_INITSEG
0018E0H   0019AAH   0000CBH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
0019ABH   001A44H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001A45H   001AD7H   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001AD8H   001B4FH   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001B50H   001BC7H   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001BC8H   001C3AH   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
001C3BH   001CAAH   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
001CABH   001D09H   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
001D0AH   001D66H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
001D67H   001DC1H   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
001DC2H   001E10H   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
001E11H   001E5CH   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
001E5DH   001EA6H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001EA7H   001EEDH   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
001EEEH   001F2CH   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
001F2DH   001F66H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
001F67H   001F9BH   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001F9CH   001FCFH   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001FD0H   002000H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
002001H   00202DH   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
00202EH   002057H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
002058H   002080H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
002081H   0020A7H   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
0020A8H   0020CDH   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
0020CEH   0020F2H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
0020F3H   002117H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
002118H   00213BH   000024H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
00213CH   00215BH   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
00215CH   00217BH   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
00217CH   00219BH   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
00219CH   0021BAH   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
0021BBH   0021D9H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
0021DAH   0021F8H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
0021F9H   002216H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
002217H   002233H   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
002234H   00224DH   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
00224EH   002263H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
002264H   002277H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
002278H   00228AH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
00228BH   00229DH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
00229EH   0022B0H   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
0022B1H   0022C1H   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
0022C2H   0022CAH   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
0022CBH   0022D3H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
0022D4H   0022DCH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
0022DDH   0022E5H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_STARTCONVERT?ADC_USED
0022E6H   0022EDH   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
0022EEH   0022F3H   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
0022F4H   0022F9H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
0022FAH   002306H   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000034H   000035H   BYTE   UNIT     XDATA          ?XD?MAIN
000035H   000058H   000024H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000059H   000079H   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
00007AH   00008EH   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
00008FH   000099H   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
00009AH   00009FH   000006H   BYTE   UNIT     XDATA          ?XD?KEY
0000A0H   0000A3H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 7


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
   *DEL*:           00000FH   BYTE   UNIT     XDATA          ?XD?_ADC?ADC_USED
   *DEL*:           00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP
======================================================================
?C_C51STARTUP                                 ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     25H.7 26H.2  0035H 0045H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _TMR_STOP/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_START/TIMER
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 8


_DELAY1MS/MAIN                                ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----

_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  0046H 004BH
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  0046H 004BH
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 9


  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  0046H 0047H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  0046H 0047H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  0046H 0053H
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  0054H 0058H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----
  +--> _ADC_STARTCONVERT/ADC_USED
  +--> ADC_GETCONVERTINTFLAG/ADC_USED
  +--> ADC_GETRESULT/ADC_USED
  +--> ADC_CLEARCONVERTINTFLAG/ADC_USED

_ADC_STARTCONVERT/ADC_USED                    ----- -----  ----- -----
  +--> _ADC_ENABLECHANNEL/ADC

ADC_GETCONVERTINTFLAG/ADC_USED                ----- -----  ----- -----

ADC_GETRESULT/ADC_USED                        ----- -----  ----- -----
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----

ADC_CLEARCONVERTINTFLAG/ADC_USED              ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----

_TMR_STOP/TIMER                               ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 10


  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 11



*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      02000054H   XDATA    BYTE      ?_UART_Send_String?BYTE
      010011AEH   CODE     ---       ?C?CCASE
      01000FA9H   CODE     ---       ?C?CLDOPTR
      01000F90H   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 12


      01000F6AH   CODE     ---       ?C?COPY
      01000FE8H   CODE     ---       ?C?CSTOPTR
      01000FD6H   CODE     ---       ?C?CSTPTR
      01000B72H   CODE     ---       ?C?FCASTC
      01000B6DH   CODE     ---       ?C?FCASTI
      01000B68H   CODE     ---       ?C?FCASTL
      01000D39H   CODE     ---       ?C?FPADD
      01000C2DH   CODE     ---       ?C?FPCONVERT
      01000ACBH   CODE     ---       ?C?FPDIV
      01000BA6H   CODE     ---       ?C?FPGETOPN2
      010009C2H   CODE     ---       ?C?FPMUL
      01000BDBH   CODE     ---       ?C?FPNANRESULT
      01000BE5H   CODE     ---       ?C?FPOVERFLOW
      01000BBDH   CODE     ---       ?C?FPRESULT
      01000BD1H   CODE     ---       ?C?FPRESULT2
      01000BF0H   CODE     ---       ?C?FPROUND
      01000D35H   CODE     ---       ?C?FPSUB
      01000BE2H   CODE     ---       ?C?FPUNDERFLOW
      01000E5AH   CODE     ---       ?C?FTNPWR
      0100105FH   CODE     ---       ?C?ILDIX
      01001143H   CODE     ---       ?C?LNEG
      0100115DH   CODE     ---       ?C?LSTKXDATA
      01001151H   CODE     ---       ?C?LSTXDATA
      0100118EH   CODE     ---       ?C?PLDIXDATA
      010011A5H   CODE     ---       ?C?PSTXDATA
      0100100AH   CODE     ---       ?C?UIDIV
      010010B1H   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      01001A00H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      010022C2H   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      0100224EH   CODE     ---       _ADC_ConfigRunMode
      010021F9H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      010022DDH   CODE     ---       _ADC_StartConvert
      0100217CH   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 13


*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01001FD0H   CODE     ---       _FLASH_Erase
      01001F9CH   CODE     ---       _FLASH_Read
      01001F67H   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001BC8H   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01001EA7H   CODE     ---       _Key_Function_Switch_System
      01001A45H   CODE     ---       _Motor_Step_Control
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
      01002001H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01001E11H   CODE     ---       _TMR_ConfigRunMode
      01001E5DH   CODE     ---       _TMR_ConfigTimerClk
      01002081H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      01002217H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      0100219CH   CODE     ---       _TMR_Start
      010021BBH   CODE     ---       _TMR_Stop
      010021DAH   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      010022CBH   CODE     ---       _UART_ConfigBRTClk
      010022D4H   CODE     ---       _UART_ConfigBRTPeriod
      01001CABH   CODE     ---       _UART_ConfigRunMode
      0100213CH   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      01002278H   CODE     ---       _UART_EnableDoubleFrequency
      010022B1H   CODE     ---       _UART_EnableInt
      0100228BH   CODE     ---       _UART_EnableReceive
      01002264H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      010020F3H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01001D0AH   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000030H   CODE     ---       ACMP_IRQHandler
      0100003EH   CODE     ---       ADC_ClearConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      01002234H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      010020CEH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
      01000086H   CODE     ---       ADC_GetConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000026H   CODE     ---       ADC_GetResult
      01000037H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.3 BIT      BIT       auto_rotate_flash
      02000014H   XDATA    WORD      auto_rotate_flash_timer
      00000022H.0 BIT      BIT       auto_rotate_mode
      00000020H.7 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000022H.1 BIT      BIT       batlow1
      0200002FH   XDATA    BYTE      batlow1_cnt
      0200001DH   XDATA    BYTE      batlow_cnt
      0200008AH   XDATA    WORD      Battery_ADC_Wait_Time
      010018E0H   CODE     ---       Battery_Check
      0200001CH   XDATA    BYTE      battery_check_divider
      02000030H   XDATA    WORD      BatV
      00000023H.1 BIT      BIT       Bit_1_ms_Buff
      00000024H.1 BIT      BIT       Bit_N_ms_Buff
      00000023H.5 BIT      BIT       Bit_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 15


*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000025H.4 BIT      BIT       Center_Line_Control
      00000021H.7 BIT      BIT       Charg_State_Buff
      00000023H.3 BIT      BIT       charge_flash
      02000005H   XDATA    WORD      charge_flash_cnt
      00000022H.7 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      010022F4H   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      0200001EH   XDATA    INT       Count_1_Degree_Pulse
      02000085H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      02000079H   XDATA    BYTE      Data_Length
      00000023H.4 BIT      BIT       Delay_Open
      00000025H.6 BIT      BIT       Delay_Over
      02000087H   XDATA    WORD      Delay_Time
      02000083H   XDATA    WORD      Delay_Time_Count
      00000024H.2 BIT      BIT       direction_changed
      02000024H   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000036H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000025H.5 BIT      BIT       Get_String_Buff
      02000089H   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01001C3BH   CODE     ---       GPIO_Config
      0100229EH   CODE     ---       GPIO_Key_Interrupt_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 16


      01000039H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200007CH   XDATA    BYTE      K1_cnt
      00000021H.0 BIT      BIT       K1_cnt_EN
      0200009BH   XDATA    BYTE      K1_Count
      00000024H.6 BIT      BIT       K1_Press
      0200007DH   XDATA    BYTE      K2_cnt
      00000021H.1 BIT      BIT       K2_cnt_EN
      0200009CH   XDATA    BYTE      K2_Count
      00000023H.6 BIT      BIT       k2_long_press_detected
      02000020H   XDATA    WORD      k2_long_press_timer
      00000024H.7 BIT      BIT       K2_Press
      00000022H.2 BIT      BIT       k2_released
      0200007EH   XDATA    BYTE      K3_cnt
      00000021H.2 BIT      BIT       K3_cnt_EN
      0200009DH   XDATA    BYTE      K3_Count
      00000025H.0 BIT      BIT       K3_Press
      00000022H.3 BIT      BIT       k3_released
      0200009EH   XDATA    BYTE      K4_Count
      00000025H.1 BIT      BIT       K4_Press
      0200009FH   XDATA    BYTE      K5_Count
      00000025H.2 BIT      BIT       K5_Press
      02000022H   XDATA    WORD      key1_duration
      00000022H.4 BIT      BIT       key1_handle
      00000023H.7 BIT      BIT       key1_long_started
      0200000CH   XDATA    WORD      key1_press_time
      00000023H.0 BIT      BIT       key1_pressed
      02000026H   XDATA    WORD      key3_duration
      00000022H.5 BIT      BIT       key3_handle
      00000024H.0 BIT      BIT       key3_long_started
      0200000EH   XDATA    WORD      key3_press_time
      00000023H.2 BIT      BIT       key3_pressed
      0200009AH   XDATA    BYTE      Key_Buff
      01001F2DH   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010011D4H   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001495H   CODE     ---       Key_Scan
      0200000BH   XDATA    BYTE      key_scan_divider
      00000024H.3 BIT      BIT       key_short_press_mode
      0200002EH   XDATA    BYTE      last_direction
      010016D8H   CODE     ---       LED_Control
      00000021H.6 BIT      BIT       led_flash_state
      0200001AH   XDATA    WORD      led_flash_timer
      00000022H.6 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000017H   XDATA    BYTE      ledonoff1_cnt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 17


      02000034H   XDATA    BYTE      ledonoff_cnt
      00000024H.4 BIT      BIT       longhit
      0200007AH   XDATA    WORD      longhit_cnt
      0100002FH   CODE     ---       LSE_IRQHandler
      0100002EH   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
      02000016H   XDATA    BYTE      main_loop_counter
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      0200008EH   XDATA    BYTE      Motor_Direction_Data
      00000020H.6 BIT      BIT       MOTOR_RUNNING_FLAG
      02000081H   XDATA    WORD      Motor_Speed_Data
      00000021H.5 BIT      BIT       need_led_flash
      0200007FH   XDATA    INT       Num
      020000A0H   XDATA    INT       Num_Forward_Pulse
      020000A2H   XDATA    INT       Num_Reverse_Pulse
      0200002BH   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000029H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001AD8H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001B50H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 18


*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      0100002AH   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000025H.3 BIT      BIT       Power_count_clean
      0200008CH   XDATA    WORD      Power_Off_Wait_Time
      02000032H   XDATA    WORD      precise_k2_timer
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      010020A8H   CODE     ---       Restore_dly
      010022EEH   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000028H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.5 BIT      BIT       speedup
      02000010H   XDATA    WORD      speedup_cnt
      0100003AH   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      0200002DH   XDATA    BYTE      System_Mode_Before_Charge
      0200002AH   XDATA    BYTE      System_Mode_Data
      02000007H   XDATA    DWORD     Systemclock
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 19


*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      0100133FH   CODE     ---       Timer0_IRQHandler
      01002118H   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      01000031H   CODE     ---       Timer3_IRQHandler
      01000032H   CODE     ---       Timer4_IRQHandler
      02000012H   XDATA    WORD      timer_1ms_count
      02000018H   XDATA    WORD      timer_test_counter
      00000020H.5 BIT      BIT       timer_test_enable
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      0100202EH   CODE     ---       TMR0_Config
      01002058H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      01001D67H   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01001DC2H   CODE     ---       UART_0_Config
      01001EEEH   CODE     ---       UART_1_Config
      0100215CH   CODE     ---       UART_Data_Init
      010015BFH   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 20


      010022E6H   CODE     ---       UART_EnableBRT
      02000059H   XDATA    ---       UART_Get_String
      00000021H.4 BIT      BIT       use_precise_timer
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000038H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      010019AEH   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      010019ABH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      010019B9H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      010019ABH   LINE      CODE     ---       #133
      010019ADH   LINE      CODE     ---       #134
      010019AEH   LINE      CODE     ---       #135
      010019AFH   LINE      CODE     ---       #136
      010019B1H   LINE      CODE     ---       #140
      010019B4H   LINE      CODE     ---       #141
      010019B6H   LINE      CODE     ---       #145
      010019B8H   LINE      CODE     ---       #147
      010019B9H   LINE      CODE     ---       #148
      010019BAH   LINE      CODE     ---       #149
      010019BBH   LINE      CODE     ---       #150
      010019BDH   LINE      CODE     ---       #151
      010019BFH   LINE      CODE     ---       #185
      010019C2H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      010022C2H   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      010020CEH   PUBLIC    CODE     ---       ADC_GetADCResult
      010021F9H   PUBLIC    CODE     ---       _ADC_EnableChannel
      0100224EH   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 21


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 22


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 23


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      0100224EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      0100224EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100224EH   LINE      CODE     ---       #88
      0100224EH   LINE      CODE     ---       #89
      0100224EH   LINE      CODE     ---       #90
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 24


      0100224EH   LINE      CODE     ---       #92
      01002250H   LINE      CODE     ---       #93
      01002253H   LINE      CODE     ---       #94
      01002254H   LINE      CODE     ---       #95
      01002256H   LINE      CODE     ---       #97
      01002258H   LINE      CODE     ---       #98
      0100225CH   LINE      CODE     ---       #99
      01002261H   LINE      CODE     ---       #100
      01002263H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      010021F9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      010021F9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021F9H   LINE      CODE     ---       #154
      010021F9H   LINE      CODE     ---       #155
      010021F9H   LINE      CODE     ---       #156
      010021F9H   LINE      CODE     ---       #158
      010021FBH   LINE      CODE     ---       #159
      010021FFH   LINE      CODE     ---       #160
      01002208H   LINE      CODE     ---       #161
      0100220AH   LINE      CODE     ---       #163
      0100220CH   LINE      CODE     ---       #164
      01002210H   LINE      CODE     ---       #165
      01002214H   LINE      CODE     ---       #166
      01002216H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      010020CEH   BLOCK     CODE     ---       LVL=0
      010020CEH   LINE      CODE     ---       #258
      010020CEH   LINE      CODE     ---       #259
      010020CEH   LINE      CODE     ---       #260
      010020D5H   LINE      CODE     ---       #261
      010020D5H   LINE      CODE     ---       #262
      010020E8H   LINE      CODE     ---       #263
      010020E8H   LINE      CODE     ---       #264
      010020F2H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      010022C2H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      010022C2H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022C2H   LINE      CODE     ---       #344
      010022C2H   LINE      CODE     ---       #345
      010022C2H   LINE      CODE     ---       #346
      010022C2H   LINE      CODE     ---       #348
      010022C6H   LINE      CODE     ---       #349
      010022C8H   LINE      CODE     ---       #350
      010022C9H   LINE      CODE     ---       #351
      010022CAH   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 25



      ---         MODULE    ---      ---       EPWM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 26


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 27


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 28


      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 29


      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 30


      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 31


      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 32


      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 33


      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 34


      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #358
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000057H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005BH   LINE      CODE     ---       #363
      0100005CH   LINE      CODE     ---       #364
      0100005DH   LINE      CODE     ---       #365
      0100005EH   LINE      CODE     ---       #366
      0100005FH   LINE      CODE     ---       #367
      01000060H   LINE      CODE     ---       #368
      01000061H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      010021BBH   PUBLIC    CODE     ---       _TMR_Stop
      0100219CH   PUBLIC    CODE     ---       _TMR_Start
      01002217H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      01002081H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      01001E5DH   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01001E11H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 35


      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 36


      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 37


      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001E11H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01001E11H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001E11H   LINE      CODE     ---       #74
      01001E11H   LINE      CODE     ---       #75
      01001E11H   LINE      CODE     ---       #76
      01001E11H   LINE      CODE     ---       #78
      01001E20H   LINE      CODE     ---       #79
      01001E20H   LINE      CODE     ---       #80
      01001E20H   LINE      CODE     ---       #81
      01001E22H   LINE      CODE     ---       #82
      01001E26H   LINE      CODE     ---       #83
      01001E2CH   LINE      CODE     ---       #84
      01001E2CH   LINE      CODE     ---       #85
      01001E2EH   LINE      CODE     ---       #86
      01001E2EH   LINE      CODE     ---       #87
      01001E30H   LINE      CODE     ---       #88
      01001E34H   LINE      CODE     ---       #89
      01001E41H   LINE      CODE     ---       #90
      01001E43H   LINE      CODE     ---       #91
      01001E44H   LINE      CODE     ---       #92
      01001E44H   LINE      CODE     ---       #93
      01001E46H   LINE      CODE     ---       #94
      01001E49H   LINE      CODE     ---       #95
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 38


      01001E4AH   LINE      CODE     ---       #96
      01001E4CH   LINE      CODE     ---       #97
      01001E4DH   LINE      CODE     ---       #98
      01001E4DH   LINE      CODE     ---       #99
      01001E4FH   LINE      CODE     ---       #100
      01001E53H   LINE      CODE     ---       #101
      01001E5AH   LINE      CODE     ---       #102
      01001E5CH   LINE      CODE     ---       #103
      01001E5CH   LINE      CODE     ---       #104
      01001E5CH   LINE      CODE     ---       #105
      01001E5CH   LINE      CODE     ---       #106
      01001E5CH   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      01001E5DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      01001E5DH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001E5DH   LINE      CODE     ---       #117
      01001E5DH   LINE      CODE     ---       #118
      01001E5DH   LINE      CODE     ---       #119
      01001E5DH   LINE      CODE     ---       #121
      01001E6CH   LINE      CODE     ---       #122
      01001E6CH   LINE      CODE     ---       #123
      01001E6CH   LINE      CODE     ---       #124
      01001E6EH   LINE      CODE     ---       #125
      01001E72H   LINE      CODE     ---       #126
      01001E78H   LINE      CODE     ---       #127
      01001E78H   LINE      CODE     ---       #128
      01001E7AH   LINE      CODE     ---       #129
      01001E7AH   LINE      CODE     ---       #130
      01001E7CH   LINE      CODE     ---       #131
      01001E80H   LINE      CODE     ---       #132
      01001E85H   LINE      CODE     ---       #133
      01001E87H   LINE      CODE     ---       #134
      01001E88H   LINE      CODE     ---       #135
      01001E88H   LINE      CODE     ---       #136
      01001E8AH   LINE      CODE     ---       #137
      01001E8EH   LINE      CODE     ---       #138
      01001E93H   LINE      CODE     ---       #139
      01001E93H   LINE      CODE     ---       #140
      01001E95H   LINE      CODE     ---       #141
      01001E95H   LINE      CODE     ---       #142
      01001E97H   LINE      CODE     ---       #143
      01001E9BH   LINE      CODE     ---       #144
      01001EA4H   LINE      CODE     ---       #145
      01001EA6H   LINE      CODE     ---       #146
      01001EA6H   LINE      CODE     ---       #147
      01001EA6H   LINE      CODE     ---       #148
      01001EA6H   LINE      CODE     ---       #149
      01001EA6H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01002081H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      01002081H   LINE      CODE     ---       #160
      01002081H   LINE      CODE     ---       #161
      01002081H   LINE      CODE     ---       #162
      01002090H   LINE      CODE     ---       #163
      01002090H   LINE      CODE     ---       #164
      01002090H   LINE      CODE     ---       #165
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 39


      01002092H   LINE      CODE     ---       #166
      01002094H   LINE      CODE     ---       #167
      01002095H   LINE      CODE     ---       #168
      01002095H   LINE      CODE     ---       #169
      01002097H   LINE      CODE     ---       #170
      01002099H   LINE      CODE     ---       #171
      0100209AH   LINE      CODE     ---       #172
      0100209AH   LINE      CODE     ---       #173
      0100209CH   LINE      CODE     ---       #174
      0100209EH   LINE      CODE     ---       #175
      0100209FH   LINE      CODE     ---       #176
      0100209FH   LINE      CODE     ---       #177
      010020A3H   LINE      CODE     ---       #178
      010020A7H   LINE      CODE     ---       #179
      010020A7H   LINE      CODE     ---       #180
      010020A7H   LINE      CODE     ---       #181
      010020A7H   LINE      CODE     ---       #182
      010020A7H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002217H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002217H   LINE      CODE     ---       #256
      01002217H   LINE      CODE     ---       #257
      01002217H   LINE      CODE     ---       #258
      01002226H   LINE      CODE     ---       #259
      01002226H   LINE      CODE     ---       #260
      01002226H   LINE      CODE     ---       #261
      01002228H   LINE      CODE     ---       #262
      01002229H   LINE      CODE     ---       #263
      01002229H   LINE      CODE     ---       #264
      0100222BH   LINE      CODE     ---       #265
      0100222CH   LINE      CODE     ---       #266
      0100222CH   LINE      CODE     ---       #267
      0100222FH   LINE      CODE     ---       #268
      01002230H   LINE      CODE     ---       #269
      01002230H   LINE      CODE     ---       #270
      01002233H   LINE      CODE     ---       #271
      01002233H   LINE      CODE     ---       #272
      01002233H   LINE      CODE     ---       #273
      01002233H   LINE      CODE     ---       #274
      01002233H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      0100219CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      0100219CH   LINE      CODE     ---       #368
      0100219CH   LINE      CODE     ---       #369
      0100219CH   LINE      CODE     ---       #370
      010021ABH   LINE      CODE     ---       #371
      010021ABH   LINE      CODE     ---       #372
      010021ABH   LINE      CODE     ---       #373
      010021AEH   LINE      CODE     ---       #374
      010021AFH   LINE      CODE     ---       #375
      010021AFH   LINE      CODE     ---       #376
      010021B2H   LINE      CODE     ---       #377
      010021B3H   LINE      CODE     ---       #378
      010021B3H   LINE      CODE     ---       #379
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 40


      010021B6H   LINE      CODE     ---       #380
      010021B7H   LINE      CODE     ---       #381
      010021B7H   LINE      CODE     ---       #382
      010021BAH   LINE      CODE     ---       #383
      010021BAH   LINE      CODE     ---       #384
      010021BAH   LINE      CODE     ---       #385
      010021BAH   LINE      CODE     ---       #386
      010021BAH   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      010021BBH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010021BBH   LINE      CODE     ---       #395
      010021BBH   LINE      CODE     ---       #396
      010021BBH   LINE      CODE     ---       #397
      010021CAH   LINE      CODE     ---       #398
      010021CAH   LINE      CODE     ---       #399
      010021CAH   LINE      CODE     ---       #400
      010021CDH   LINE      CODE     ---       #401
      010021CEH   LINE      CODE     ---       #402
      010021CEH   LINE      CODE     ---       #403
      010021D1H   LINE      CODE     ---       #404
      010021D2H   LINE      CODE     ---       #405
      010021D2H   LINE      CODE     ---       #406
      010021D5H   LINE      CODE     ---       #407
      010021D6H   LINE      CODE     ---       #408
      010021D6H   LINE      CODE     ---       #409
      010021D9H   LINE      CODE     ---       #410
      010021D9H   LINE      CODE     ---       #411
      010021D9H   LINE      CODE     ---       #412
      010021D9H   LINE      CODE     ---       #413
      010021D9H   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      010022D4H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 41


      010022CBH   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      010022E6H   PUBLIC    CODE     ---       UART_EnableBRT
      01002264H   PUBLIC    CODE     ---       _UART_GetBuff
      010021DAH   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      010020F3H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      010022B1H   PUBLIC    CODE     ---       _UART_EnableInt
      0100228BH   PUBLIC    CODE     ---       _UART_EnableReceive
      01002278H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      01001CABH   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 42


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 43


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001CABH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      01001CABH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 44


      01001CABH   LINE      CODE     ---       #70
      01001CABH   LINE      CODE     ---       #71
      01001CABH   LINE      CODE     ---       #72
      01001CABH   LINE      CODE     ---       #74
      01001CAEH   LINE      CODE     ---       #75
      01001CAEH   LINE      CODE     ---       #76
      01001CB0H   LINE      CODE     ---       #77
      01001CB4H   LINE      CODE     ---       #78
      01001CBBH   LINE      CODE     ---       #79
      01001CBDH   LINE      CODE     ---       #81
      01001CC0H   LINE      CODE     ---       #82
      01001CCCH   LINE      CODE     ---       #83
      01001CCCH   LINE      CODE     ---       #84
      01001CCCH   LINE      CODE     ---       #85
      01001CCCH   LINE      CODE     ---       #86
      01001CCCH   LINE      CODE     ---       #87
      01001CCFH   LINE      CODE     ---       #88
      01001CD1H   LINE      CODE     ---       #89
      01001CD1H   LINE      CODE     ---       #90
      01001CD4H   LINE      CODE     ---       #91
      01001CD6H   LINE      CODE     ---       #92
      01001CD6H   LINE      CODE     ---       #93
      01001CD9H   LINE      CODE     ---       #94
      01001CD9H   LINE      CODE     ---       #95
      01001CD9H   LINE      CODE     ---       #96
      01001CD9H   LINE      CODE     ---       #97
      01001CD9H   LINE      CODE     ---       #99
      01001CD9H   LINE      CODE     ---       #100
      01001CDEH   LINE      CODE     ---       #101
      01001CDEH   LINE      CODE     ---       #102
      01001CE0H   LINE      CODE     ---       #103
      01001CE4H   LINE      CODE     ---       #104
      01001CEDH   LINE      CODE     ---       #105
      01001CEFH   LINE      CODE     ---       #107
      01001CF2H   LINE      CODE     ---       #108
      01001CFEH   LINE      CODE     ---       #109
      01001CFEH   LINE      CODE     ---       #110
      01001CFEH   LINE      CODE     ---       #111
      01001CFEH   LINE      CODE     ---       #112
      01001CFEH   LINE      CODE     ---       #113
      01001D01H   LINE      CODE     ---       #114
      01001D02H   LINE      CODE     ---       #115
      01001D02H   LINE      CODE     ---       #116
      01001D05H   LINE      CODE     ---       #117
      01001D06H   LINE      CODE     ---       #118
      01001D06H   LINE      CODE     ---       #119
      01001D09H   LINE      CODE     ---       #120
      01001D09H   LINE      CODE     ---       #121
      01001D09H   LINE      CODE     ---       #122
      01001D09H   LINE      CODE     ---       #123
      01001D09H   LINE      CODE     ---       #124
      01001D09H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01002278H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002278H   LINE      CODE     ---       #133
      01002278H   LINE      CODE     ---       #134
      01002278H   LINE      CODE     ---       #135
      0100227EH   LINE      CODE     ---       #136
      0100227EH   LINE      CODE     ---       #137
      01002281H   LINE      CODE     ---       #138
      01002281H   LINE      CODE     ---       #139
      01002287H   LINE      CODE     ---       #140
      01002287H   LINE      CODE     ---       #141
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 45


      0100228AH   LINE      CODE     ---       #142
      0100228AH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100228BH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100228BH   LINE      CODE     ---       #280
      0100228BH   LINE      CODE     ---       #281
      0100228BH   LINE      CODE     ---       #282
      01002291H   LINE      CODE     ---       #283
      01002291H   LINE      CODE     ---       #284
      01002294H   LINE      CODE     ---       #285
      01002294H   LINE      CODE     ---       #286
      0100229AH   LINE      CODE     ---       #287
      0100229AH   LINE      CODE     ---       #288
      0100229DH   LINE      CODE     ---       #289
      0100229DH   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010022B1H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010022B1H   LINE      CODE     ---       #317
      010022B1H   LINE      CODE     ---       #318
      010022B1H   LINE      CODE     ---       #319
      010022B7H   LINE      CODE     ---       #320
      010022B7H   LINE      CODE     ---       #321
      010022B9H   LINE      CODE     ---       #322
      010022B9H   LINE      CODE     ---       #323
      010022BFH   LINE      CODE     ---       #324
      010022BFH   LINE      CODE     ---       #325
      010022C1H   LINE      CODE     ---       #326
      010022C1H   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010020F3H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      010020F3H   LINE      CODE     ---       #353
      010020F5H   LINE      CODE     ---       #354
      010020F5H   LINE      CODE     ---       #355
      010020FBH   LINE      CODE     ---       #356
      010020FBH   LINE      CODE     ---       #357
      01002105H   LINE      CODE     ---       #358
      01002105H   LINE      CODE     ---       #359
      0100210BH   LINE      CODE     ---       #360
      0100210BH   LINE      CODE     ---       #361
      01002115H   LINE      CODE     ---       #362
      01002115H   LINE      CODE     ---       #363
      01002117H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      010021DAH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010021DAH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021DAH   LINE      CODE     ---       #373
      010021DAH   LINE      CODE     ---       #374
      010021DAH   LINE      CODE     ---       #377
      010021E0H   LINE      CODE     ---       #378
      010021E0H   LINE      CODE     ---       #379
      010021E2H   LINE      CODE     ---       #380
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 46


      010021E5H   LINE      CODE     ---       #381
      010021E9H   LINE      CODE     ---       #382
      010021E9H   LINE      CODE     ---       #383
      010021EFH   LINE      CODE     ---       #384
      010021EFH   LINE      CODE     ---       #385
      010021F1H   LINE      CODE     ---       #386
      010021F4H   LINE      CODE     ---       #387
      010021F8H   LINE      CODE     ---       #388
      010021F8H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      01002264H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002264H   LINE      CODE     ---       #443
      01002264H   LINE      CODE     ---       #444
      01002264H   LINE      CODE     ---       #445
      0100226AH   LINE      CODE     ---       #446
      0100226AH   LINE      CODE     ---       #447
      0100226DH   LINE      CODE     ---       #448
      0100226DH   LINE      CODE     ---       #449
      01002275H   LINE      CODE     ---       #450
      01002275H   LINE      CODE     ---       #451
      01002277H   LINE      CODE     ---       #452
      01002277H   LINE      CODE     ---       #453
      01002277H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010022E6H   BLOCK     CODE     ---       LVL=0
      010022E6H   LINE      CODE     ---       #534
      010022E6H   LINE      CODE     ---       #535
      010022E6H   LINE      CODE     ---       #536
      010022EDH   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      010022CBH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      010022CBH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022CBH   LINE      CODE     ---       #544
      010022CBH   LINE      CODE     ---       #545
      010022CBH   LINE      CODE     ---       #546
      010022CBH   LINE      CODE     ---       #548
      010022CFH   LINE      CODE     ---       #549
      010022D1H   LINE      CODE     ---       #550
      010022D2H   LINE      CODE     ---       #551
      010022D3H   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      010022D4H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      010022D4H   LINE      CODE     ---       #560
      010022D4H   LINE      CODE     ---       #561
      010022D4H   LINE      CODE     ---       #562
      010022D9H   LINE      CODE     ---       #563
      010022DCH   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 47



      ---         MODULE    ---      ---       WDT
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 48


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 49


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01001FD0H   PUBLIC    CODE     ---       _FLASH_Erase
      01001F9CH   PUBLIC    CODE     ---       _FLASH_Read
      01001F67H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 50


      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 51


      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 52


      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      01001F67H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      01001F67H   LINE      CODE     ---       #95
      01001F69H   LINE      CODE     ---       #96
      01001F69H   LINE      CODE     ---       #97
      01001F6DH   LINE      CODE     ---       #98
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 53


      01001F6FH   LINE      CODE     ---       #99
      01001F72H   LINE      CODE     ---       #101
      01001F75H   LINE      CODE     ---       #102
      01001F75H   LINE      CODE     ---       #103
      01001F77H   LINE      CODE     ---       #104
      01001F78H   LINE      CODE     ---       #105
      01001F7DH   LINE      CODE     ---       #106
      01001F7EH   LINE      CODE     ---       #107
      01001F7FH   LINE      CODE     ---       #108
      01001F80H   LINE      CODE     ---       #109
      01001F81H   LINE      CODE     ---       #110
      01001F82H   LINE      CODE     ---       #111
      01001F83H   LINE      CODE     ---       #112
      01001F88H   LINE      CODE     ---       #113
      01001F8AH   LINE      CODE     ---       #114
      01001F8BH   LINE      CODE     ---       #116
      01001F8BH   LINE      CODE     ---       #117
      01001F90H   LINE      CODE     ---       #118
      01001F91H   LINE      CODE     ---       #119
      01001F92H   LINE      CODE     ---       #120
      01001F93H   LINE      CODE     ---       #121
      01001F94H   LINE      CODE     ---       #122
      01001F95H   LINE      CODE     ---       #123
      01001F96H   LINE      CODE     ---       #124
      01001F9BH   LINE      CODE     ---       #125
      01001F9BH   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      01001F9CH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F9CH   LINE      CODE     ---       #138
      01001F9EH   LINE      CODE     ---       #139
      01001F9EH   LINE      CODE     ---       #140
      01001FA0H   LINE      CODE     ---       #141
      01001FA3H   LINE      CODE     ---       #142
      01001FA6H   LINE      CODE     ---       #143
      01001FA6H   LINE      CODE     ---       #144
      01001FA8H   LINE      CODE     ---       #145
      01001FA9H   LINE      CODE     ---       #146
      01001FAEH   LINE      CODE     ---       #147
      01001FAFH   LINE      CODE     ---       #148
      01001FB0H   LINE      CODE     ---       #149
      01001FB1H   LINE      CODE     ---       #150
      01001FB2H   LINE      CODE     ---       #151
      01001FB3H   LINE      CODE     ---       #152
      01001FB4H   LINE      CODE     ---       #153
      01001FB9H   LINE      CODE     ---       #154
      01001FBBH   LINE      CODE     ---       #155
      01001FBDH   LINE      CODE     ---       #157
      01001FBDH   LINE      CODE     ---       #158
      01001FC2H   LINE      CODE     ---       #159
      01001FC3H   LINE      CODE     ---       #160
      01001FC4H   LINE      CODE     ---       #161
      01001FC5H   LINE      CODE     ---       #162
      01001FC6H   LINE      CODE     ---       #163
      01001FC7H   LINE      CODE     ---       #164
      01001FC8H   LINE      CODE     ---       #165
      01001FCDH   LINE      CODE     ---       #166
      01001FCDH   LINE      CODE     ---       #167
      01001FCFH   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      01001FD0H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 54


      00000004H   SYMBOL    DATA     WORD      Addr
      01001FD0H   LINE      CODE     ---       #179
      01001FD2H   LINE      CODE     ---       #180
      01001FD2H   LINE      CODE     ---       #181
      01001FD4H   LINE      CODE     ---       #182
      01001FD7H   LINE      CODE     ---       #183
      01001FDAH   LINE      CODE     ---       #184
      01001FDAH   LINE      CODE     ---       #185
      01001FDCH   LINE      CODE     ---       #186
      01001FDDH   LINE      CODE     ---       #187
      01001FE2H   LINE      CODE     ---       #188
      01001FE3H   LINE      CODE     ---       #189
      01001FE4H   LINE      CODE     ---       #190
      01001FE5H   LINE      CODE     ---       #191
      01001FE6H   LINE      CODE     ---       #192
      01001FE7H   LINE      CODE     ---       #193
      01001FE8H   LINE      CODE     ---       #194
      01001FEDH   LINE      CODE     ---       #195
      01001FEFH   LINE      CODE     ---       #196
      01001FF0H   LINE      CODE     ---       #198
      01001FF0H   LINE      CODE     ---       #199
      01001FF5H   LINE      CODE     ---       #200
      01001FF6H   LINE      CODE     ---       #201
      01001FF7H   LINE      CODE     ---       #202
      01001FF8H   LINE      CODE     ---       #203
      01001FF9H   LINE      CODE     ---       #204
      01001FFAH   LINE      CODE     ---       #205
      01001FFBH   LINE      CODE     ---       #206
      01002000H   LINE      CODE     ---       #207
      01002000H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      01002234H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 55


      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 56


      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 57


      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002234H   BLOCK     CODE     ---       LVL=0
      01002234H   LINE      CODE     ---       #65
      01002234H   LINE      CODE     ---       #66
      01002234H   LINE      CODE     ---       #68
      0100223BH   LINE      CODE     ---       #71
      01002240H   LINE      CODE     ---       #72
      01002246H   LINE      CODE     ---       #75
      0100224BH   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      0200008EH   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      0200008CH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      0200008AH   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000025H.6 PUBLIC    BIT      BIT       Delay_Over
      02000089H   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      02000087H   PUBLIC    XDATA    WORD      Delay_Time
      00000025H.5 PUBLIC    BIT      BIT       Get_String_Buff
      02000085H   PUBLIC    XDATA    INT       Count_Toggle
      02000083H   PUBLIC    XDATA    WORD      Delay_Time_Count
      02000081H   PUBLIC    XDATA    WORD      Motor_Speed_Data
      0200007FH   PUBLIC    XDATA    INT       Num
      0200007EH   PUBLIC    XDATA    BYTE      K3_cnt
      0200007DH   PUBLIC    XDATA    BYTE      K2_cnt
      0200007CH   PUBLIC    XDATA    BYTE      K1_cnt
      0200007AH   PUBLIC    XDATA    WORD      longhit_cnt
      00000025H.4 PUBLIC    BIT      BIT       Center_Line_Control
      00000025H.3 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 58


      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 59


      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 60


      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      0100229EH   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01001C3BH   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 61


      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 62


      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 63


      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C3BH   BLOCK     CODE     ---       LVL=0
      01001C3BH   LINE      CODE     ---       #42
      01001C3BH   LINE      CODE     ---       #43
      01001C3BH   LINE      CODE     ---       #44
      01001C3EH   LINE      CODE     ---       #45
      01001C41H   LINE      CODE     ---       #46
      01001C44H   LINE      CODE     ---       #47
      01001C47H   LINE      CODE     ---       #48
      01001C4AH   LINE      CODE     ---       #49
      01001C4DH   LINE      CODE     ---       #50
      01001C50H   LINE      CODE     ---       #51
      01001C53H   LINE      CODE     ---       #77
      01001C58H   LINE      CODE     ---       #78
      01001C5BH   LINE      CODE     ---       #79
      01001C62H   LINE      CODE     ---       #81
      01001C67H   LINE      CODE     ---       #82
      01001C6AH   LINE      CODE     ---       #83
      01001C71H   LINE      CODE     ---       #85
      01001C76H   LINE      CODE     ---       #86
      01001C79H   LINE      CODE     ---       #87
      01001C80H   LINE      CODE     ---       #90
      01001C85H   LINE      CODE     ---       #91
      01001C88H   LINE      CODE     ---       #92
      01001C8FH   LINE      CODE     ---       #94
      01001C94H   LINE      CODE     ---       #95
      01001C97H   LINE      CODE     ---       #96
      01001C9EH   LINE      CODE     ---       #100
      01001CA3H   LINE      CODE     ---       #101
      01001CA6H   LINE      CODE     ---       #102
      01001CA8H   LINE      CODE     ---       #105
      01001CAAH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      0100229EH   BLOCK     CODE     ---       LVL=0
      0100229EH   LINE      CODE     ---       #131
      0100229EH   LINE      CODE     ---       #132
      0100229EH   LINE      CODE     ---       #134
      010022A4H   LINE      CODE     ---       #135
      010022A7H   LINE      CODE     ---       #138
      010022ABH   LINE      CODE     ---       #139
      010022AEH   LINE      CODE     ---       #142
      010022B0H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      01002058H   PUBLIC    CODE     ---       TMR1_Config
      0100202EH   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 64


      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 65


      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 66


      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100202EH   BLOCK     CODE     ---       LVL=0
      0100202EH   LINE      CODE     ---       #11
      0100202EH   LINE      CODE     ---       #12
      0100202EH   LINE      CODE     ---       #16
      01002036H   LINE      CODE     ---       #20
      0100203CH   LINE      CODE     ---       #24
      01002045H   LINE      CODE     ---       #29
      0100204AH   LINE      CODE     ---       #34
      01002050H   LINE      CODE     ---       #35
      01002053H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      01002058H   BLOCK     CODE     ---       LVL=0
      01002058H   LINE      CODE     ---       #50
      01002058H   LINE      CODE     ---       #51
      01002058H   LINE      CODE     ---       #55
      01002061H   LINE      CODE     ---       #59
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 67


      01002068H   LINE      CODE     ---       #63
      01002071H   LINE      CODE     ---       #68
      01002076H   LINE      CODE     ---       #73
      01002079H   LINE      CODE     ---       #74
      0100207CH   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      02000054H   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01001D0AH   PUBLIC    CODE     ---       _UART_Send_String
      01001EEEH   PUBLIC    CODE     ---       UART_1_Config
      01001DC2H   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 68


      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 69


      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DC2H   BLOCK     CODE     ---       LVL=0
      01001DC2H   BLOCK     CODE     NEAR LAB  LVL=1
      02000046H   SYMBOL    XDATA    WORD      BRTValue
      02000048H   SYMBOL    XDATA    DWORD     BaudRateVlue
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 70


      ---         BLOCKEND  ---      ---       LVL=1
      01001DC2H   LINE      CODE     ---       #42
      01001DC2H   LINE      CODE     ---       #43
      01001DC2H   LINE      CODE     ---       #44
      01001DCCH   LINE      CODE     ---       #45
      01001DD4H   LINE      CODE     ---       #143
      01001DDDH   LINE      CODE     ---       #144
      01001DE2H   LINE      CODE     ---       #147
      01001DE7H   LINE      CODE     ---       #148
      01001DECH   LINE      CODE     ---       #152
      01001DF7H   LINE      CODE     ---       #153
      01001DFAH   LINE      CODE     ---       #156
      01001E00H   LINE      CODE     ---       #157
      01001E05H   LINE      CODE     ---       #159
      01001E0AH   LINE      CODE     ---       #160
      01001E0DH   LINE      CODE     ---       #161
      01001E10H   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01001EEEH   BLOCK     CODE     ---       LVL=0
      01001EEEH   BLOCK     CODE     NEAR LAB  LVL=1
      02000046H   SYMBOL    XDATA    WORD      BRTValue
      02000048H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001EEEH   LINE      CODE     ---       #223
      01001EEEH   LINE      CODE     ---       #224
      01001EEEH   LINE      CODE     ---       #225
      01001EF8H   LINE      CODE     ---       #226
      01001F00H   LINE      CODE     ---       #324
      01001F09H   LINE      CODE     ---       #325
      01001F0EH   LINE      CODE     ---       #328
      01001F13H   LINE      CODE     ---       #329
      01001F18H   LINE      CODE     ---       #333
      01001F23H   LINE      CODE     ---       #334
      01001F26H   LINE      CODE     ---       #337
      01001F2CH   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      00000001H   SYMBOL    DATA     ---       s

      01001D0AH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000055H   SYMBOL    XDATA    ---       String
      02000058H   SYMBOL    XDATA    BYTE      Length
      01001D15H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01001D0AH   LINE      CODE     ---       #408
      01001D15H   LINE      CODE     ---       #409
      01001D15H   LINE      CODE     ---       #410
      01001D17H   LINE      CODE     ---       #411
      01001D21H   LINE      CODE     ---       #412
      01001D21H   LINE      CODE     ---       #413
      01001D24H   LINE      CODE     ---       #414
      01001D24H   LINE      CODE     ---       #415
      01001D39H   LINE      CODE     ---       #416
      01001D3EH   LINE      CODE     ---       #417
      01001D41H   LINE      CODE     ---       #418
      01001D41H   LINE      CODE     ---       #419
      01001D46H   LINE      CODE     ---       #420
      01001D46H   LINE      CODE     ---       #421
      01001D5BH   LINE      CODE     ---       #422
      01001D60H   LINE      CODE     ---       #423
      01001D63H   LINE      CODE     ---       #424
      01001D63H   LINE      CODE     ---       #425
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 71


      01001D66H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ISR
      020000A2H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      020000A0H   PUBLIC    XDATA    INT       Num_Forward_Pulse
      0100003AH   PUBLIC    CODE     ---       SPI_IRQHandler
      01000039H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000038H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000037H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000036H   PUBLIC    CODE     ---       EPWM_IRQHandler
      01000032H   PUBLIC    CODE     ---       Timer4_IRQHandler
      01000031H   PUBLIC    CODE     ---       Timer3_IRQHandler
      01000030H   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002FH   PUBLIC    CODE     ---       LSE_IRQHandler
      0100002EH   PUBLIC    CODE     ---       LVD_IRQHandler
      0100002AH   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001B50H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001AD8H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000029H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      01001D67H   PUBLIC    CODE     ---       UART0_IRQHandler
      01002118H   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      0100133FH   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 72


      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 73


      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 74


      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #74
      0100000AH   LINE      CODE     ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      0100133FH   BLOCK     CODE     ---       LVL=0
      0100133FH   LINE      CODE     ---       #86
      0100134EH   LINE      CODE     ---       #88
      01001351H   LINE      CODE     ---       #89
      01001354H   LINE      CODE     ---       #91
      01001356H   LINE      CODE     ---       #92
      01001364H   LINE      CODE     ---       #93
      01001372H   LINE      CODE     ---       #94
      01001387H   LINE      CODE     ---       #96
      010013A7H   LINE      CODE     ---       #97
      010013A7H   LINE      CODE     ---       #98
      010013A7H   LINE      CODE     ---       #99
      010013A7H   LINE      CODE     ---       #100
      010013B3H   LINE      CODE     ---       #101
      010013B3H   LINE      CODE     ---       #102
      010013B3H   LINE      CODE     ---       #103
      010013B3H   LINE      CODE     ---       #104
      010013B3H   LINE      CODE     ---       #105
      010013B3H   LINE      CODE     ---       #106
      010013B5H   LINE      CODE     ---       #107
      010013B5H   LINE      CODE     ---       #108
      010013C1H   LINE      CODE     ---       #109
      010013C1H   LINE      CODE     ---       #110
      010013C1H   LINE      CODE     ---       #111
      010013C1H   LINE      CODE     ---       #112
      010013C1H   LINE      CODE     ---       #113
      010013C3H   LINE      CODE     ---       #114
      010013C3H   LINE      CODE     ---       #115
      010013C3H   LINE      CODE     ---       #116
      010013CFH   LINE      CODE     ---       #117
      010013CFH   LINE      CODE     ---       #118
      010013CFH   LINE      CODE     ---       #119
      010013CFH   LINE      CODE     ---       #120
      010013CFH   LINE      CODE     ---       #121
      010013CFH   LINE      CODE     ---       #122
      010013D1H   LINE      CODE     ---       #123
      010013D1H   LINE      CODE     ---       #124
      010013D1H   LINE      CODE     ---       #125
      010013DDH   LINE      CODE     ---       #126
      010013DDH   LINE      CODE     ---       #127
      010013DDH   LINE      CODE     ---       #128
      010013DDH   LINE      CODE     ---       #129
      010013DDH   LINE      CODE     ---       #130
      010013DDH   LINE      CODE     ---       #131
      010013DFH   LINE      CODE     ---       #132
      010013DFH   LINE      CODE     ---       #133
      010013DFH   LINE      CODE     ---       #134
      010013EBH   LINE      CODE     ---       #135
      010013EBH   LINE      CODE     ---       #136
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 75


      010013EDH   LINE      CODE     ---       #137
      010013F3H   LINE      CODE     ---       #138
      010013F3H   LINE      CODE     ---       #139
      010013F3H   LINE      CODE     ---       #140
      010013F3H   LINE      CODE     ---       #141
      010013F3H   LINE      CODE     ---       #142
      010013F3H   LINE      CODE     ---       #143
      010013F3H   LINE      CODE     ---       #145
      010013F6H   LINE      CODE     ---       #146
      010013F6H   LINE      CODE     ---       #147
      010013FFH   LINE      CODE     ---       #148
      01001401H   LINE      CODE     ---       #150
      01001401H   LINE      CODE     ---       #151
      01001406H   LINE      CODE     ---       #152
      01001406H   LINE      CODE     ---       #154
      01001413H   LINE      CODE     ---       #155
      01001413H   LINE      CODE     ---       #156
      01001415H   LINE      CODE     ---       #157
      01001423H   LINE      CODE     ---       #158
      01001425H   LINE      CODE     ---       #160
      01001425H   LINE      CODE     ---       #161
      01001427H   LINE      CODE     ---       #162
      0100142EH   LINE      CODE     ---       #163
      0100142EH   LINE      CODE     ---       #165
      01001431H   LINE      CODE     ---       #166
      01001431H   LINE      CODE     ---       #167
      0100143FH   LINE      CODE     ---       #168
      01001454H   LINE      CODE     ---       #169
      01001454H   LINE      CODE     ---       #170
      01001456H   LINE      CODE     ---       #171
      01001456H   LINE      CODE     ---       #172
      01001456H   LINE      CODE     ---       #173
      01001458H   LINE      CODE     ---       #175
      01001458H   LINE      CODE     ---       #176
      0100145FH   LINE      CODE     ---       #177
      0100145FH   LINE      CODE     ---       #180
      01001462H   LINE      CODE     ---       #181
      01001462H   LINE      CODE     ---       #182
      01001470H   LINE      CODE     ---       #183
      0100147FH   LINE      CODE     ---       #184
      0100147FH   LINE      CODE     ---       #185
      01001483H   LINE      CODE     ---       #186
      01001488H   LINE      CODE     ---       #187
      01001488H   LINE      CODE     ---       #188
      01001488H   LINE      CODE     ---       #189
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #198
      01000012H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      01002118H   BLOCK     CODE     ---       LVL=0
      01002118H   LINE      CODE     ---       #210
      0100211EH   LINE      CODE     ---       #213
      01002121H   LINE      CODE     ---       #214
      01002124H   LINE      CODE     ---       #217
      01002127H   LINE      CODE     ---       #218
      01002127H   LINE      CODE     ---       #219
      01002135H   LINE      CODE     ---       #220
      01002135H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      01001D67H   BLOCK     CODE     ---       LVL=0
      01001D67H   LINE      CODE     ---       #230
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 76


      01001D84H   LINE      CODE     ---       #232
      01001D8CH   LINE      CODE     ---       #233
      01001D8CH   LINE      CODE     ---       #234
      01001D8EH   LINE      CODE     ---       #235
      01001D93H   LINE      CODE     ---       #236
      01001DA2H   LINE      CODE     ---       #237
      01001DA7H   LINE      CODE     ---       #238
      01001DA7H   LINE      CODE     ---       #239
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #248
      0100001AH   LINE      CODE     ---       #251
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #260
      01000022H   LINE      CODE     ---       #263
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #272
      01000029H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0

      01001AD8H   BLOCK     CODE     ---       LVL=0
      01001AD8H   LINE      CODE     ---       #284
      01001AE7H   LINE      CODE     ---       #287
      01001AEAH   LINE      CODE     ---       #290
      01001AEDH   LINE      CODE     ---       #291
      01001AEDH   LINE      CODE     ---       #293
      01001AF0H   LINE      CODE     ---       #294
      01001AF0H   LINE      CODE     ---       #296
      01001AFFH   LINE      CODE     ---       #298
      01001B01H   LINE      CODE     ---       #300
      01001B03H   LINE      CODE     ---       #301
      01001B03H   LINE      CODE     ---       #302
      01001B05H   LINE      CODE     ---       #304
      01001B05H   LINE      CODE     ---       #306
      01001B08H   LINE      CODE     ---       #307
      01001B08H   LINE      CODE     ---       #309
      01001B23H   LINE      CODE     ---       #311
      01001B3FH   LINE      CODE     ---       #312
      01001B3FH   LINE      CODE     ---       #314
      01001B41H   LINE      CODE     ---       #315
      01001B41H   LINE      CODE     ---       #317
      01001B43H   LINE      CODE     ---       #318
      01001B43H   LINE      CODE     ---       #319
      01001B43H   LINE      CODE     ---       #320
      ---         BLOCKEND  ---      ---       LVL=0

      01001B50H   BLOCK     CODE     ---       LVL=0
      01001B50H   LINE      CODE     ---       #329
      01001B5FH   LINE      CODE     ---       #331
      01001B62H   LINE      CODE     ---       #333
      01001B65H   LINE      CODE     ---       #334
      01001B65H   LINE      CODE     ---       #336
      01001B68H   LINE      CODE     ---       #337
      01001B68H   LINE      CODE     ---       #338
      01001B77H   LINE      CODE     ---       #339
      01001B79H   LINE      CODE     ---       #340
      01001B7BH   LINE      CODE     ---       #341
      01001B7BH   LINE      CODE     ---       #342
      01001B7DH   LINE      CODE     ---       #344
      01001B7DH   LINE      CODE     ---       #345
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 77


      01001B80H   LINE      CODE     ---       #346
      01001B80H   LINE      CODE     ---       #347
      01001B9BH   LINE      CODE     ---       #348
      01001BB7H   LINE      CODE     ---       #349
      01001BB7H   LINE      CODE     ---       #350
      01001BB9H   LINE      CODE     ---       #351
      01001BB9H   LINE      CODE     ---       #352
      01001BBBH   LINE      CODE     ---       #353
      01001BBBH   LINE      CODE     ---       #354
      01001BBBH   LINE      CODE     ---       #355
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #364
      0100002AH   LINE      CODE     ---       #367
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #376
      0100002EH   LINE      CODE     ---       #379
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #388
      0100002FH   LINE      CODE     ---       #391
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #400
      01000030H   LINE      CODE     ---       #403
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #412
      01000031H   LINE      CODE     ---       #415
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #424
      01000032H   LINE      CODE     ---       #427
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #436
      01000036H   LINE      CODE     ---       #439
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #448
      01000037H   LINE      CODE     ---       #451
      ---         BLOCKEND  ---      ---       LVL=0

      01000038H   BLOCK     CODE     ---       LVL=0
      01000038H   LINE      CODE     ---       #460
      01000038H   LINE      CODE     ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      01000039H   BLOCK     CODE     ---       LVL=0
      01000039H   LINE      CODE     ---       #472
      01000039H   LINE      CODE     ---       #475
      ---         BLOCKEND  ---      ---       LVL=0

      0100003AH   BLOCK     CODE     ---       LVL=0
      0100003AH   LINE      CODE     ---       #484
      0100003AH   LINE      CODE     ---       #487
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 78


      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      02000079H   PUBLIC    XDATA    BYTE      Data_Length
      02000059H   PUBLIC    XDATA    ---       UART_Get_String
      010015BFH   PUBLIC    CODE     ---       UART_Data_Process
      01001BC8H   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      0100215CH   PUBLIC    CODE     ---       UART_Data_Init
      010022F4H   PUBLIC    CODE     ---       Clean_UART_Data_Length
      010022EEH   PUBLIC    CODE     ---       Return_UART_Data_Length
      0100213CH   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 79


      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 80


      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      010022FAH   SYMBOL    CONST    ---       _?ix1000
      010022FDH   SYMBOL    CONST    ---       _?ix1001
      01002300H   SYMBOL    CONST    ---       _?ix1002

      0100213CH   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 81


      00000005H   SYMBOL    DATA     BYTE      Source_Data
      0100213CH   LINE      CODE     ---       #12
      0100213CH   LINE      CODE     ---       #13
      0100213CH   LINE      CODE     ---       #14
      01002149H   LINE      CODE     ---       #15
      0100214FH   LINE      CODE     ---       #16
      01002159H   LINE      CODE     ---       #17
      01002159H   LINE      CODE     ---       #18
      0100215BH   LINE      CODE     ---       #19
      0100215BH   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      010022EEH   BLOCK     CODE     ---       LVL=0
      010022EEH   LINE      CODE     ---       #24
      010022EEH   LINE      CODE     ---       #25
      010022EEH   LINE      CODE     ---       #26
      010022F3H   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      010022F4H   BLOCK     CODE     ---       LVL=0
      010022F4H   LINE      CODE     ---       #30
      010022F4H   LINE      CODE     ---       #31
      010022F4H   LINE      CODE     ---       #32
      010022F9H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      0100215CH   BLOCK     CODE     ---       LVL=0
      0100215CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100215CH   LINE      CODE     ---       #36
      0100215CH   LINE      CODE     ---       #37
      0100215CH   LINE      CODE     ---       #39
      01002161H   LINE      CODE     ---       #40
      0100216CH   LINE      CODE     ---       #41
      0100216CH   LINE      CODE     ---       #42
      01002178H   LINE      CODE     ---       #43
      0100217BH   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001BC8H   BLOCK     CODE     ---       LVL=0
      02000046H   SYMBOL    XDATA    BYTE      CMD_No
      01001BCDH   BLOCK     CODE     NEAR LAB  LVL=1
      02000047H   SYMBOL    XDATA    ---       UART_PASS_Data
      0200004AH   SYMBOL    XDATA    ---       UART_Error_Data
      0200004DH   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001BC8H   LINE      CODE     ---       #61
      01001BCDH   LINE      CODE     ---       #62
      01001BCDH   LINE      CODE     ---       #63
      01001BE0H   LINE      CODE     ---       #64
      01001BF3H   LINE      CODE     ---       #65
      01001C06H   LINE      CODE     ---       #66
      01001C14H   LINE      CODE     ---       #67
      01001C14H   LINE      CODE     ---       #69
      01001C14H   LINE      CODE     ---       #70
      01001C14H   LINE      CODE     ---       #71
      01001C1AH   LINE      CODE     ---       #72
      01001C1AH   LINE      CODE     ---       #73
      01001C1CH   LINE      CODE     ---       #75
      01001C1CH   LINE      CODE     ---       #76
      01001C1CH   LINE      CODE     ---       #77
      01001C27H   LINE      CODE     ---       #78
      01001C27H   LINE      CODE     ---       #79
      01001C29H   LINE      CODE     ---       #81
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 82


      01001C29H   LINE      CODE     ---       #82
      01001C29H   LINE      CODE     ---       #83
      01001C3AH   LINE      CODE     ---       #86
      01001C3AH   LINE      CODE     ---       #87
      01001C3AH   LINE      CODE     ---       #88
      01001C3AH   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      010015BFH   BLOCK     CODE     ---       LVL=0
      010015BFH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      010015BFH   LINE      CODE     ---       #92
      010015BFH   LINE      CODE     ---       #93
      010015BFH   LINE      CODE     ---       #96
      010015C2H   LINE      CODE     ---       #98
      010015C7H   LINE      CODE     ---       #99
      010015C7H   LINE      CODE     ---       #100
      010015CAH   LINE      CODE     ---       #101
      010015CDH   LINE      CODE     ---       #102
      010015CDH   LINE      CODE     ---       #104
      010015D8H   LINE      CODE     ---       #105
      010015D8H   LINE      CODE     ---       #106
      010015E7H   LINE      CODE     ---       #107
      010015E7H   LINE      CODE     ---       #109
      010015E9H   LINE      CODE     ---       #110
      010015ECH   LINE      CODE     ---       #111
      010015FAH   LINE      CODE     ---       #112
      010015FAH   LINE      CODE     ---       #114
      010015FCH   LINE      CODE     ---       #115
      010015FFH   LINE      CODE     ---       #116
      0100160DH   LINE      CODE     ---       #117
      0100160DH   LINE      CODE     ---       #119
      0100160FH   LINE      CODE     ---       #120
      01001612H   LINE      CODE     ---       #121
      01001623H   LINE      CODE     ---       #122
      01001623H   LINE      CODE     ---       #124
      01001625H   LINE      CODE     ---       #125
      01001628H   LINE      CODE     ---       #126
      01001636H   LINE      CODE     ---       #127
      01001636H   LINE      CODE     ---       #129
      01001638H   LINE      CODE     ---       #130
      0100163BH   LINE      CODE     ---       #131
      01001649H   LINE      CODE     ---       #132
      01001649H   LINE      CODE     ---       #134
      0100164BH   LINE      CODE     ---       #135
      0100164EH   LINE      CODE     ---       #136
      0100165FH   LINE      CODE     ---       #137
      0100165FH   LINE      CODE     ---       #139
      01001661H   LINE      CODE     ---       #140
      01001663H   LINE      CODE     ---       #141
      01001671H   LINE      CODE     ---       #142
      01001671H   LINE      CODE     ---       #144
      01001673H   LINE      CODE     ---       #145
      01001675H   LINE      CODE     ---       #146
      01001683H   LINE      CODE     ---       #147
      01001683H   LINE      CODE     ---       #149
      01001685H   LINE      CODE     ---       #150
      01001687H   LINE      CODE     ---       #151
      01001698H   LINE      CODE     ---       #152
      01001698H   LINE      CODE     ---       #154
      0100169AH   LINE      CODE     ---       #155
      0100169CH   LINE      CODE     ---       #156
      010016AAH   LINE      CODE     ---       #157
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 83


      010016AAH   LINE      CODE     ---       #159
      010016ACH   LINE      CODE     ---       #160
      010016AEH   LINE      CODE     ---       #161
      010016BCH   LINE      CODE     ---       #162
      010016BCH   LINE      CODE     ---       #164
      010016BEH   LINE      CODE     ---       #165
      010016C0H   LINE      CODE     ---       #166
      010016CCH   LINE      CODE     ---       #167
      010016CCH   LINE      CODE     ---       #169
      010016CEH   LINE      CODE     ---       #170
      010016D0H   LINE      CODE     ---       #172
      010016D0H   LINE      CODE     ---       #174
      010016D2H   LINE      CODE     ---       #175
      010016D2H   LINE      CODE     ---       #176
      010016D2H   LINE      CODE     ---       #178
      010016D5H   LINE      CODE     ---       #180
      010016D7H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000025H.2 PUBLIC    BIT      BIT       K5_Press
      00000025H.1 PUBLIC    BIT      BIT       K4_Press
      00000025H.0 PUBLIC    BIT      BIT       K3_Press
      00000024H.7 PUBLIC    BIT      BIT       K2_Press
      00000024H.6 PUBLIC    BIT      BIT       K1_Press
      0200009FH   PUBLIC    XDATA    BYTE      K5_Count
      0200009EH   PUBLIC    XDATA    BYTE      K4_Count
      0200009DH   PUBLIC    XDATA    BYTE      K3_Count
      0200009CH   PUBLIC    XDATA    BYTE      K2_Count
      0200009BH   PUBLIC    XDATA    BYTE      K1_Count
      0200009AH   PUBLIC    XDATA    BYTE      Key_Buff
      01001F2DH   PUBLIC    CODE     ---       Key_Buff_Return
      01001495H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 84


      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 85


      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 86


      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001495H   BLOCK     CODE     ---       LVL=0
      01001495H   LINE      CODE     ---       #20
      01001495H   LINE      CODE     ---       #21
      01001495H   LINE      CODE     ---       #23
      01001498H   LINE      CODE     ---       #24
      01001498H   LINE      CODE     ---       #25
      0100149BH   LINE      CODE     ---       #26
      0100149BH   LINE      CODE     ---       #27
      010014A8H   LINE      CODE     ---       #28
      010014A8H   LINE      CODE     ---       #29
      010014AAH   LINE      CODE     ---       #30
      010014ACH   LINE      CODE     ---       #31
      010014AEH   LINE      CODE     ---       #43
      010014AEH   LINE      CODE     ---       #44
      010014B1H   LINE      CODE     ---       #45
      010014B1H   LINE      CODE     ---       #46
      010014BEH   LINE      CODE     ---       #47
      010014BEH   LINE      CODE     ---       #48
      010014C0H   LINE      CODE     ---       #49
      010014C2H   LINE      CODE     ---       #50
      010014C4H   LINE      CODE     ---       #52
      010014C4H   LINE      CODE     ---       #53
      010014CAH   LINE      CODE     ---       #54
      010014CAH   LINE      CODE     ---       #55
      010014CCH   LINE      CODE     ---       #57
      010014CCH   LINE      CODE     ---       #58
      010014D1H   LINE      CODE     ---       #59
      010014D1H   LINE      CODE     ---       #60
      010014D1H   LINE      CODE     ---       #63
      010014D4H   LINE      CODE     ---       #64
      010014D4H   LINE      CODE     ---       #65
      010014D7H   LINE      CODE     ---       #66
      010014D7H   LINE      CODE     ---       #67
      010014E4H   LINE      CODE     ---       #68
      010014E4H   LINE      CODE     ---       #69
      010014E6H   LINE      CODE     ---       #70
      010014E8H   LINE      CODE     ---       #71
      010014EAH   LINE      CODE     ---       #83
      010014EAH   LINE      CODE     ---       #84
      010014EDH   LINE      CODE     ---       #85
      010014EDH   LINE      CODE     ---       #86
      010014FAH   LINE      CODE     ---       #87
      010014FAH   LINE      CODE     ---       #88
      010014FCH   LINE      CODE     ---       #89
      010014FEH   LINE      CODE     ---       #90
      01001500H   LINE      CODE     ---       #92
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 87


      01001500H   LINE      CODE     ---       #93
      01001506H   LINE      CODE     ---       #94
      01001506H   LINE      CODE     ---       #95
      01001508H   LINE      CODE     ---       #97
      01001508H   LINE      CODE     ---       #98
      0100150DH   LINE      CODE     ---       #99
      0100150DH   LINE      CODE     ---       #100
      0100150DH   LINE      CODE     ---       #103
      01001510H   LINE      CODE     ---       #104
      01001510H   LINE      CODE     ---       #105
      01001513H   LINE      CODE     ---       #106
      01001513H   LINE      CODE     ---       #107
      01001520H   LINE      CODE     ---       #108
      01001520H   LINE      CODE     ---       #109
      01001522H   LINE      CODE     ---       #110
      01001524H   LINE      CODE     ---       #111
      01001526H   LINE      CODE     ---       #123
      01001526H   LINE      CODE     ---       #124
      01001529H   LINE      CODE     ---       #125
      01001529H   LINE      CODE     ---       #126
      01001536H   LINE      CODE     ---       #127
      01001536H   LINE      CODE     ---       #128
      01001538H   LINE      CODE     ---       #129
      0100153AH   LINE      CODE     ---       #130
      0100153CH   LINE      CODE     ---       #132
      0100153CH   LINE      CODE     ---       #133
      01001542H   LINE      CODE     ---       #134
      01001542H   LINE      CODE     ---       #135
      01001544H   LINE      CODE     ---       #137
      01001544H   LINE      CODE     ---       #138
      01001549H   LINE      CODE     ---       #139
      01001549H   LINE      CODE     ---       #140
      01001549H   LINE      CODE     ---       #143
      0100154CH   LINE      CODE     ---       #144
      0100154CH   LINE      CODE     ---       #145
      0100154FH   LINE      CODE     ---       #146
      0100154FH   LINE      CODE     ---       #147
      0100155CH   LINE      CODE     ---       #148
      0100155CH   LINE      CODE     ---       #149
      0100155EH   LINE      CODE     ---       #150
      01001560H   LINE      CODE     ---       #151
      01001562H   LINE      CODE     ---       #163
      01001562H   LINE      CODE     ---       #164
      01001565H   LINE      CODE     ---       #165
      01001565H   LINE      CODE     ---       #166
      01001572H   LINE      CODE     ---       #167
      01001572H   LINE      CODE     ---       #168
      01001574H   LINE      CODE     ---       #169
      01001576H   LINE      CODE     ---       #170
      01001578H   LINE      CODE     ---       #172
      01001578H   LINE      CODE     ---       #173
      0100157EH   LINE      CODE     ---       #174
      0100157EH   LINE      CODE     ---       #175
      01001580H   LINE      CODE     ---       #177
      01001580H   LINE      CODE     ---       #178
      01001585H   LINE      CODE     ---       #179
      01001585H   LINE      CODE     ---       #180
      01001585H   LINE      CODE     ---       #183
      01001588H   LINE      CODE     ---       #184
      01001588H   LINE      CODE     ---       #185
      0100158BH   LINE      CODE     ---       #186
      0100158BH   LINE      CODE     ---       #187
      01001598H   LINE      CODE     ---       #188
      01001598H   LINE      CODE     ---       #189
      0100159AH   LINE      CODE     ---       #190
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 88


      0100159CH   LINE      CODE     ---       #191
      0100159DH   LINE      CODE     ---       #203
      0100159DH   LINE      CODE     ---       #204
      010015A0H   LINE      CODE     ---       #205
      010015A0H   LINE      CODE     ---       #206
      010015ADH   LINE      CODE     ---       #207
      010015ADH   LINE      CODE     ---       #208
      010015AFH   LINE      CODE     ---       #209
      010015B1H   LINE      CODE     ---       #210
      010015B2H   LINE      CODE     ---       #212
      010015B2H   LINE      CODE     ---       #213
      010015B8H   LINE      CODE     ---       #214
      010015B8H   LINE      CODE     ---       #215
      010015B9H   LINE      CODE     ---       #217
      010015B9H   LINE      CODE     ---       #218
      010015BEH   LINE      CODE     ---       #219
      010015BEH   LINE      CODE     ---       #220
      010015BEH   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      01001F2DH   BLOCK     CODE     ---       LVL=0
      01001F2DH   LINE      CODE     ---       #224
      01001F2DH   LINE      CODE     ---       #225
      01001F2DH   LINE      CODE     ---       #226
      01001F32H   LINE      CODE     ---       #228
      01001F39H   LINE      CODE     ---       #229
      01001F43H   LINE      CODE     ---       #230
      01001F4DH   LINE      CODE     ---       #231
      01001F57H   LINE      CODE     ---       #232
      01001F61H   LINE      CODE     ---       #234
      01001F66H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      01000026H   PUBLIC    CODE     ---       ADC_GetResult
      0100003EH   PUBLIC    CODE     ---       ADC_ClearConvertIntFlag
      01000086H   PUBLIC    CODE     ---       ADC_GetConvertIntFlag
      010022DDH   PUBLIC    CODE     ---       _ADC_StartConvert
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 89


      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 90


      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 91


      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0200008FH   SYMBOL    XDATA    ---       filter_buffer
      02000099H   SYMBOL    XDATA    BYTE      filter_index

      010022DDH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      010022DDH   LINE      CODE     ---       #43
      010022DDH   LINE      CODE     ---       #44
      010022DDH   LINE      CODE     ---       #45
      010022DFH   LINE      CODE     ---       #46
      010022E2H   LINE      CODE     ---       #47
      010022E5H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #50
      01000086H   LINE      CODE     ---       #51
      01000086H   LINE      CODE     ---       #52
      0100008FH   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      0100003EH   BLOCK     CODE     ---       LVL=0
      0100003EH   LINE      CODE     ---       #55
      0100003EH   LINE      CODE     ---       #56
      0100003EH   LINE      CODE     ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #60
      01000026H   LINE      CODE     ---       #61
      01000026H   LINE      CODE     ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.5 PUBLIC    BIT      BIT       speedup
      00000024H.4 PUBLIC    BIT      BIT       longhit
      02000034H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.3 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.2 PUBLIC    BIT      BIT       direction_changed
      00000024H.1 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000032H   PUBLIC    XDATA    WORD      precise_k2_timer
      02000030H   PUBLIC    XDATA    WORD      BatV
      00000024H.0 PUBLIC    BIT      BIT       key3_long_started
      00000023H.7 PUBLIC    BIT      BIT       key1_long_started
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 92


      00000023H.6 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.5 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.4 PUBLIC    BIT      BIT       Delay_Open
      0200002FH   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.3 PUBLIC    BIT      BIT       charge_flash
      0200002EH   PUBLIC    XDATA    BYTE      last_direction
      00000023H.2 PUBLIC    BIT      BIT       key3_pressed
      00000023H.1 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000023H.0 PUBLIC    BIT      BIT       key1_pressed
      0200002DH   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.7 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.6 PUBLIC    BIT      BIT       ledonoff
      0200002BH   PUBLIC    XDATA    WORD      original_speed
      00000022H.5 PUBLIC    BIT      BIT       key3_handle
      0200002AH   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000028H   PUBLIC    XDATA    INT       Self_Check
      00000022H.4 PUBLIC    BIT      BIT       key1_handle
      02000026H   PUBLIC    XDATA    WORD      key3_duration
      02000024H   PUBLIC    XDATA    WORD      dly
      00000022H.3 PUBLIC    BIT      BIT       k3_released
      02000022H   PUBLIC    XDATA    WORD      key1_duration
      00000022H.2 PUBLIC    BIT      BIT       k2_released
      02000020H   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000022H.1 PUBLIC    BIT      BIT       batlow1
      0200001EH   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000022H.0 PUBLIC    BIT      BIT       auto_rotate_mode
      0200001DH   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.7 PUBLIC    BIT      BIT       Charg_State_Buff
      0200001CH   PUBLIC    XDATA    BYTE      battery_check_divider
      00000021H.6 PUBLIC    BIT      BIT       led_flash_state
      0200001AH   PUBLIC    XDATA    WORD      led_flash_timer
      02000018H   PUBLIC    XDATA    WORD      timer_test_counter
      02000017H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      02000016H   PUBLIC    XDATA    BYTE      main_loop_counter
      00000021H.5 PUBLIC    BIT      BIT       need_led_flash
      02000014H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      02000012H   PUBLIC    XDATA    WORD      timer_1ms_count
      00000021H.4 PUBLIC    BIT      BIT       use_precise_timer
      02000010H   PUBLIC    XDATA    WORD      speedup_cnt
      0200000EH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.3 PUBLIC    BIT      BIT       auto_rotate_flash
      0200000CH   PUBLIC    XDATA    WORD      key1_press_time
      0200000BH   PUBLIC    XDATA    BYTE      key_scan_divider
      00000021H.2 PUBLIC    BIT      BIT       K3_cnt_EN
      02000007H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.1 PUBLIC    BIT      BIT       K2_cnt_EN
      00000021H.0 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.6 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.5 PUBLIC    BIT      BIT       timer_test_enable
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000005H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010011D4H   PUBLIC    CODE     ---       Key_Interrupt_Process
      010016D8H   PUBLIC    CODE     ---       LED_Control
      010020A8H   PUBLIC    CODE     ---       Restore_dly
      01002001H   PUBLIC    CODE     ---       _Store_dly
      010018E0H   PUBLIC    CODE     ---       Battery_Check
      01001EA7H   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001A45H   PUBLIC    CODE     ---       _Motor_Step_Control
      0100217CH   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 93


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 94


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 95


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000025H.7 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000035H   SYMBOL    XDATA    INT       Key_Input
      02000037H   SYMBOL    XDATA    INT       Charge_Input
      02000039H   SYMBOL    XDATA    INT       Key_State
      0200003BH   SYMBOL    XDATA    INT       Key_State_Save
      0200003DH   SYMBOL    XDATA    INT       Charge_State_Save
      0200003FH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000026H.0 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000026H.1 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      02000041H   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      02000043H   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000026H.2 SYMBOL    BIT      BIT       Voltage_Low
      02000044H   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 96


      010000B6H   LINE      CODE     ---       #149
      010000B6H   LINE      CODE     ---       #150
      010000B6H   LINE      CODE     ---       #156
      010000B8H   LINE      CODE     ---       #159
      010000BAH   LINE      CODE     ---       #160
      010000C1H   LINE      CODE     ---       #163
      010000C4H   LINE      CODE     ---       #164
      010000CBH   LINE      CODE     ---       #166
      010000CEH   LINE      CODE     ---       #168
      010000D1H   LINE      CODE     ---       #169
      010000D4H   LINE      CODE     ---       #171
      010000D7H   LINE      CODE     ---       #172
      010000DAH   LINE      CODE     ---       #175
      010000DCH   LINE      CODE     ---       #177
      010000DEH   LINE      CODE     ---       #178
      010000E5H   LINE      CODE     ---       #179
      010000E9H   LINE      CODE     ---       #180
      010000EBH   LINE      CODE     ---       #181
      010000F1H   LINE      CODE     ---       #182
      010000F7H   LINE      CODE     ---       #183
      010000FDH   LINE      CODE     ---       #184
      010000FFH   LINE      CODE     ---       #185
      0100010AH   LINE      CODE     ---       #186
      0100011EH   LINE      CODE     ---       #187
      0100011EH   LINE      CODE     ---       #188
      01000127H   LINE      CODE     ---       #189
      0100012DH   LINE      CODE     ---       #190
      0100012DH   LINE      CODE     ---       #191
      0100012FH   LINE      CODE     ---       #194
      01000132H   LINE      CODE     ---       #196
      01000138H   LINE      CODE     ---       #197
      01000138H   LINE      CODE     ---       #198
      0100013BH   LINE      CODE     ---       #199
      0100013DH   LINE      CODE     ---       #201
      01000140H   LINE      CODE     ---       #202
      0100014BH   LINE      CODE     ---       #205
      01000151H   LINE      CODE     ---       #206
      01000151H   LINE      CODE     ---       #207
      0100015FH   LINE      CODE     ---       #208
      01000171H   LINE      CODE     ---       #209
      01000171H   LINE      CODE     ---       #210
      01000175H   LINE      CODE     ---       #211
      01000177H   LINE      CODE     ---       #212
      0100017DH   LINE      CODE     ---       #213
      0100017FH   LINE      CODE     ---       #214
      01000181H   LINE      CODE     ---       #215
      01000183H   LINE      CODE     ---       #216
      0100018AH   LINE      CODE     ---       #217
      01000190H   LINE      CODE     ---       #218
      01000192H   LINE      CODE     ---       #219
      01000194H   LINE      CODE     ---       #220
      0100019CH   LINE      CODE     ---       #221
      0100019EH   LINE      CODE     ---       #222
      010001A0H   LINE      CODE     ---       #223
      010001A2H   LINE      CODE     ---       #224
      010001A2H   LINE      CODE     ---       #225
      010001A5H   LINE      CODE     ---       #227
      010001A5H   LINE      CODE     ---       #228
      010001ACH   LINE      CODE     ---       #230
      010001C8H   LINE      CODE     ---       #231
      010001C8H   LINE      CODE     ---       #232
      010001CEH   LINE      CODE     ---       #233
      010001CEH   LINE      CODE     ---       #234
      010001DCH   LINE      CODE     ---       #235
      010001EDH   LINE      CODE     ---       #236
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 97


      010001EDH   LINE      CODE     ---       #237
      010001F1H   LINE      CODE     ---       #238
      010001F3H   LINE      CODE     ---       #239
      010001F5H   LINE      CODE     ---       #240
      010001FAH   LINE      CODE     ---       #241
      010001FAH   LINE      CODE     ---       #242
      010001FCH   LINE      CODE     ---       #243
      01000207H   LINE      CODE     ---       #244
      01000207H   LINE      CODE     ---       #245
      01000215H   LINE      CODE     ---       #246
      01000226H   LINE      CODE     ---       #247
      01000226H   LINE      CODE     ---       #248
      0100022AH   LINE      CODE     ---       #249
      0100022CH   LINE      CODE     ---       #250
      01000230H   LINE      CODE     ---       #251
      01000232H   LINE      CODE     ---       #252
      01000238H   LINE      CODE     ---       #253
      01000238H   LINE      CODE     ---       #254
      0100023AH   LINE      CODE     ---       #255
      01000244H   LINE      CODE     ---       #256
      01000244H   LINE      CODE     ---       #257
      0100024AH   LINE      CODE     ---       #258
      0100024CH   LINE      CODE     ---       #259
      01000250H   LINE      CODE     ---       #260
      01000256H   LINE      CODE     ---       #261
      01000258H   LINE      CODE     ---       #262
      01000258H   LINE      CODE     ---       #263
      0100025AH   LINE      CODE     ---       #265
      01000264H   LINE      CODE     ---       #266
      01000264H   LINE      CODE     ---       #267
      01000269H   LINE      CODE     ---       #268
      01000269H   LINE      CODE     ---       #269
      01000269H   LINE      CODE     ---       #271
      01000278H   LINE      CODE     ---       #272
      01000290H   LINE      CODE     ---       #273
      01000293H   LINE      CODE     ---       #275
      01000295H   LINE      CODE     ---       #276
      0100029BH   LINE      CODE     ---       #277
      010002A4H   LINE      CODE     ---       #278
      010002A7H   LINE      CODE     ---       #281
      010002B0H   LINE      CODE     ---       #282
      010002B0H   LINE      CODE     ---       #284
      010002B6H   LINE      CODE     ---       #285
      010002B6H   LINE      CODE     ---       #287
      010002B6H   LINE      CODE     ---       #289
      010002B6H   LINE      CODE     ---       #291
      010002B6H   LINE      CODE     ---       #292
      010002B6H   LINE      CODE     ---       #293
      010002B9H   LINE      CODE     ---       #294
      010002BBH   LINE      CODE     ---       #296
      010002BEH   LINE      CODE     ---       #297
      010002BEH   LINE      CODE     ---       #298
      010002CCH   LINE      CODE     ---       #301
      010002D3H   LINE      CODE     ---       #302
      010002D8H   LINE      CODE     ---       #304
      010002D8H   LINE      CODE     ---       #307
      010002DEH   LINE      CODE     ---       #310
      010002EDH   LINE      CODE     ---       #311
      010002EDH   LINE      CODE     ---       #312
      010002EFH   LINE      CODE     ---       #313
      010002F2H   LINE      CODE     ---       #314
      010002F2H   LINE      CODE     ---       #315
      010002FDH   LINE      CODE     ---       #317
      0100030EH   LINE      CODE     ---       #318
      01000326H   LINE      CODE     ---       #321
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 98


      01000335H   LINE      CODE     ---       #322
      01000335H   LINE      CODE     ---       #323
      01000337H   LINE      CODE     ---       #324
      0100033AH   LINE      CODE     ---       #325
      0100033AH   LINE      CODE     ---       #328
      0100033DH   LINE      CODE     ---       #331
      0100034AH   LINE      CODE     ---       #332
      0100034AH   LINE      CODE     ---       #334
      0100034DH   LINE      CODE     ---       #335
      0100034DH   LINE      CODE     ---       #337
      01000350H   LINE      CODE     ---       #338
      01000350H   LINE      CODE     ---       #339
      01000358H   LINE      CODE     ---       #340
      0100035AH   LINE      CODE     ---       #341
      0100035AH   LINE      CODE     ---       #342
      0100035AH   LINE      CODE     ---       #343
      0100035CH   LINE      CODE     ---       #344
      0100035EH   LINE      CODE     ---       #345
      01000364H   LINE      CODE     ---       #346
      01000364H   LINE      CODE     ---       #348
      01000367H   LINE      CODE     ---       #349
      01000367H   LINE      CODE     ---       #351
      0100036AH   LINE      CODE     ---       #352
      0100036AH   LINE      CODE     ---       #353
      01000372H   LINE      CODE     ---       #354
      01000374H   LINE      CODE     ---       #355
      01000374H   LINE      CODE     ---       #356
      01000374H   LINE      CODE     ---       #357
      01000376H   LINE      CODE     ---       #358
      01000378H   LINE      CODE     ---       #360
      01000378H   LINE      CODE     ---       #362
      0100037EH   LINE      CODE     ---       #363
      0100037EH   LINE      CODE     ---       #365
      01000386H   LINE      CODE     ---       #366
      01000388H   LINE      CODE     ---       #370
      01000388H   LINE      CODE     ---       #371
      0100038AH   LINE      CODE     ---       #372
      0100038AH   LINE      CODE     ---       #375
      01000395H   LINE      CODE     ---       #376
      01000395H   LINE      CODE     ---       #378
      01000398H   LINE      CODE     ---       #379
      01000398H   LINE      CODE     ---       #381
      0100039BH   LINE      CODE     ---       #382
      0100039BH   LINE      CODE     ---       #383
      0100039DH   LINE      CODE     ---       #384
      0100039DH   LINE      CODE     ---       #386
      010003A0H   LINE      CODE     ---       #387
      010003A0H   LINE      CODE     ---       #388
      010003A2H   LINE      CODE     ---       #389
      010003A2H   LINE      CODE     ---       #392
      010003ABH   LINE      CODE     ---       #393
      010003ABH   LINE      CODE     ---       #394
      010003ADH   LINE      CODE     ---       #395
      010003AFH   LINE      CODE     ---       #396
      010003B1H   LINE      CODE     ---       #397
      010003B9H   LINE      CODE     ---       #398
      010003BBH   LINE      CODE     ---       #399
      010003BBH   LINE      CODE     ---       #400
      010003BDH   LINE      CODE     ---       #402
      010003BDH   LINE      CODE     ---       #405
      010003C9H   LINE      CODE     ---       #406
      010003C9H   LINE      CODE     ---       #407
      010003CCH   LINE      CODE     ---       #408
      010003CCH   LINE      CODE     ---       #410
      010003CEH   LINE      CODE     ---       #411
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 99


      010003D4H   LINE      CODE     ---       #413
      010003D9H   LINE      CODE     ---       #414
      010003E2H   LINE      CODE     ---       #415
      010003E7H   LINE      CODE     ---       #416
      010003E7H   LINE      CODE     ---       #419
      010003F6H   LINE      CODE     ---       #420
      010003F6H   LINE      CODE     ---       #421
      010003F8H   LINE      CODE     ---       #422
      010003FDH   LINE      CODE     ---       #423
      010003FFH   LINE      CODE     ---       #424
      01000401H   LINE      CODE     ---       #425
      01000403H   LINE      CODE     ---       #426
      0100040BH   LINE      CODE     ---       #427
      0100040DH   LINE      CODE     ---       #428
      0100040FH   LINE      CODE     ---       #429
      01000414H   LINE      CODE     ---       #430
      01000414H   LINE      CODE     ---       #431
      01000416H   LINE      CODE     ---       #433
      01000416H   LINE      CODE     ---       #435
      01000419H   LINE      CODE     ---       #436
      01000419H   LINE      CODE     ---       #437
      0100041BH   LINE      CODE     ---       #438
      01000420H   LINE      CODE     ---       #439
      01000427H   LINE      CODE     ---       #440
      01000427H   LINE      CODE     ---       #441
      01000429H   LINE      CODE     ---       #442
      01000429H   LINE      CODE     ---       #443
      01000429H   LINE      CODE     ---       #446
      0100042FH   LINE      CODE     ---       #447
      0100042FH   LINE      CODE     ---       #448
      0100044CH   LINE      CODE     ---       #449
      0100044CH   LINE      CODE     ---       #450
      0100044FH   LINE      CODE     ---       #451
      0100044FH   LINE      CODE     ---       #453
      0100044FH   LINE      CODE     ---       #454
      0100044FH   LINE      CODE     ---       #455
      0100044FH   LINE      CODE     ---       #456
      0100044FH   LINE      CODE     ---       #457
      0100044FH   LINE      CODE     ---       #458
      0100044FH   LINE      CODE     ---       #459
      01000451H   LINE      CODE     ---       #460
      0100045CH   LINE      CODE     ---       #461
      0100045CH   LINE      CODE     ---       #463
      0100045CH   LINE      CODE     ---       #464
      0100045CH   LINE      CODE     ---       #465
      0100045CH   LINE      CODE     ---       #466
      0100045CH   LINE      CODE     ---       #468
      0100045EH   LINE      CODE     ---       #469
      01000464H   LINE      CODE     ---       #470
      01000464H   LINE      CODE     ---       #472
      01000469H   LINE      CODE     ---       #473
      01000469H   LINE      CODE     ---       #474
      01000469H   LINE      CODE     ---       #475
      01000469H   LINE      CODE     ---       #476
      01000469H   LINE      CODE     ---       #477
      01000469H   LINE      CODE     ---       #478
      01000469H   LINE      CODE     ---       #479
      0100046BH   LINE      CODE     ---       #480
      01000488H   LINE      CODE     ---       #481
      01000488H   LINE      CODE     ---       #482
      0100048BH   LINE      CODE     ---       #483
      0100048BH   LINE      CODE     ---       #485
      01000490H   LINE      CODE     ---       #486
      01000490H   LINE      CODE     ---       #487
      01000490H   LINE      CODE     ---       #488
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 100


      01000490H   LINE      CODE     ---       #489
      01000490H   LINE      CODE     ---       #490
      01000490H   LINE      CODE     ---       #491
      01000492H   LINE      CODE     ---       #492
      0100049DH   LINE      CODE     ---       #493
      0100049DH   LINE      CODE     ---       #495
      0100049FH   LINE      CODE     ---       #496
      010004A1H   LINE      CODE     ---       #497
      010004A9H   LINE      CODE     ---       #498
      010004ABH   LINE      CODE     ---       #499
      010004ADH   LINE      CODE     ---       #500
      010004B3H   LINE      CODE     ---       #501
      010004B3H   LINE      CODE     ---       #503
      010004B9H   LINE      CODE     ---       #504
      010004C2H   LINE      CODE     ---       #505
      010004C4H   LINE      CODE     ---       #506
      010004C6H   LINE      CODE     ---       #507
      010004C8H   LINE      CODE     ---       #508
      010004CFH   LINE      CODE     ---       #509
      010004CFH   LINE      CODE     ---       #510
      010004CFH   LINE      CODE     ---       #511
      010004CFH   LINE      CODE     ---       #514
      010004D5H   LINE      CODE     ---       #515
      010004D5H   LINE      CODE     ---       #516
      010004D7H   LINE      CODE     ---       #517
      010004E5H   LINE      CODE     ---       #519
      010004F6H   LINE      CODE     ---       #520
      010004F6H   LINE      CODE     ---       #521
      010004FEH   LINE      CODE     ---       #522
      01000500H   LINE      CODE     ---       #524
      01000500H   LINE      CODE     ---       #525
      01000507H   LINE      CODE     ---       #526
      01000507H   LINE      CODE     ---       #527
      01000507H   LINE      CODE     ---       #530
      0100050AH   LINE      CODE     ---       #531
      0100050AH   LINE      CODE     ---       #533
      01000518H   LINE      CODE     ---       #534
      0100052AH   LINE      CODE     ---       #535
      0100052AH   LINE      CODE     ---       #536
      0100052EH   LINE      CODE     ---       #537
      01000530H   LINE      CODE     ---       #538
      01000530H   LINE      CODE     ---       #539
      01000533H   LINE      CODE     ---       #541
      01000533H   LINE      CODE     ---       #543
      01000535H   LINE      CODE     ---       #544
      01000539H   LINE      CODE     ---       #545
      01000539H   LINE      CODE     ---       #546
      0100053CH   LINE      CODE     ---       #549
      0100053CH   LINE      CODE     ---       #550
      0100053FH   LINE      CODE     ---       #552
      0100055FH   LINE      CODE     ---       #553
      0100055FH   LINE      CODE     ---       #554
      01000561H   LINE      CODE     ---       #555
      01000564H   LINE      CODE     ---       #556
      01000581H   LINE      CODE     ---       #557
      01000581H   LINE      CODE     ---       #558
      01000589H   LINE      CODE     ---       #559
      0100058BH   LINE      CODE     ---       #560
      0100059AH   LINE      CODE     ---       #561
      0100059AH   LINE      CODE     ---       #563
      0100059DH   LINE      CODE     ---       #564
      0100059DH   LINE      CODE     ---       #565
      0100059FH   LINE      CODE     ---       #566
      010005A5H   LINE      CODE     ---       #567
      010005AAH   LINE      CODE     ---       #568
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 101


      010005B3H   LINE      CODE     ---       #569
      010005B8H   LINE      CODE     ---       #570
      010005B8H   LINE      CODE     ---       #572
      010005C7H   LINE      CODE     ---       #573
      010005C7H   LINE      CODE     ---       #574
      010005C9H   LINE      CODE     ---       #575
      010005CEH   LINE      CODE     ---       #576
      010005D7H   LINE      CODE     ---       #577
      010005D9H   LINE      CODE     ---       #578
      010005DBH   LINE      CODE     ---       #579
      010005E0H   LINE      CODE     ---       #580
      010005E0H   LINE      CODE     ---       #581
      010005E0H   LINE      CODE     ---       #582
      010005EFH   LINE      CODE     ---       #583
      010005EFH   LINE      CODE     ---       #585
      010005F1H   LINE      CODE     ---       #586
      010005F6H   LINE      CODE     ---       #587
      010005FDH   LINE      CODE     ---       #588
      010005FDH   LINE      CODE     ---       #589
      010005FDH   LINE      CODE     ---       #591
      0100060CH   LINE      CODE     ---       #594
      01000617H   LINE      CODE     ---       #595
      01000617H   LINE      CODE     ---       #596
      0100061DH   LINE      CODE     ---       #597
      0100061DH   LINE      CODE     ---       #598
      0100062AH   LINE      CODE     ---       #599
      0100062AH   LINE      CODE     ---       #600
      0100062CH   LINE      CODE     ---       #601
      0100062EH   LINE      CODE     ---       #602
      01000636H   LINE      CODE     ---       #603
      01000638H   LINE      CODE     ---       #605
      01000638H   LINE      CODE     ---       #606
      0100063EH   LINE      CODE     ---       #607
      0100063EH   LINE      CODE     ---       #609
      01000646H   LINE      CODE     ---       #610
      01000646H   LINE      CODE     ---       #611
      01000647H   LINE      CODE     ---       #612
      0100064BH   LINE      CODE     ---       #613
      0100064EH   LINE      CODE     ---       #614
      01000657H   LINE      CODE     ---       #615
      01000657H   LINE      CODE     ---       #616
      0100065CH   LINE      CODE     ---       #618
      010006A5H   LINE      CODE     ---       #619
      010006A5H   LINE      CODE     ---       #620
      010006A5H   LINE      CODE     ---       #621
      010006A5H   LINE      CODE     ---       #622
      010006B3H   LINE      CODE     ---       #623
      010006C0H   LINE      CODE     ---       #624
      010006C0H   LINE      CODE     ---       #625
      010006C7H   LINE      CODE     ---       #626
      010006C9H   LINE      CODE     ---       #628
      010006C9H   LINE      CODE     ---       #629
      010006CBH   LINE      CODE     ---       #630
      010006D2H   LINE      CODE     ---       #631
      010006D2H   LINE      CODE     ---       #632
      010006D2H   LINE      CODE     ---       #633
      010006D2H   LINE      CODE     ---       #634
      010006D2H   LINE      CODE     ---       #635
      010006D4H   LINE      CODE     ---       #636
      010006D4H   LINE      CODE     ---       #637
      010006DDH   LINE      CODE     ---       #638
      010006E8H   LINE      CODE     ---       #639
      010006E8H   LINE      CODE     ---       #640
      010006EAH   LINE      CODE     ---       #641
      010006EAH   LINE      CODE     ---       #642
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 102


      010006EAH   LINE      CODE     ---       #643
      010006EAH   LINE      CODE     ---       #644
      010006EAH   LINE      CODE     ---       #645
      010006EAH   LINE      CODE     ---       #646
      010006EDH   LINE      CODE     ---       #647
      010006EDH   LINE      CODE     ---       #648
      010006EDH   LINE      CODE     ---       #649
      010006FBH   LINE      CODE     ---       #650
      01000708H   LINE      CODE     ---       #651
      01000708H   LINE      CODE     ---       #652
      0100070EH   LINE      CODE     ---       #653
      01000710H   LINE      CODE     ---       #655
      01000710H   LINE      CODE     ---       #656
      01000712H   LINE      CODE     ---       #657
      01000719H   LINE      CODE     ---       #658
      01000719H   LINE      CODE     ---       #659
      01000724H   LINE      CODE     ---       #660
      01000729H   LINE      CODE     ---       #661
      01000729H   LINE      CODE     ---       #662
      0100072CH   LINE      CODE     ---       #663
      0100072CH   LINE      CODE     ---       #664
      01000735H   LINE      CODE     ---       #665
      0100073DH   LINE      CODE     ---       #666
      0100073DH   LINE      CODE     ---       #667
      0100073FH   LINE      CODE     ---       #668
      0100073FH   LINE      CODE     ---       #669
      0100073FH   LINE      CODE     ---       #670
      0100073FH   LINE      CODE     ---       #671
      0100073FH   LINE      CODE     ---       #672
      0100073FH   LINE      CODE     ---       #673
      01000741H   LINE      CODE     ---       #674
      01000741H   LINE      CODE     ---       #675
      0100074AH   LINE      CODE     ---       #676
      01000752H   LINE      CODE     ---       #677
      01000752H   LINE      CODE     ---       #678
      01000754H   LINE      CODE     ---       #679
      01000754H   LINE      CODE     ---       #680
      01000759H   LINE      CODE     ---       #681
      01000759H   LINE      CODE     ---       #682
      01000759H   LINE      CODE     ---       #683
      01000759H   LINE      CODE     ---       #684
      0100075BH   LINE      CODE     ---       #685
      0100075BH   LINE      CODE     ---       #686
      01000764H   LINE      CODE     ---       #687
      0100076CH   LINE      CODE     ---       #688
      0100076CH   LINE      CODE     ---       #689
      0100076EH   LINE      CODE     ---       #690
      0100076EH   LINE      CODE     ---       #691
      01000774H   LINE      CODE     ---       #692
      01000776H   LINE      CODE     ---       #693
      01000776H   LINE      CODE     ---       #694
      01000776H   LINE      CODE     ---       #695
      01000779H   LINE      CODE     ---       #696
      01000779H   LINE      CODE     ---       #697
      0100077FH   LINE      CODE     ---       #698
      01000781H   LINE      CODE     ---       #699
      01000789H   LINE      CODE     ---       #700
      0100078BH   LINE      CODE     ---       #701
      0100078EH   LINE      CODE     ---       #702
      0100078EH   LINE      CODE     ---       #703
      0100078EH   LINE      CODE     ---       #704
      01000797H   LINE      CODE     ---       #705
      0100079FH   LINE      CODE     ---       #706
      0100079FH   LINE      CODE     ---       #707
      010007A1H   LINE      CODE     ---       #708
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 103


      010007A1H   LINE      CODE     ---       #709
      010007A7H   LINE      CODE     ---       #710
      010007A9H   LINE      CODE     ---       #711
      010007A9H   LINE      CODE     ---       #712
      010007A9H   LINE      CODE     ---       #713
      010007A9H   LINE      CODE     ---       #714
      010007ABH   LINE      CODE     ---       #715
      010007ABH   LINE      CODE     ---       #716
      010007ABH   LINE      CODE     ---       #717
      010007B4H   LINE      CODE     ---       #718
      010007BCH   LINE      CODE     ---       #719
      010007BCH   LINE      CODE     ---       #720
      010007BEH   LINE      CODE     ---       #721
      010007BEH   LINE      CODE     ---       #722
      010007C4H   LINE      CODE     ---       #723
      010007C6H   LINE      CODE     ---       #724
      010007C6H   LINE      CODE     ---       #725
      010007C6H   LINE      CODE     ---       #726
      010007C6H   LINE      CODE     ---       #727
      010007C8H   LINE      CODE     ---       #728
      010007C8H   LINE      CODE     ---       #729
      010007C8H   LINE      CODE     ---       #730
      010007D1H   LINE      CODE     ---       #731
      010007D9H   LINE      CODE     ---       #732
      010007D9H   LINE      CODE     ---       #733
      010007DBH   LINE      CODE     ---       #734
      010007DBH   LINE      CODE     ---       #735
      010007E1H   LINE      CODE     ---       #736
      010007E3H   LINE      CODE     ---       #737
      010007E3H   LINE      CODE     ---       #738
      010007E3H   LINE      CODE     ---       #739
      010007E3H   LINE      CODE     ---       #740
      010007E5H   LINE      CODE     ---       #741
      010007E5H   LINE      CODE     ---       #742
      010007E5H   LINE      CODE     ---       #743
      010007EEH   LINE      CODE     ---       #744
      010007F6H   LINE      CODE     ---       #745
      010007F6H   LINE      CODE     ---       #746
      010007F8H   LINE      CODE     ---       #747
      010007F8H   LINE      CODE     ---       #748
      010007FEH   LINE      CODE     ---       #749
      01000800H   LINE      CODE     ---       #750
      01000802H   LINE      CODE     ---       #751
      01000808H   LINE      CODE     ---       #752
      01000808H   LINE      CODE     ---       #753
      0100080AH   LINE      CODE     ---       #754
      0100080AH   LINE      CODE     ---       #755
      0100080AH   LINE      CODE     ---       #756
      01000810H   LINE      CODE     ---       #757
      01000812H   LINE      CODE     ---       #758
      01000812H   LINE      CODE     ---       #759
      01000812H   LINE      CODE     ---       #760
      01000812H   LINE      CODE     ---       #761
      01000812H   LINE      CODE     ---       #762
      01000812H   LINE      CODE     ---       #763
      01000812H   LINE      CODE     ---       #764
      01000812H   LINE      CODE     ---       #765
      01000812H   LINE      CODE     ---       #768
      01000823H   LINE      CODE     ---       #769
      01000823H   LINE      CODE     ---       #770
      01000825H   LINE      CODE     ---       #771
      01000827H   LINE      CODE     ---       #772
      01000835H   LINE      CODE     ---       #774
      01000841H   LINE      CODE     ---       #775
      01000841H   LINE      CODE     ---       #776
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 104


      01000843H   LINE      CODE     ---       #777
      01000845H   LINE      CODE     ---       #778
      01000847H   LINE      CODE     ---       #779
      01000847H   LINE      CODE     ---       #780
      0100084DH   LINE      CODE     ---       #781
      0100084DH   LINE      CODE     ---       #782
      01000850H   LINE      CODE     ---       #783
      01000857H   LINE      CODE     ---       #784
      01000859H   LINE      CODE     ---       #785
      01000859H   LINE      CODE     ---       #786
      0100085CH   LINE      CODE     ---       #787
      0100085CH   LINE      CODE     ---       #788
      01000865H   LINE      CODE     ---       #789
      01000865H   LINE      CODE     ---       #790
      01000868H   LINE      CODE     ---       #791
      01000868H   LINE      CODE     ---       #793
      0100086BH   LINE      CODE     ---       #794
      0100086BH   LINE      CODE     ---       #795
      01000874H   LINE      CODE     ---       #796
      01000876H   LINE      CODE     ---       #798
      01000876H   LINE      CODE     ---       #800
      01000883H   LINE      CODE     ---       #801
      01000883H   LINE      CODE     ---       #802
      0100088BH   LINE      CODE     ---       #803
      0100088DH   LINE      CODE     ---       #804
      01000893H   LINE      CODE     ---       #805
      01000893H   LINE      CODE     ---       #806
      0100089BH   LINE      CODE     ---       #807
      0100089BH   LINE      CODE     ---       #808
      0100089BH   LINE      CODE     ---       #809
      0100089BH   LINE      CODE     ---       #810
      0100089EH   LINE      CODE     ---       #811
      0100089EH   LINE      CODE     ---       #812
      010008A4H   LINE      CODE     ---       #813
      010008A4H   LINE      CODE     ---       #814
      010008B5H   LINE      CODE     ---       #815
      010008B5H   LINE      CODE     ---       #816
      010008B5H   LINE      CODE     ---       #817
      010008B7H   LINE      CODE     ---       #819
      010008B7H   LINE      CODE     ---       #820
      010008BFH   LINE      CODE     ---       #821
      010008C6H   LINE      CODE     ---       #822
      010008CCH   LINE      CODE     ---       #823
      010008CEH   LINE      CODE     ---       #824
      010008D0H   LINE      CODE     ---       #825
      010008D2H   LINE      CODE     ---       #826
      010008D4H   LINE      CODE     ---       #827
      010008D6H   LINE      CODE     ---       #828
      010008D6H   LINE      CODE     ---       #829
      010008D9H   LINE      CODE     ---       #831
      010008D9H   LINE      CODE     ---       #832
      010008E1H   LINE      CODE     ---       #833
      010008E8H   LINE      CODE     ---       #834
      010008EEH   LINE      CODE     ---       #835
      010008F0H   LINE      CODE     ---       #836
      010008F2H   LINE      CODE     ---       #837
      010008F4H   LINE      CODE     ---       #838
      010008F6H   LINE      CODE     ---       #839
      010008F6H   LINE      CODE     ---       #840
      010008F9H   LINE      CODE     ---       #841
      0100090AH   LINE      CODE     ---       #842
      0100090AH   LINE      CODE     ---       #843
      01000912H   LINE      CODE     ---       #844
      01000915H   LINE      CODE     ---       #846
      01000915H   LINE      CODE     ---       #847
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 105


      0100091BH   LINE      CODE     ---       #848
      0100091BH   LINE      CODE     ---       #849
      0100091BH   LINE      CODE     ---       #850
      0100091BH   LINE      CODE     ---       #851
      0100091BH   LINE      CODE     ---       #852
      0100091BH   LINE      CODE     ---       #853
      0100091BH   LINE      CODE     ---       #854
      0100091BH   LINE      CODE     ---       #855
      0100091DH   LINE      CODE     ---       #857
      0100091DH   LINE      CODE     ---       #858
      01000923H   LINE      CODE     ---       #859
      01000923H   LINE      CODE     ---       #860
      0100092AH   LINE      CODE     ---       #861
      0100092DH   LINE      CODE     ---       #863
      0100092DH   LINE      CODE     ---       #864
      01000935H   LINE      CODE     ---       #865
      0100093CH   LINE      CODE     ---       #866
      01000942H   LINE      CODE     ---       #867
      01000944H   LINE      CODE     ---       #868
      01000946H   LINE      CODE     ---       #869
      01000948H   LINE      CODE     ---       #870
      01000948H   LINE      CODE     ---       #871
      01000948H   LINE      CODE     ---       #872
      01000948H   LINE      CODE     ---       #873
      0100094BH   LINE      CODE     ---       #874
      01000956H   LINE      CODE     ---       #875
      01000956H   LINE      CODE     ---       #876
      01000958H   LINE      CODE     ---       #877
      0100095AH   LINE      CODE     ---       #878
      0100095CH   LINE      CODE     ---       #879
      0100095EH   LINE      CODE     ---       #880
      01000960H   LINE      CODE     ---       #881
      01000963H   LINE      CODE     ---       #882
      0100096CH   LINE      CODE     ---       #883
      0100096CH   LINE      CODE     ---       #884
      0100096CH   LINE      CODE     ---       #885
      01000973H   LINE      CODE     ---       #886
      01000973H   LINE      CODE     ---       #887
      01000975H   LINE      CODE     ---       #888
      01000977H   LINE      CODE     ---       #889
      01000979H   LINE      CODE     ---       #890
      0100097BH   LINE      CODE     ---       #891
      0100097DH   LINE      CODE     ---       #892
      0100097FH   LINE      CODE     ---       #893
      01000981H   LINE      CODE     ---       #896
      01000983H   LINE      CODE     ---       #897
      01000985H   LINE      CODE     ---       #898
      01000987H   LINE      CODE     ---       #899
      0100098DH   LINE      CODE     ---       #900
      01000993H   LINE      CODE     ---       #901
      01000995H   LINE      CODE     ---       #902
      01000997H   LINE      CODE     ---       #903
      01000999H   LINE      CODE     ---       #904
      0100099BH   LINE      CODE     ---       #906
      0100099EH   LINE      CODE     ---       #908
      010009A1H   LINE      CODE     ---       #910
      010009A4H   LINE      CODE     ---       #912
      010009A7H   LINE      CODE     ---       #913
      010009AAH   LINE      CODE     ---       #914
      010009ADH   LINE      CODE     ---       #915
      010009B0H   LINE      CODE     ---       #916
      010009B3H   LINE      CODE     ---       #917
      010009B6H   LINE      CODE     ---       #918
      010009B9H   LINE      CODE     ---       #920
      010009BFH   LINE      CODE     ---       #921
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 106


      010009BFH   LINE      CODE     ---       #922
      ---         BLOCKEND  ---      ---       LVL=0

      0100217CH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      0100217CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      0100217CH   LINE      CODE     ---       #926
      0100217CH   LINE      CODE     ---       #927
      0100217CH   LINE      CODE     ---       #929
      01002186H   LINE      CODE     ---       #930
      0100219BH   LINE      CODE     ---       #931
      ---         BLOCKEND  ---      ---       LVL=0

      01001A45H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001A45H   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001A45H   LINE      CODE     ---       #933
      01001A45H   LINE      CODE     ---       #934
      01001A45H   LINE      CODE     ---       #937
      01001A4AH   LINE      CODE     ---       #938
      01001A4AH   LINE      CODE     ---       #939
      01001A50H   LINE      CODE     ---       #940
      01001A5AH   LINE      CODE     ---       #941
      01001A5AH   LINE      CODE     ---       #942
      01001A5CH   LINE      CODE     ---       #943
      01001A5CH   LINE      CODE     ---       #944
      01001A5EH   LINE      CODE     ---       #946
      01001A5EH   LINE      CODE     ---       #947
      01001A6BH   LINE      CODE     ---       #948
      01001A6BH   LINE      CODE     ---       #949
      01001A6EH   LINE      CODE     ---       #950
      01001A6EH   LINE      CODE     ---       #951
      01001A74H   LINE      CODE     ---       #952
      01001A74H   LINE      CODE     ---       #954
      01001A9CH   LINE      CODE     ---       #955
      01001A9CH   LINE      CODE     ---       #956
      01001AA0H   LINE      CODE     ---       #957
      01001AA4H   LINE      CODE     ---       #958
      01001AAAH   LINE      CODE     ---       #959
      01001AB0H   LINE      CODE     ---       #960
      01001AB9H   LINE      CODE     ---       #961
      01001AC2H   LINE      CODE     ---       #962
      01001AC6H   LINE      CODE     ---       #963
      01001ACFH   LINE      CODE     ---       #964
      01001ACFH   LINE      CODE     ---       #965
      01001AD7H   LINE      CODE     ---       #967
      01001AD7H   LINE      CODE     ---       #968
      01001AD7H   LINE      CODE     ---       #969
      ---         BLOCKEND  ---      ---       LVL=0

      01001EA7H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01001EA7H   LINE      CODE     ---       #971
      01001EA7H   LINE      CODE     ---       #972
      01001EA7H   LINE      CODE     ---       #973
      01001EB7H   LINE      CODE     ---       #974
      01001EB7H   LINE      CODE     ---       #975
      01001EB7H   LINE      CODE     ---       #976
      01001EC0H   LINE      CODE     ---       #977
      01001EC6H   LINE      CODE     ---       #978
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 107


      01001EC7H   LINE      CODE     ---       #979
      01001EC7H   LINE      CODE     ---       #980
      01001ECAH   LINE      CODE     ---       #981
      01001ECAH   LINE      CODE     ---       #982
      01001ECFH   LINE      CODE     ---       #983
      01001ED0H   LINE      CODE     ---       #985
      01001ED0H   LINE      CODE     ---       #986
      01001ED6H   LINE      CODE     ---       #987
      01001ED6H   LINE      CODE     ---       #988
      01001ED7H   LINE      CODE     ---       #989
      01001ED7H   LINE      CODE     ---       #990
      01001EE0H   LINE      CODE     ---       #991
      01001EE6H   LINE      CODE     ---       #992
      01001EE7H   LINE      CODE     ---       #993
      01001EE7H   LINE      CODE     ---       #994
      01001EEDH   LINE      CODE     ---       #995
      01001EEDH   LINE      CODE     ---       #996
      01001EEDH   LINE      CODE     ---       #997
      01001EEDH   LINE      CODE     ---       #998
      01001EEDH   LINE      CODE     ---       #999
      ---         BLOCKEND  ---      ---       LVL=0

      010018E0H   BLOCK     CODE     ---       LVL=0
      010018E0H   BLOCK     CODE     NEAR LAB  LVL=1
      02000001H   SYMBOL    XDATA    BYTE      adc_state
      02000002H   SYMBOL    XDATA    BYTE      sample_count
      02000003H   SYMBOL    XDATA    WORD      adc_sum
      ---         BLOCKEND  ---      ---       LVL=1
      010018E0H   LINE      CODE     ---       #1002
      010018E0H   LINE      CODE     ---       #1003
      010018E0H   LINE      CODE     ---       #1009
      010018F1H   LINE      CODE     ---       #1010
      010018F1H   LINE      CODE     ---       #1011
      010018F1H   LINE      CODE     ---       #1012
      010018F6H   LINE      CODE     ---       #1013
      010018FCH   LINE      CODE     ---       #1014
      010018FDH   LINE      CODE     ---       #1016
      010018FDH   LINE      CODE     ---       #1017
      01001906H   LINE      CODE     ---       #1018
      01001906H   LINE      CODE     ---       #1019
      01001915H   LINE      CODE     ---       #1020
      0100191BH   LINE      CODE     ---       #1021
      0100191EH   LINE      CODE     ---       #1023
      0100192BH   LINE      CODE     ---       #1024
      0100192BH   LINE      CODE     ---       #1025
      01001942H   LINE      CODE     ---       #1026
      01001949H   LINE      CODE     ---       #1027
      0100194DH   LINE      CODE     ---       #1028
      01001953H   LINE      CODE     ---       #1029
      01001954H   LINE      CODE     ---       #1031
      01001954H   LINE      CODE     ---       #1032
      01001954H   LINE      CODE     ---       #1033
      01001954H   LINE      CODE     ---       #1034
      01001954H   LINE      CODE     ---       #1035
      01001956H   LINE      CODE     ---       #1037
      01001956H   LINE      CODE     ---       #1038
      01001966H   LINE      CODE     ---       #1039
      01001966H   LINE      CODE     ---       #1040
      0100196CH   LINE      CODE     ---       #1041
      01001977H   LINE      CODE     ---       #1042
      01001979H   LINE      CODE     ---       #1044
      01001979H   LINE      CODE     ---       #1045
      0100197BH   LINE      CODE     ---       #1046
      01001980H   LINE      CODE     ---       #1047
      01001980H   LINE      CODE     ---       #1049
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 108


      01001989H   LINE      CODE     ---       #1050
      01001989H   LINE      CODE     ---       #1051
      0100198FH   LINE      CODE     ---       #1052
      0100199AH   LINE      CODE     ---       #1053
      0100199CH   LINE      CODE     ---       #1055
      0100199CH   LINE      CODE     ---       #1056
      010019A1H   LINE      CODE     ---       #1057
      010019A3H   LINE      CODE     ---       #1058
      010019A3H   LINE      CODE     ---       #1059
      010019A3H   LINE      CODE     ---       #1060
      010019A5H   LINE      CODE     ---       #1062
      010019A5H   LINE      CODE     ---       #1063
      010019AAH   LINE      CODE     ---       #1064
      010019AAH   LINE      CODE     ---       #1065
      010019AAH   LINE      CODE     ---       #1066
      ---         BLOCKEND  ---      ---       LVL=0

      01002001H   BLOCK     CODE     ---       LVL=0
      02000046H   SYMBOL    XDATA    WORD      dly1
      01002001H   LINE      CODE     ---       #1068
      01002009H   LINE      CODE     ---       #1069
      01002009H   LINE      CODE     ---       #1070
      0100200CH   LINE      CODE     ---       #1071
      0100200FH   LINE      CODE     ---       #1072
      01002018H   LINE      CODE     ---       #1073
      01002027H   LINE      CODE     ---       #1074
      0100202AH   LINE      CODE     ---       #1075
      0100202DH   LINE      CODE     ---       #1076
      ---         BLOCKEND  ---      ---       LVL=0

      010020A8H   BLOCK     CODE     ---       LVL=0
      010020A8H   BLOCK     CODE     NEAR LAB  LVL=1
      02000046H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      010020A8H   LINE      CODE     ---       #1078
      010020A8H   LINE      CODE     ---       #1079
      010020A8H   LINE      CODE     ---       #1082
      010020ABH   LINE      CODE     ---       #1083
      010020AEH   LINE      CODE     ---       #1084
      010020BFH   LINE      CODE     ---       #1085
      010020C2H   LINE      CODE     ---       #1086
      010020C5H   LINE      CODE     ---       #1088
      010020CDH   LINE      CODE     ---       #1089
      ---         BLOCKEND  ---      ---       LVL=0

      010016D8H   BLOCK     CODE     ---       LVL=0
      010016D8H   LINE      CODE     ---       #1091
      010016D8H   LINE      CODE     ---       #1092
      010016D8H   LINE      CODE     ---       #1094
      010016E3H   LINE      CODE     ---       #1095
      010016E3H   LINE      CODE     ---       #1097
      010016E6H   LINE      CODE     ---       #1098
      010016E6H   LINE      CODE     ---       #1100
      010016E8H   LINE      CODE     ---       #1101
      010016EBH   LINE      CODE     ---       #1102
      010016EBH   LINE      CODE     ---       #1104
      010016F2H   LINE      CODE     ---       #1105
      010016F4H   LINE      CODE     ---       #1106
      010016F6H   LINE      CODE     ---       #1108
      010016F6H   LINE      CODE     ---       #1109
      010016F8H   LINE      CODE     ---       #1110
      010016F8H   LINE      CODE     ---       #1111
      010016FAH   LINE      CODE     ---       #1113
      010016FAH   LINE      CODE     ---       #1115
      010016FCH   LINE      CODE     ---       #1116
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 109


      010016FFH   LINE      CODE     ---       #1117
      010016FFH   LINE      CODE     ---       #1119
      01001702H   LINE      CODE     ---       #1120
      01001702H   LINE      CODE     ---       #1121
      01001704H   LINE      CODE     ---       #1122
      01001706H   LINE      CODE     ---       #1124
      01001706H   LINE      CODE     ---       #1125
      01001708H   LINE      CODE     ---       #1126
      01001708H   LINE      CODE     ---       #1127
      0100170AH   LINE      CODE     ---       #1129
      0100170AH   LINE      CODE     ---       #1131
      0100170CH   LINE      CODE     ---       #1132
      0100170CH   LINE      CODE     ---       #1133
      0100170CH   LINE      CODE     ---       #1134
      0100170EH   LINE      CODE     ---       #1136
      0100170EH   LINE      CODE     ---       #1138
      01001711H   LINE      CODE     ---       #1139
      01001711H   LINE      CODE     ---       #1140
      01001713H   LINE      CODE     ---       #1141
      01001716H   LINE      CODE     ---       #1142
      01001716H   LINE      CODE     ---       #1144
      0100171DH   LINE      CODE     ---       #1145
      0100171FH   LINE      CODE     ---       #1146
      01001721H   LINE      CODE     ---       #1148
      01001721H   LINE      CODE     ---       #1149
      01001723H   LINE      CODE     ---       #1150
      01001723H   LINE      CODE     ---       #1151
      01001725H   LINE      CODE     ---       #1153
      01001725H   LINE      CODE     ---       #1154
      01001728H   LINE      CODE     ---       #1155
      01001728H   LINE      CODE     ---       #1156
      0100172AH   LINE      CODE     ---       #1157
      01001734H   LINE      CODE     ---       #1159
      01001734H   LINE      CODE     ---       #1160
      0100173BH   LINE      CODE     ---       #1161
      0100173DH   LINE      CODE     ---       #1162
      0100173DH   LINE      CODE     ---       #1163
      0100173FH   LINE      CODE     ---       #1165
      0100173FH   LINE      CODE     ---       #1166
      01001741H   LINE      CODE     ---       #1167
      0100174BH   LINE      CODE     ---       #1169
      0100174BH   LINE      CODE     ---       #1170
      01001752H   LINE      CODE     ---       #1171
      01001754H   LINE      CODE     ---       #1172
      01001754H   LINE      CODE     ---       #1173
      01001754H   LINE      CODE     ---       #1174
      01001754H   LINE      CODE     ---       #1175
      01001754H   LINE      CODE     ---       #1178
      01001757H   LINE      CODE     ---       #1179
      01001757H   LINE      CODE     ---       #1180
      01001765H   LINE      CODE     ---       #1181
      01001774H   LINE      CODE     ---       #1182
      01001774H   LINE      CODE     ---       #1183
      01001776H   LINE      CODE     ---       #1184
      01001778H   LINE      CODE     ---       #1186
      01001778H   LINE      CODE     ---       #1187
      0100177AH   LINE      CODE     ---       #1188
      0100177CH   LINE      CODE     ---       #1189
      01001783H   LINE      CODE     ---       #1190
      01001783H   LINE      CODE     ---       #1191
      01001783H   LINE      CODE     ---       #1194
      01001789H   LINE      CODE     ---       #1195
      01001793H   LINE      CODE     ---       #1196
      01001793H   LINE      CODE     ---       #1197
      01001795H   LINE      CODE     ---       #1198
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 110


      0100179AH   LINE      CODE     ---       #1199
      0100179AH   LINE      CODE     ---       #1202
      010017A8H   LINE      CODE     ---       #1203
      010017B7H   LINE      CODE     ---       #1204
      010017B7H   LINE      CODE     ---       #1205
      010017BBH   LINE      CODE     ---       #1206
      010017C0H   LINE      CODE     ---       #1207
      010017C0H   LINE      CODE     ---       #1210
      010017C8H   LINE      CODE     ---       #1211
      010017C8H   LINE      CODE     ---       #1212
      010017CEH   LINE      CODE     ---       #1213
      010017D8H   LINE      CODE     ---       #1214
      010017D8H   LINE      CODE     ---       #1215
      010017DAH   LINE      CODE     ---       #1216
      010017DFH   LINE      CODE     ---       #1217
      010017DFH   LINE      CODE     ---       #1218
      010017E0H   LINE      CODE     ---       #1220
      010017E0H   LINE      CODE     ---       #1221
      010017E5H   LINE      CODE     ---       #1222
      010017E7H   LINE      CODE     ---       #1223
      010017E7H   LINE      CODE     ---       #1224
      ---         BLOCKEND  ---      ---       LVL=0

      010011D4H   BLOCK     CODE     ---       LVL=0
      010011D4H   LINE      CODE     ---       #1233
      010011D4H   LINE      CODE     ---       #1234
      010011D4H   LINE      CODE     ---       #1236
      010011E0H   LINE      CODE     ---       #1237
      010011E0H   LINE      CODE     ---       #1238
      010011E3H   LINE      CODE     ---       #1239
      010011E3H   LINE      CODE     ---       #1240
      010011E5H   LINE      CODE     ---       #1241
      010011E7H   LINE      CODE     ---       #1242
      010011E9H   LINE      CODE     ---       #1243
      010011EBH   LINE      CODE     ---       #1244
      010011EDH   LINE      CODE     ---       #1245
      010011F3H   LINE      CODE     ---       #1246
      010011F3H   LINE      CODE     ---       #1247
      010011F3H   LINE      CODE     ---       #1250
      01001214H   LINE      CODE     ---       #1251
      01001214H   LINE      CODE     ---       #1253
      0100121AH   LINE      CODE     ---       #1254
      0100121AH   LINE      CODE     ---       #1255
      01001223H   LINE      CODE     ---       #1256
      01001229H   LINE      CODE     ---       #1257
      0100122BH   LINE      CODE     ---       #1258
      0100122DH   LINE      CODE     ---       #1259
      0100122FH   LINE      CODE     ---       #1260
      01001231H   LINE      CODE     ---       #1261
      01001235H   LINE      CODE     ---       #1262
      01001235H   LINE      CODE     ---       #1263
      01001235H   LINE      CODE     ---       #1266
      01001238H   LINE      CODE     ---       #1267
      01001238H   LINE      CODE     ---       #1268
      0100123AH   LINE      CODE     ---       #1270
      01001255H   LINE      CODE     ---       #1271
      01001255H   LINE      CODE     ---       #1273
      01001257H   LINE      CODE     ---       #1274
      01001259H   LINE      CODE     ---       #1275
      0100125BH   LINE      CODE     ---       #1278
      01001264H   LINE      CODE     ---       #1279
      0100126AH   LINE      CODE     ---       #1280
      0100126CH   LINE      CODE     ---       #1281
      0100126EH   LINE      CODE     ---       #1282
      01001272H   LINE      CODE     ---       #1283
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 111


      01001279H   LINE      CODE     ---       #1284
      0100127BH   LINE      CODE     ---       #1285
      0100128AH   LINE      CODE     ---       #1286
      0100128AH   LINE      CODE     ---       #1288
      01001290H   LINE      CODE     ---       #1289
      01001292H   LINE      CODE     ---       #1290
      01001294H   LINE      CODE     ---       #1291
      01001296H   LINE      CODE     ---       #1292
      01001298H   LINE      CODE     ---       #1293
      01001298H   LINE      CODE     ---       #1294
      01001298H   LINE      CODE     ---       #1297
      010012B9H   LINE      CODE     ---       #1298
      010012B9H   LINE      CODE     ---       #1300
      010012BFH   LINE      CODE     ---       #1301
      010012BFH   LINE      CODE     ---       #1302
      010012C8H   LINE      CODE     ---       #1303
      010012CEH   LINE      CODE     ---       #1304
      010012D0H   LINE      CODE     ---       #1305
      010012D2H   LINE      CODE     ---       #1306
      010012D4H   LINE      CODE     ---       #1307
      010012D6H   LINE      CODE     ---       #1308
      010012DBH   LINE      CODE     ---       #1309
      010012DBH   LINE      CODE     ---       #1310
      010012DBH   LINE      CODE     ---       #1313
      010012DEH   LINE      CODE     ---       #1314
      010012DEH   LINE      CODE     ---       #1315
      010012E0H   LINE      CODE     ---       #1317
      010012FBH   LINE      CODE     ---       #1318
      010012FBH   LINE      CODE     ---       #1320
      010012FDH   LINE      CODE     ---       #1321
      010012FFH   LINE      CODE     ---       #1322
      01001301H   LINE      CODE     ---       #1325
      0100130AH   LINE      CODE     ---       #1326
      01001310H   LINE      CODE     ---       #1327
      01001312H   LINE      CODE     ---       #1328
      01001314H   LINE      CODE     ---       #1329
      01001319H   LINE      CODE     ---       #1330
      01001320H   LINE      CODE     ---       #1331
      01001321H   LINE      CODE     ---       #1332
      01001330H   LINE      CODE     ---       #1333
      01001330H   LINE      CODE     ---       #1335
      01001336H   LINE      CODE     ---       #1336
      01001338H   LINE      CODE     ---       #1337
      0100133AH   LINE      CODE     ---       #1338
      0100133CH   LINE      CODE     ---       #1339
      0100133EH   LINE      CODE     ---       #1340
      0100133EH   LINE      CODE     ---       #1341
      0100133EH   LINE      CODE     ---       #1342
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      010009C2H   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000ACBH   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000B72H   PUBLIC    CODE     ---       ?C?FCASTC
      01000B6DH   PUBLIC    CODE     ---       ?C?FCASTI
      01000B68H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000BA6H   PUBLIC    CODE     ---       ?C?FPGETOPN2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 112


      01000BDBH   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000BE5H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000BBDH   PUBLIC    CODE     ---       ?C?FPRESULT
      01000BD1H   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000BE2H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000BF0H   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000C2DH   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000D39H   PUBLIC    CODE     ---       ?C?FPADD
      01000D35H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000E5AH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      01001A00H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000F6AH   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000F90H   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000FA9H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000FD6H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000FE8H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      0100100AH   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      0100105FH   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      010010B1H   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      01001143H   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      01001151H   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      0100115DH   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      0100118EH   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      010011A5H   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      010011AEH   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=15.3 xdata=164 const=13 code=8896
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  15:27:02  PAGE 113


LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
