LX51 LINKER/LOCATER V4.66.97.0                                                          07/22/2025  13:47:29  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   0026EBH   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000C6H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000007H.2 BIT
C:000000H   C:000000H   C:00FFFFH   000029H   CONST
I:000000H   I:000000H   I:00007FH   00000DH   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00000CH   000005H   BYTE   UNIT     DATA           _DATA_GROUP_
00000DH.0 00001FH.7 000013H.0 ---    ---      **GAP**
000020H.0 000024H.3 000004H.4 BIT    UNIT     BIT            ?BI?MAIN
000024H.4 000026H.0 000001H.5 BIT    UNIT     BIT            _BIT_GROUP_
000026H.1 000026H.5 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000026H.6 000027H.1 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000027H.2 000027H   000000H.6 ---    ---      **GAP**
000028H   000028H   000001H   BYTE   UNIT     IDATA          ?STACK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 3



* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000026H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
000027H   000027H   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
000028H   000028H   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
000038H   00003AH   000003H   ---    ---      **GAP**
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   000042H   000005H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000062H   00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   000091H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000092H   000092H   000001H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   00091EH   000869H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
00091FH   001130H   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
001131H   0015B3H   000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
0015B4H   00171EH   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
00171FH   001859H   00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
00185AH   001986H   00012DH   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 4


001987H   001AB0H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
001AB1H   001BC9H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
001BCAH   001CD9H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
001CDAH   001DAEH   0000D5H   BYTE   UNIT     CODE           ?C_INITSEG
001DAFH   001E48H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001E49H   001EDBH   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001EDCH   001F53H   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001F54H   001FCBH   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001FCCH   00203EH   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
00203FH   0020AEH   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
0020AFH   00211BH   00006DH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
00211CH   00217AH   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
00217BH   0021D7H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
0021D8H   002232H   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
002233H   002281H   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
002282H   0022CDH   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
0022CEH   002317H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
002318H   00235EH   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
00235FH   00239DH   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
00239EH   0023D7H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
0023D8H   00240CH   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
00240DH   002440H   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
002441H   002471H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
002472H   00249EH   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
00249FH   0024C8H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
0024C9H   0024F1H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
0024F2H   002518H   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
002519H   00253EH   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
00253FH   002563H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
002564H   002588H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
002589H   0025A8H   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
0025A9H   0025C8H   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
0025C9H   0025E8H   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
0025E9H   002607H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
002608H   002626H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
002627H   002644H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
002645H   002661H   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
002662H   00267BH   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
00267CH   002691H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
002692H   0026A5H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
0026A6H   0026B8H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
0026B9H   0026CBH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
0026CCH   0026DEH   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
0026DFH   0026EFH   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
0026F0H   0026F8H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
0026F9H   002701H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
002702H   00270AH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
00270BH   002712H   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
002713H   002719H   000007H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
00271AH   00271FH   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
002720H   002725H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
002726H   002741H   00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
002742H   00274EH   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   00004FH   000050H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000050H   00007AH   00002BH   BYTE   UNIT     XDATA          ?XD?MAIN
00007BH   00009BH   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
00009CH   0000B0H   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
0000B1H   0000BBH   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
0000BCH   0000C1H   000006H   BYTE   UNIT     XDATA          ?XD?KEY
0000C2H   0000C5H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 7


   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP  START  STOP
===================================================================================
?C_C51STARTUP                                 ----- -----  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     24H.4 24H.7  ----- -----  0000H 0010H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----  ----- -----

_DELAY1MS/MAIN                                ----- -----  ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 8


_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  ----- -----  0011H 0012H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 9



FLASH_UNLOCK/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  ----- -----  0011H 0012H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  ----- -----  0011H 001EH
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  ----- -----  001FH 0023H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----  ----- -----
  +--> _ADC/ADC_USED

_ADC/ADC_USED                                 ----- -----  ----- -----  0011H 001FH
  +--> _ADC_ENABLECHANNEL/ADC
  +--> ADC_GETADCRESULT/ADC
  +--> PRINTF/PRINTF

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----  ----- -----

PRINTF/PRINTF                                 25H.0 26H.0  0008H 000CH  0020H 004FH
  +--> _PUTCHAR/UART_INIT

_PUTCHAR/UART_INIT                            ----- -----  ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----  ----- -----
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 10



?C_INITSEG                                    ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 11


LSE_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
      02000020H   XDATA    ---       ?_PRINTF?BYTE
      02000020H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      0200001FH   XDATA    BYTE      ?_UART_Send_String?BYTE
      0100110BH   CODE     ---       ?C?CCASE
      01000F06H   CODE     ---       ?C?CLDOPTR
      01000EEDH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01000EC7H   CODE     ---       ?C?COPY
      01000F45H   CODE     ---       ?C?CSTOPTR
      01000F33H   CODE     ---       ?C?CSTPTR
      01000ACFH   CODE     ---       ?C?FCASTC
      01000ACAH   CODE     ---       ?C?FCASTI
      01000AC5H   CODE     ---       ?C?FCASTL
      01000C96H   CODE     ---       ?C?FPADD
      01000B8AH   CODE     ---       ?C?FPCONVERT
      01000A28H   CODE     ---       ?C?FPDIV
      01000B03H   CODE     ---       ?C?FPGETOPN2
      0100091FH   CODE     ---       ?C?FPMUL
      01000B38H   CODE     ---       ?C?FPNANRESULT
      01000B42H   CODE     ---       ?C?FPOVERFLOW
      01000B1AH   CODE     ---       ?C?FPRESULT
      01000B2EH   CODE     ---       ?C?FPRESULT2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 12


      01000B4DH   CODE     ---       ?C?FPROUND
      01000C92H   CODE     ---       ?C?FPSUB
      01000B3FH   CODE     ---       ?C?FPUNDERFLOW
      01000DB7H   CODE     ---       ?C?FTNPWR
      01000FBCH   CODE     ---       ?C?ILDIX
      010010A0H   CODE     ---       ?C?LNEG
      010010BAH   CODE     ---       ?C?LSTKXDATA
      010010AEH   CODE     ---       ?C?LSTXDATA
      010010EBH   CODE     ---       ?C?PLDIXDATA
      01001102H   CODE     ---       ?C?PSTXDATA
      01000F67H   CODE     ---       ?C?UIDIV
      0100100EH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      01001E04H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      0100171FH   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      010026F0H   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      0100267CH   CODE     ---       _ADC_ConfigRunMode
      01002627H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      010025C9H   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01002441H   CODE     ---       _FLASH_Erase
      0100240DH   CODE     ---       _FLASH_Read
      010023D8H   CODE     ---       _FLASH_Write
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 13


*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001FCCH   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01002318H   CODE     ---       _Key_Function_Switch_System
      01001E49H   CODE     ---       _Motor_Step_Control
      0100119CH   CODE     ---       _PRINTF
      01000056H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
      01001196H   CODE     ---       _SPRINTF
      01002472H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01002282H   CODE     ---       _TMR_ConfigRunMode
      010022CEH   CODE     ---       _TMR_ConfigTimerClk
      010024F2H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      01002645H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      010025E9H   CODE     ---       _TMR_Start
*DEL*:00000000H   CODE     ---       _TMR_Stop
      01002608H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      010026F9H   CODE     ---       _UART_ConfigBRTClk
      01002702H   CODE     ---       _UART_ConfigBRTPeriod
      0100211CH   CODE     ---       _UART_ConfigRunMode
      01002589H   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      010026A6H   CODE     ---       _UART_EnableDoubleFrequency
      010026DFH   CODE     ---       _UART_EnableInt
      010026B9H   CODE     ---       _UART_EnableReceive
      01002692H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      01002564H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      0100217BH   CODE     ---       _UART_Send_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      0100002AH   CODE     ---       ACMP_IRQHandler
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      01002662H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      0100253FH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000031H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000060H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.6 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000021H.7 BIT      BIT       batlow1
      02000077H   XDATA    BYTE      batlow1_cnt
      02000065H   XDATA    BYTE      batlow_cnt
      020000ACH   XDATA    WORD      Battery_ADC_Wait_Time
      010020AFH   CODE     ---       Battery_Check
      02000078H   XDATA    WORD      BatV
      00000022H.7 BIT      BIT       Bit_1_ms_Buff
      00000023H.7 BIT      BIT       Bit_N_ms_Buff
      00000023H.3 BIT      BIT       Bit_Toggle
*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000026H.7 BIT      BIT       Center_Line_Control
      00000021H.5 BIT      BIT       Charg_State_Buff
      00000023H.1 BIT      BIT       charge_flash
      02000052H   XDATA    WORD      charge_flash_cnt
      00000022H.5 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      01002720H   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      02000066H   XDATA    INT       Count_1_Degree_Pulse
      020000A7H   XDATA    INT       Count_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 15


*SFR* 000000D0H.7 DATA     BIT       CY
      0200009BH   XDATA    BYTE      Data_Length
      00000023H.2 BIT      BIT       Delay_Open
      00000027H.1 BIT      BIT       Delay_Over
      020000A9H   XDATA    WORD      Delay_Time
      020000A5H   XDATA    WORD      Delay_Time_Count
      00000024H.0 BIT      BIT       direction_changed
      0200006CH   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000030H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000027H.0 BIT      BIT       Get_String_Buff
      020000ABH   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      0100203FH   CODE     ---       GPIO_Config
      010026CCH   CODE     ---       GPIO_Key_Interrupt_Config
      01000036H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 16


      0200009EH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      020000BDH   XDATA    BYTE      K1_Count
      00000026H.1 BIT      BIT       K1_Press
      0200009FH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      020000BEH   XDATA    BYTE      K2_Count
      00000023H.4 BIT      BIT       k2_long_press_detected
      02000068H   XDATA    WORD      k2_long_press_timer
      00000026H.2 BIT      BIT       K2_Press
      00000022H.0 BIT      BIT       k2_released
      020000A0H   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      020000BFH   XDATA    BYTE      K3_Count
      00000026H.3 BIT      BIT       K3_Press
      00000022H.1 BIT      BIT       k3_released
      020000C0H   XDATA    BYTE      K4_Count
      00000026H.4 BIT      BIT       K4_Press
      020000C1H   XDATA    BYTE      K5_Count
      00000026H.5 BIT      BIT       K5_Press
      0200006AH   XDATA    WORD      key1_duration
      00000022H.2 BIT      BIT       key1_handle
      00000023H.5 BIT      BIT       key1_long_started
      02000058H   XDATA    WORD      key1_press_time
      00000022H.6 BIT      BIT       key1_pressed
      0200006EH   XDATA    WORD      key3_duration
      00000022H.3 BIT      BIT       key3_handle
      00000023H.6 BIT      BIT       key3_long_started
      0200005AH   XDATA    WORD      key3_press_time
      00000023H.0 BIT      BIT       key3_pressed
      020000BCH   XDATA    BYTE      Key_Buff
      0100239EH   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010015B4H   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001987H   CODE     ---       Key_Scan
      00000024H.1 BIT      BIT       key_short_press_mode
      02000076H   XDATA    BYTE      last_direction
      01001BCAH   CODE     ---       LED_Control
      00000021H.4 BIT      BIT       led_flash_state
      02000063H   XDATA    WORD      led_flash_timer
      00000022H.4 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000062H   XDATA    BYTE      ledonoff1_cnt
      0200007AH   XDATA    BYTE      ledonoff_cnt
      00000024H.2 BIT      BIT       longhit
      0200009CH   XDATA    WORD      longhit_cnt
      01000029H   CODE     ---       LSE_IRQHandler
      01000028H   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      020000B0H   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      020000A3H   XDATA    WORD      Motor_Speed_Data
      00000021H.3 BIT      BIT       need_led_flash
      020000A1H   XDATA    INT       Num
      020000C2H   XDATA    INT       Num_Forward_Pulse
      020000C4H   XDATA    INT       Num_Reverse_Pulse
      02000073H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 17


*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000026H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001EDCH   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001F54H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      01000027H   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000026H.6 BIT      BIT       Power_count_clean
      020000AEH   XDATA    WORD      Power_Off_Wait_Time
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      01002519H   CODE     ---       Restore_dly
      0100271AH   CODE     ---       Return_UART_Data_Length
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 18


*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000070H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.3 BIT      BIT       speedup
      0200005CH   XDATA    WORD      speedup_cnt
      01000037H   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000086H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      02000075H   XDATA    BYTE      System_Mode_Before_Charge
      02000072H   XDATA    BYTE      System_Mode_Data
      02000054H   XDATA    DWORD     Systemclock
*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      0100185AH   CODE     ---       Timer0_IRQHandler
      01002713H   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 19


      0100002EH   CODE     ---       Timer3_IRQHandler
      0100002FH   CODE     ---       Timer4_IRQHandler
      0200005EH   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      0100249FH   CODE     ---       TMR0_Config
      010024C9H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      010021D8H   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01002233H   CODE     ---       UART_0_Config
      0100235FH   CODE     ---       UART_1_Config
      010025A9H   CODE     ---       UART_Data_Init
      01001AB1H   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      0100270BH   CODE     ---       UART_EnableBRT
      0200007BH   XDATA    ---       UART_Get_String
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000032H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 20


      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001DB2H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01001DAFH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      01001DBDH   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01001DAFH   LINE      CODE     ---       #133
      01001DB1H   LINE      CODE     ---       #134
      01001DB2H   LINE      CODE     ---       #135
      01001DB3H   LINE      CODE     ---       #136
      01001DB5H   LINE      CODE     ---       #140
      01001DB8H   LINE      CODE     ---       #141
      01001DBAH   LINE      CODE     ---       #145
      01001DBCH   LINE      CODE     ---       #147
      01001DBDH   LINE      CODE     ---       #148
      01001DBEH   LINE      CODE     ---       #149
      01001DBFH   LINE      CODE     ---       #150
      01001DC1H   LINE      CODE     ---       #151
      01001DC3H   LINE      CODE     ---       #185
      01001DC6H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      010026F0H   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      0100253FH   PUBLIC    CODE     ---       ADC_GetADCResult
      01002627H   PUBLIC    CODE     ---       _ADC_EnableChannel
      0100267CH   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 21


      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 22


      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 23


      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      0100267CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      0100267CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100267CH   LINE      CODE     ---       #88
      0100267CH   LINE      CODE     ---       #89
      0100267CH   LINE      CODE     ---       #90
      0100267CH   LINE      CODE     ---       #92
      0100267EH   LINE      CODE     ---       #93
      01002681H   LINE      CODE     ---       #94
      01002682H   LINE      CODE     ---       #95
      01002684H   LINE      CODE     ---       #97
      01002686H   LINE      CODE     ---       #98
      0100268AH   LINE      CODE     ---       #99
      0100268FH   LINE      CODE     ---       #100
      01002691H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      01002627H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      01002627H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002627H   LINE      CODE     ---       #154
      01002627H   LINE      CODE     ---       #155
      01002627H   LINE      CODE     ---       #156
      01002627H   LINE      CODE     ---       #158
      01002629H   LINE      CODE     ---       #159
      0100262DH   LINE      CODE     ---       #160
      01002636H   LINE      CODE     ---       #161
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 24


      01002638H   LINE      CODE     ---       #163
      0100263AH   LINE      CODE     ---       #164
      0100263EH   LINE      CODE     ---       #165
      01002642H   LINE      CODE     ---       #166
      01002644H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      0100253FH   BLOCK     CODE     ---       LVL=0
      0100253FH   LINE      CODE     ---       #258
      0100253FH   LINE      CODE     ---       #259
      0100253FH   LINE      CODE     ---       #260
      01002546H   LINE      CODE     ---       #261
      01002546H   LINE      CODE     ---       #262
      01002559H   LINE      CODE     ---       #263
      01002559H   LINE      CODE     ---       #264
      01002563H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      010026F0H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      010026F0H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010026F0H   LINE      CODE     ---       #344
      010026F0H   LINE      CODE     ---       #345
      010026F0H   LINE      CODE     ---       #346
      010026F0H   LINE      CODE     ---       #348
      010026F4H   LINE      CODE     ---       #349
      010026F6H   LINE      CODE     ---       #350
      010026F7H   LINE      CODE     ---       #351
      010026F8H   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 25


      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 26


      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 27


      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 28


      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 29


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 30


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 31


      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000086H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 32


      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 33


      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 34



      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #358
      01000086H   LINE      CODE     ---       #359
      01000086H   LINE      CODE     ---       #360
      01000087H   LINE      CODE     ---       #361
      01000088H   LINE      CODE     ---       #362
      0100008BH   LINE      CODE     ---       #363
      0100008CH   LINE      CODE     ---       #364
      0100008DH   LINE      CODE     ---       #365
      0100008EH   LINE      CODE     ---       #366
      0100008FH   LINE      CODE     ---       #367
      01000090H   LINE      CODE     ---       #368
      01000091H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      010025E9H   PUBLIC    CODE     ---       _TMR_Start
      01002645H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      010024F2H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      010022CEH   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01002282H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 35


      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 36


      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 37


      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002282H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01002282H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002282H   LINE      CODE     ---       #74
      01002282H   LINE      CODE     ---       #75
      01002282H   LINE      CODE     ---       #76
      01002282H   LINE      CODE     ---       #78
      01002291H   LINE      CODE     ---       #79
      01002291H   LINE      CODE     ---       #80
      01002291H   LINE      CODE     ---       #81
      01002293H   LINE      CODE     ---       #82
      01002297H   LINE      CODE     ---       #83
      0100229DH   LINE      CODE     ---       #84
      0100229DH   LINE      CODE     ---       #85
      0100229FH   LINE      CODE     ---       #86
      0100229FH   LINE      CODE     ---       #87
      010022A1H   LINE      CODE     ---       #88
      010022A5H   LINE      CODE     ---       #89
      010022B2H   LINE      CODE     ---       #90
      010022B4H   LINE      CODE     ---       #91
      010022B5H   LINE      CODE     ---       #92
      010022B5H   LINE      CODE     ---       #93
      010022B7H   LINE      CODE     ---       #94
      010022BAH   LINE      CODE     ---       #95
      010022BBH   LINE      CODE     ---       #96
      010022BDH   LINE      CODE     ---       #97
      010022BEH   LINE      CODE     ---       #98
      010022BEH   LINE      CODE     ---       #99
      010022C0H   LINE      CODE     ---       #100
      010022C4H   LINE      CODE     ---       #101
      010022CBH   LINE      CODE     ---       #102
      010022CDH   LINE      CODE     ---       #103
      010022CDH   LINE      CODE     ---       #104
      010022CDH   LINE      CODE     ---       #105
      010022CDH   LINE      CODE     ---       #106
      010022CDH   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      010022CEH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      010022CEH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022CEH   LINE      CODE     ---       #117
      010022CEH   LINE      CODE     ---       #118
      010022CEH   LINE      CODE     ---       #119
      010022CEH   LINE      CODE     ---       #121
      010022DDH   LINE      CODE     ---       #122
      010022DDH   LINE      CODE     ---       #123
      010022DDH   LINE      CODE     ---       #124
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 38


      010022DFH   LINE      CODE     ---       #125
      010022E3H   LINE      CODE     ---       #126
      010022E9H   LINE      CODE     ---       #127
      010022E9H   LINE      CODE     ---       #128
      010022EBH   LINE      CODE     ---       #129
      010022EBH   LINE      CODE     ---       #130
      010022EDH   LINE      CODE     ---       #131
      010022F1H   LINE      CODE     ---       #132
      010022F6H   LINE      CODE     ---       #133
      010022F8H   LINE      CODE     ---       #134
      010022F9H   LINE      CODE     ---       #135
      010022F9H   LINE      CODE     ---       #136
      010022FBH   LINE      CODE     ---       #137
      010022FFH   LINE      CODE     ---       #138
      01002304H   LINE      CODE     ---       #139
      01002304H   LINE      CODE     ---       #140
      01002306H   LINE      CODE     ---       #141
      01002306H   LINE      CODE     ---       #142
      01002308H   LINE      CODE     ---       #143
      0100230CH   LINE      CODE     ---       #144
      01002315H   LINE      CODE     ---       #145
      01002317H   LINE      CODE     ---       #146
      01002317H   LINE      CODE     ---       #147
      01002317H   LINE      CODE     ---       #148
      01002317H   LINE      CODE     ---       #149
      01002317H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      010024F2H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      010024F2H   LINE      CODE     ---       #160
      010024F2H   LINE      CODE     ---       #161
      010024F2H   LINE      CODE     ---       #162
      01002501H   LINE      CODE     ---       #163
      01002501H   LINE      CODE     ---       #164
      01002501H   LINE      CODE     ---       #165
      01002503H   LINE      CODE     ---       #166
      01002505H   LINE      CODE     ---       #167
      01002506H   LINE      CODE     ---       #168
      01002506H   LINE      CODE     ---       #169
      01002508H   LINE      CODE     ---       #170
      0100250AH   LINE      CODE     ---       #171
      0100250BH   LINE      CODE     ---       #172
      0100250BH   LINE      CODE     ---       #173
      0100250DH   LINE      CODE     ---       #174
      0100250FH   LINE      CODE     ---       #175
      01002510H   LINE      CODE     ---       #176
      01002510H   LINE      CODE     ---       #177
      01002514H   LINE      CODE     ---       #178
      01002518H   LINE      CODE     ---       #179
      01002518H   LINE      CODE     ---       #180
      01002518H   LINE      CODE     ---       #181
      01002518H   LINE      CODE     ---       #182
      01002518H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002645H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002645H   LINE      CODE     ---       #256
      01002645H   LINE      CODE     ---       #257
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 39


      01002645H   LINE      CODE     ---       #258
      01002654H   LINE      CODE     ---       #259
      01002654H   LINE      CODE     ---       #260
      01002654H   LINE      CODE     ---       #261
      01002656H   LINE      CODE     ---       #262
      01002657H   LINE      CODE     ---       #263
      01002657H   LINE      CODE     ---       #264
      01002659H   LINE      CODE     ---       #265
      0100265AH   LINE      CODE     ---       #266
      0100265AH   LINE      CODE     ---       #267
      0100265DH   LINE      CODE     ---       #268
      0100265EH   LINE      CODE     ---       #269
      0100265EH   LINE      CODE     ---       #270
      01002661H   LINE      CODE     ---       #271
      01002661H   LINE      CODE     ---       #272
      01002661H   LINE      CODE     ---       #273
      01002661H   LINE      CODE     ---       #274
      01002661H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      010025E9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010025E9H   LINE      CODE     ---       #368
      010025E9H   LINE      CODE     ---       #369
      010025E9H   LINE      CODE     ---       #370
      010025F8H   LINE      CODE     ---       #371
      010025F8H   LINE      CODE     ---       #372
      010025F8H   LINE      CODE     ---       #373
      010025FBH   LINE      CODE     ---       #374
      010025FCH   LINE      CODE     ---       #375
      010025FCH   LINE      CODE     ---       #376
      010025FFH   LINE      CODE     ---       #377
      01002600H   LINE      CODE     ---       #378
      01002600H   LINE      CODE     ---       #379
      01002603H   LINE      CODE     ---       #380
      01002604H   LINE      CODE     ---       #381
      01002604H   LINE      CODE     ---       #382
      01002607H   LINE      CODE     ---       #383
      01002607H   LINE      CODE     ---       #384
      01002607H   LINE      CODE     ---       #385
      01002607H   LINE      CODE     ---       #386
      01002607H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 40


      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      01002702H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      010026F9H   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      0100270BH   PUBLIC    CODE     ---       UART_EnableBRT
      01002692H   PUBLIC    CODE     ---       _UART_GetBuff
      01002608H   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      01002564H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      010026DFH   PUBLIC    CODE     ---       _UART_EnableInt
      010026B9H   PUBLIC    CODE     ---       _UART_EnableReceive
      010026A6H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      0100211CH   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 41


      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 42


      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 43


      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100211CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      0100211CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100211CH   LINE      CODE     ---       #70
      0100211CH   LINE      CODE     ---       #71
      0100211CH   LINE      CODE     ---       #72
      0100211CH   LINE      CODE     ---       #74
      0100211FH   LINE      CODE     ---       #75
      0100211FH   LINE      CODE     ---       #76
      01002121H   LINE      CODE     ---       #77
      01002125H   LINE      CODE     ---       #78
      0100212CH   LINE      CODE     ---       #79
      0100212EH   LINE      CODE     ---       #81
      01002131H   LINE      CODE     ---       #82
      0100213DH   LINE      CODE     ---       #83
      0100213DH   LINE      CODE     ---       #84
      0100213DH   LINE      CODE     ---       #85
      0100213DH   LINE      CODE     ---       #86
      0100213DH   LINE      CODE     ---       #87
      01002140H   LINE      CODE     ---       #88
      01002142H   LINE      CODE     ---       #89
      01002142H   LINE      CODE     ---       #90
      01002145H   LINE      CODE     ---       #91
      01002147H   LINE      CODE     ---       #92
      01002147H   LINE      CODE     ---       #93
      0100214AH   LINE      CODE     ---       #94
      0100214AH   LINE      CODE     ---       #95
      0100214AH   LINE      CODE     ---       #96
      0100214AH   LINE      CODE     ---       #97
      0100214AH   LINE      CODE     ---       #99
      0100214AH   LINE      CODE     ---       #100
      0100214FH   LINE      CODE     ---       #101
      0100214FH   LINE      CODE     ---       #102
      01002151H   LINE      CODE     ---       #103
      01002155H   LINE      CODE     ---       #104
      0100215EH   LINE      CODE     ---       #105
      01002160H   LINE      CODE     ---       #107
      01002163H   LINE      CODE     ---       #108
      0100216FH   LINE      CODE     ---       #109
      0100216FH   LINE      CODE     ---       #110
      0100216FH   LINE      CODE     ---       #111
      0100216FH   LINE      CODE     ---       #112
      0100216FH   LINE      CODE     ---       #113
      01002172H   LINE      CODE     ---       #114
      01002173H   LINE      CODE     ---       #115
      01002173H   LINE      CODE     ---       #116
      01002176H   LINE      CODE     ---       #117
      01002177H   LINE      CODE     ---       #118
      01002177H   LINE      CODE     ---       #119
      0100217AH   LINE      CODE     ---       #120
      0100217AH   LINE      CODE     ---       #121
      0100217AH   LINE      CODE     ---       #122
      0100217AH   LINE      CODE     ---       #123
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 44


      0100217AH   LINE      CODE     ---       #124
      0100217AH   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      010026A6H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010026A6H   LINE      CODE     ---       #133
      010026A6H   LINE      CODE     ---       #134
      010026A6H   LINE      CODE     ---       #135
      010026ACH   LINE      CODE     ---       #136
      010026ACH   LINE      CODE     ---       #137
      010026AFH   LINE      CODE     ---       #138
      010026AFH   LINE      CODE     ---       #139
      010026B5H   LINE      CODE     ---       #140
      010026B5H   LINE      CODE     ---       #141
      010026B8H   LINE      CODE     ---       #142
      010026B8H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010026B9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010026B9H   LINE      CODE     ---       #280
      010026B9H   LINE      CODE     ---       #281
      010026B9H   LINE      CODE     ---       #282
      010026BFH   LINE      CODE     ---       #283
      010026BFH   LINE      CODE     ---       #284
      010026C2H   LINE      CODE     ---       #285
      010026C2H   LINE      CODE     ---       #286
      010026C8H   LINE      CODE     ---       #287
      010026C8H   LINE      CODE     ---       #288
      010026CBH   LINE      CODE     ---       #289
      010026CBH   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010026DFH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010026DFH   LINE      CODE     ---       #317
      010026DFH   LINE      CODE     ---       #318
      010026DFH   LINE      CODE     ---       #319
      010026E5H   LINE      CODE     ---       #320
      010026E5H   LINE      CODE     ---       #321
      010026E7H   LINE      CODE     ---       #322
      010026E7H   LINE      CODE     ---       #323
      010026EDH   LINE      CODE     ---       #324
      010026EDH   LINE      CODE     ---       #325
      010026EFH   LINE      CODE     ---       #326
      010026EFH   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002564H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      01002564H   LINE      CODE     ---       #353
      01002566H   LINE      CODE     ---       #354
      01002566H   LINE      CODE     ---       #355
      0100256CH   LINE      CODE     ---       #356
      0100256CH   LINE      CODE     ---       #357
      01002576H   LINE      CODE     ---       #358
      01002576H   LINE      CODE     ---       #359
      0100257CH   LINE      CODE     ---       #360
      0100257CH   LINE      CODE     ---       #361
      01002586H   LINE      CODE     ---       #362
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 45


      01002586H   LINE      CODE     ---       #363
      01002588H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      01002608H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002608H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002608H   LINE      CODE     ---       #373
      01002608H   LINE      CODE     ---       #374
      01002608H   LINE      CODE     ---       #377
      0100260EH   LINE      CODE     ---       #378
      0100260EH   LINE      CODE     ---       #379
      01002610H   LINE      CODE     ---       #380
      01002613H   LINE      CODE     ---       #381
      01002617H   LINE      CODE     ---       #382
      01002617H   LINE      CODE     ---       #383
      0100261DH   LINE      CODE     ---       #384
      0100261DH   LINE      CODE     ---       #385
      0100261FH   LINE      CODE     ---       #386
      01002622H   LINE      CODE     ---       #387
      01002626H   LINE      CODE     ---       #388
      01002626H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      01002692H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002692H   LINE      CODE     ---       #443
      01002692H   LINE      CODE     ---       #444
      01002692H   LINE      CODE     ---       #445
      01002698H   LINE      CODE     ---       #446
      01002698H   LINE      CODE     ---       #447
      0100269BH   LINE      CODE     ---       #448
      0100269BH   LINE      CODE     ---       #449
      010026A3H   LINE      CODE     ---       #450
      010026A3H   LINE      CODE     ---       #451
      010026A5H   LINE      CODE     ---       #452
      010026A5H   LINE      CODE     ---       #453
      010026A5H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100270BH   BLOCK     CODE     ---       LVL=0
      0100270BH   LINE      CODE     ---       #534
      0100270BH   LINE      CODE     ---       #535
      0100270BH   LINE      CODE     ---       #536
      01002712H   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      010026F9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      010026F9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010026F9H   LINE      CODE     ---       #544
      010026F9H   LINE      CODE     ---       #545
      010026F9H   LINE      CODE     ---       #546
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 46


      010026F9H   LINE      CODE     ---       #548
      010026FDH   LINE      CODE     ---       #549
      010026FFH   LINE      CODE     ---       #550
      01002700H   LINE      CODE     ---       #551
      01002701H   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      01002702H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      01002702H   LINE      CODE     ---       #560
      01002702H   LINE      CODE     ---       #561
      01002702H   LINE      CODE     ---       #562
      01002707H   LINE      CODE     ---       #563
      0100270AH   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 47


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 48


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 49


      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01002441H   PUBLIC    CODE     ---       _FLASH_Erase
      0100240DH   PUBLIC    CODE     ---       _FLASH_Read
      010023D8H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 50


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 51


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 52


      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      010023D8H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      010023D8H   LINE      CODE     ---       #95
      010023DAH   LINE      CODE     ---       #96
      010023DAH   LINE      CODE     ---       #97
      010023DEH   LINE      CODE     ---       #98
      010023E0H   LINE      CODE     ---       #99
      010023E3H   LINE      CODE     ---       #101
      010023E6H   LINE      CODE     ---       #102
      010023E6H   LINE      CODE     ---       #103
      010023E8H   LINE      CODE     ---       #104
      010023E9H   LINE      CODE     ---       #105
      010023EEH   LINE      CODE     ---       #106
      010023EFH   LINE      CODE     ---       #107
      010023F0H   LINE      CODE     ---       #108
      010023F1H   LINE      CODE     ---       #109
      010023F2H   LINE      CODE     ---       #110
      010023F3H   LINE      CODE     ---       #111
      010023F4H   LINE      CODE     ---       #112
      010023F9H   LINE      CODE     ---       #113
      010023FBH   LINE      CODE     ---       #114
      010023FCH   LINE      CODE     ---       #116
      010023FCH   LINE      CODE     ---       #117
      01002401H   LINE      CODE     ---       #118
      01002402H   LINE      CODE     ---       #119
      01002403H   LINE      CODE     ---       #120
      01002404H   LINE      CODE     ---       #121
      01002405H   LINE      CODE     ---       #122
      01002406H   LINE      CODE     ---       #123
      01002407H   LINE      CODE     ---       #124
      0100240CH   LINE      CODE     ---       #125
      0100240CH   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      0100240DH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      0100240DH   LINE      CODE     ---       #138
      0100240FH   LINE      CODE     ---       #139
      0100240FH   LINE      CODE     ---       #140
      01002411H   LINE      CODE     ---       #141
      01002414H   LINE      CODE     ---       #142
      01002417H   LINE      CODE     ---       #143
      01002417H   LINE      CODE     ---       #144
      01002419H   LINE      CODE     ---       #145
      0100241AH   LINE      CODE     ---       #146
      0100241FH   LINE      CODE     ---       #147
      01002420H   LINE      CODE     ---       #148
      01002421H   LINE      CODE     ---       #149
      01002422H   LINE      CODE     ---       #150
      01002423H   LINE      CODE     ---       #151
      01002424H   LINE      CODE     ---       #152
      01002425H   LINE      CODE     ---       #153
      0100242AH   LINE      CODE     ---       #154
      0100242CH   LINE      CODE     ---       #155
      0100242EH   LINE      CODE     ---       #157
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 53


      0100242EH   LINE      CODE     ---       #158
      01002433H   LINE      CODE     ---       #159
      01002434H   LINE      CODE     ---       #160
      01002435H   LINE      CODE     ---       #161
      01002436H   LINE      CODE     ---       #162
      01002437H   LINE      CODE     ---       #163
      01002438H   LINE      CODE     ---       #164
      01002439H   LINE      CODE     ---       #165
      0100243EH   LINE      CODE     ---       #166
      0100243EH   LINE      CODE     ---       #167
      01002440H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      01002441H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01002441H   LINE      CODE     ---       #179
      01002443H   LINE      CODE     ---       #180
      01002443H   LINE      CODE     ---       #181
      01002445H   LINE      CODE     ---       #182
      01002448H   LINE      CODE     ---       #183
      0100244BH   LINE      CODE     ---       #184
      0100244BH   LINE      CODE     ---       #185
      0100244DH   LINE      CODE     ---       #186
      0100244EH   LINE      CODE     ---       #187
      01002453H   LINE      CODE     ---       #188
      01002454H   LINE      CODE     ---       #189
      01002455H   LINE      CODE     ---       #190
      01002456H   LINE      CODE     ---       #191
      01002457H   LINE      CODE     ---       #192
      01002458H   LINE      CODE     ---       #193
      01002459H   LINE      CODE     ---       #194
      0100245EH   LINE      CODE     ---       #195
      01002460H   LINE      CODE     ---       #196
      01002461H   LINE      CODE     ---       #198
      01002461H   LINE      CODE     ---       #199
      01002466H   LINE      CODE     ---       #200
      01002467H   LINE      CODE     ---       #201
      01002468H   LINE      CODE     ---       #202
      01002469H   LINE      CODE     ---       #203
      0100246AH   LINE      CODE     ---       #204
      0100246BH   LINE      CODE     ---       #205
      0100246CH   LINE      CODE     ---       #206
      01002471H   LINE      CODE     ---       #207
      01002471H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      01002662H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 54


      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 55


      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 56


      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002662H   BLOCK     CODE     ---       LVL=0
      01002662H   LINE      CODE     ---       #65
      01002662H   LINE      CODE     ---       #66
      01002662H   LINE      CODE     ---       #68
      01002669H   LINE      CODE     ---       #71
      0100266EH   LINE      CODE     ---       #72
      01002674H   LINE      CODE     ---       #75
      01002679H   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      020000B0H   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      020000AEH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      020000ACH   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000027H.1 PUBLIC    BIT      BIT       Delay_Over
      020000ABH   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      020000A9H   PUBLIC    XDATA    WORD      Delay_Time
      00000027H.0 PUBLIC    BIT      BIT       Get_String_Buff
      020000A7H   PUBLIC    XDATA    INT       Count_Toggle
      020000A5H   PUBLIC    XDATA    WORD      Delay_Time_Count
      020000A3H   PUBLIC    XDATA    WORD      Motor_Speed_Data
      020000A1H   PUBLIC    XDATA    INT       Num
      020000A0H   PUBLIC    XDATA    BYTE      K3_cnt
      0200009FH   PUBLIC    XDATA    BYTE      K2_cnt
      0200009EH   PUBLIC    XDATA    BYTE      K1_cnt
      0200009CH   PUBLIC    XDATA    WORD      longhit_cnt
      00000026H.7 PUBLIC    BIT      BIT       Center_Line_Control
      00000026H.6 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 57


      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 58


      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 59


      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      010026CCH   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      0100203FH   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 60


      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 61


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 62


      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100203FH   BLOCK     CODE     ---       LVL=0
      0100203FH   LINE      CODE     ---       #42
      0100203FH   LINE      CODE     ---       #43
      0100203FH   LINE      CODE     ---       #44
      01002042H   LINE      CODE     ---       #45
      01002045H   LINE      CODE     ---       #46
      01002048H   LINE      CODE     ---       #47
      0100204BH   LINE      CODE     ---       #48
      0100204EH   LINE      CODE     ---       #49
      01002051H   LINE      CODE     ---       #50
      01002054H   LINE      CODE     ---       #51
      01002057H   LINE      CODE     ---       #77
      0100205CH   LINE      CODE     ---       #78
      0100205FH   LINE      CODE     ---       #79
      01002066H   LINE      CODE     ---       #81
      0100206BH   LINE      CODE     ---       #82
      0100206EH   LINE      CODE     ---       #83
      01002075H   LINE      CODE     ---       #85
      0100207AH   LINE      CODE     ---       #86
      0100207DH   LINE      CODE     ---       #87
      01002084H   LINE      CODE     ---       #90
      01002089H   LINE      CODE     ---       #91
      0100208CH   LINE      CODE     ---       #92
      01002093H   LINE      CODE     ---       #94
      01002098H   LINE      CODE     ---       #95
      0100209BH   LINE      CODE     ---       #96
      010020A2H   LINE      CODE     ---       #100
      010020A7H   LINE      CODE     ---       #101
      010020AAH   LINE      CODE     ---       #102
      010020ACH   LINE      CODE     ---       #105
      010020AEH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 63


      010026CCH   BLOCK     CODE     ---       LVL=0
      010026CCH   LINE      CODE     ---       #131
      010026CCH   LINE      CODE     ---       #132
      010026CCH   LINE      CODE     ---       #134
      010026D2H   LINE      CODE     ---       #135
      010026D5H   LINE      CODE     ---       #138
      010026D9H   LINE      CODE     ---       #139
      010026DCH   LINE      CODE     ---       #142
      010026DEH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      010024C9H   PUBLIC    CODE     ---       TMR1_Config
      0100249FH   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 64


      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 65


      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100249FH   BLOCK     CODE     ---       LVL=0
      0100249FH   LINE      CODE     ---       #11
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 66


      0100249FH   LINE      CODE     ---       #12
      0100249FH   LINE      CODE     ---       #16
      010024A7H   LINE      CODE     ---       #20
      010024ADH   LINE      CODE     ---       #24
      010024B6H   LINE      CODE     ---       #29
      010024BBH   LINE      CODE     ---       #34
      010024C1H   LINE      CODE     ---       #35
      010024C4H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      010024C9H   BLOCK     CODE     ---       LVL=0
      010024C9H   LINE      CODE     ---       #50
      010024C9H   LINE      CODE     ---       #51
      010024C9H   LINE      CODE     ---       #55
      010024D2H   LINE      CODE     ---       #59
      010024D9H   LINE      CODE     ---       #63
      010024E2H   LINE      CODE     ---       #68
      010024E7H   LINE      CODE     ---       #73
      010024EAH   LINE      CODE     ---       #74
      010024EDH   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      0200001FH   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      0100217BH   PUBLIC    CODE     ---       _UART_Send_String
      01000056H   PUBLIC    CODE     ---       _putchar
      0100235FH   PUBLIC    CODE     ---       UART_1_Config
      01002233H   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 67


      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 68


      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 69


      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002233H   BLOCK     CODE     ---       LVL=0
      01002233H   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01002233H   LINE      CODE     ---       #42
      01002233H   LINE      CODE     ---       #43
      01002233H   LINE      CODE     ---       #44
      0100223DH   LINE      CODE     ---       #45
      01002245H   LINE      CODE     ---       #143
      0100224EH   LINE      CODE     ---       #144
      01002253H   LINE      CODE     ---       #147
      01002258H   LINE      CODE     ---       #148
      0100225DH   LINE      CODE     ---       #152
      01002268H   LINE      CODE     ---       #153
      0100226BH   LINE      CODE     ---       #156
      01002271H   LINE      CODE     ---       #157
      01002276H   LINE      CODE     ---       #159
      0100227BH   LINE      CODE     ---       #160
      0100227EH   LINE      CODE     ---       #161
      01002281H   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      0100235FH   BLOCK     CODE     ---       LVL=0
      0100235FH   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      0100235FH   LINE      CODE     ---       #223
      0100235FH   LINE      CODE     ---       #224
      0100235FH   LINE      CODE     ---       #225
      01002369H   LINE      CODE     ---       #226
      01002371H   LINE      CODE     ---       #324
      0100237AH   LINE      CODE     ---       #325
      0100237FH   LINE      CODE     ---       #328
      01002384H   LINE      CODE     ---       #329
      01002389H   LINE      CODE     ---       #333
      01002394H   LINE      CODE     ---       #334
      01002397H   LINE      CODE     ---       #337
      0100239DH   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000056H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005DH   LINE      CODE     ---       #363
      01000060H   LINE      CODE     ---       #364
      01000062H   LINE      CODE     ---       #365
      ---         BLOCKEND  ---      ---       LVL=0
      00000001H   SYMBOL    DATA     ---       s
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 70



      0100217BH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000020H   SYMBOL    XDATA    ---       String
      02000023H   SYMBOL    XDATA    BYTE      Length
      01002186H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      0100217BH   LINE      CODE     ---       #408
      01002186H   LINE      CODE     ---       #409
      01002186H   LINE      CODE     ---       #410
      01002188H   LINE      CODE     ---       #411
      01002192H   LINE      CODE     ---       #412
      01002192H   LINE      CODE     ---       #413
      01002195H   LINE      CODE     ---       #414
      01002195H   LINE      CODE     ---       #415
      010021AAH   LINE      CODE     ---       #416
      010021AFH   LINE      CODE     ---       #417
      010021B2H   LINE      CODE     ---       #418
      010021B2H   LINE      CODE     ---       #419
      010021B7H   LINE      CODE     ---       #420
      010021B7H   LINE      CODE     ---       #421
      010021CCH   LINE      CODE     ---       #422
      010021D1H   LINE      CODE     ---       #423
      010021D4H   LINE      CODE     ---       #424
      010021D4H   LINE      CODE     ---       #425
      010021D7H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ISR
      020000C4H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      020000C2H   PUBLIC    XDATA    INT       Num_Forward_Pulse
      01000037H   PUBLIC    CODE     ---       SPI_IRQHandler
      01000036H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000032H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000031H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000030H   PUBLIC    CODE     ---       EPWM_IRQHandler
      0100002FH   PUBLIC    CODE     ---       Timer4_IRQHandler
      0100002EH   PUBLIC    CODE     ---       Timer3_IRQHandler
      0100002AH   PUBLIC    CODE     ---       ACMP_IRQHandler
      01000029H   PUBLIC    CODE     ---       LSE_IRQHandler
      01000028H   PUBLIC    CODE     ---       LVD_IRQHandler
      01000027H   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001F54H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001EDCH   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000026H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      010021D8H   PUBLIC    CODE     ---       UART0_IRQHandler
      01002713H   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      0100185AH   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 71


      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 72


      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 73


      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #70
      0100000AH   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

      0100185AH   BLOCK     CODE     ---       LVL=0
      0100185AH   LINE      CODE     ---       #82
      01001869H   LINE      CODE     ---       #84
      0100186CH   LINE      CODE     ---       #85
      0100186FH   LINE      CODE     ---       #87
      01001871H   LINE      CODE     ---       #88
      0100187FH   LINE      CODE     ---       #89
      0100188DH   LINE      CODE     ---       #90
      010018A2H   LINE      CODE     ---       #92
      010018C2H   LINE      CODE     ---       #93
      010018C2H   LINE      CODE     ---       #94
      010018C2H   LINE      CODE     ---       #95
      010018C2H   LINE      CODE     ---       #96
      010018CEH   LINE      CODE     ---       #97
      010018CEH   LINE      CODE     ---       #98
      010018CEH   LINE      CODE     ---       #99
      010018CEH   LINE      CODE     ---       #100
      010018CEH   LINE      CODE     ---       #101
      010018CEH   LINE      CODE     ---       #102
      010018D0H   LINE      CODE     ---       #103
      010018D0H   LINE      CODE     ---       #104
      010018DCH   LINE      CODE     ---       #105
      010018DCH   LINE      CODE     ---       #106
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 74


      010018DCH   LINE      CODE     ---       #107
      010018DCH   LINE      CODE     ---       #108
      010018DCH   LINE      CODE     ---       #109
      010018DEH   LINE      CODE     ---       #110
      010018DEH   LINE      CODE     ---       #111
      010018DEH   LINE      CODE     ---       #112
      010018EAH   LINE      CODE     ---       #113
      010018EAH   LINE      CODE     ---       #114
      010018EAH   LINE      CODE     ---       #115
      010018EAH   LINE      CODE     ---       #116
      010018EAH   LINE      CODE     ---       #117
      010018EAH   LINE      CODE     ---       #118
      010018ECH   LINE      CODE     ---       #119
      010018ECH   LINE      CODE     ---       #120
      010018ECH   LINE      CODE     ---       #121
      010018F8H   LINE      CODE     ---       #122
      010018F8H   LINE      CODE     ---       #123
      010018F8H   LINE      CODE     ---       #124
      010018F8H   LINE      CODE     ---       #125
      010018F8H   LINE      CODE     ---       #126
      010018F8H   LINE      CODE     ---       #127
      010018FAH   LINE      CODE     ---       #128
      010018FAH   LINE      CODE     ---       #129
      010018FAH   LINE      CODE     ---       #130
      01001906H   LINE      CODE     ---       #131
      01001906H   LINE      CODE     ---       #132
      01001908H   LINE      CODE     ---       #133
      0100190EH   LINE      CODE     ---       #134
      0100190EH   LINE      CODE     ---       #135
      0100190EH   LINE      CODE     ---       #136
      0100190EH   LINE      CODE     ---       #137
      0100190EH   LINE      CODE     ---       #138
      0100190EH   LINE      CODE     ---       #139
      0100190EH   LINE      CODE     ---       #141
      01001911H   LINE      CODE     ---       #142
      01001911H   LINE      CODE     ---       #143
      0100191AH   LINE      CODE     ---       #144
      0100191CH   LINE      CODE     ---       #146
      0100191CH   LINE      CODE     ---       #147
      01001921H   LINE      CODE     ---       #148
      01001921H   LINE      CODE     ---       #150
      0100192EH   LINE      CODE     ---       #151
      0100192EH   LINE      CODE     ---       #152
      01001930H   LINE      CODE     ---       #153
      0100193EH   LINE      CODE     ---       #154
      01001940H   LINE      CODE     ---       #156
      01001940H   LINE      CODE     ---       #157
      01001942H   LINE      CODE     ---       #158
      01001949H   LINE      CODE     ---       #159
      01001949H   LINE      CODE     ---       #161
      0100194CH   LINE      CODE     ---       #162
      0100194CH   LINE      CODE     ---       #163
      0100195AH   LINE      CODE     ---       #164
      0100196FH   LINE      CODE     ---       #165
      0100196FH   LINE      CODE     ---       #166
      01001971H   LINE      CODE     ---       #167
      01001971H   LINE      CODE     ---       #168
      01001971H   LINE      CODE     ---       #169
      01001973H   LINE      CODE     ---       #171
      01001973H   LINE      CODE     ---       #172
      0100197AH   LINE      CODE     ---       #173
      0100197AH   LINE      CODE     ---       #174
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 75


      01000012H   LINE      CODE     ---       #183
      01000012H   LINE      CODE     ---       #186
      ---         BLOCKEND  ---      ---       LVL=0

      01002713H   BLOCK     CODE     ---       LVL=0
      01002713H   LINE      CODE     ---       #195
      01002713H   LINE      CODE     ---       #197
      01002716H   LINE      CODE     ---       #198
      01002719H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      010021D8H   BLOCK     CODE     ---       LVL=0
      010021D8H   LINE      CODE     ---       #210
      010021F5H   LINE      CODE     ---       #212
      010021FDH   LINE      CODE     ---       #213
      010021FDH   LINE      CODE     ---       #214
      010021FFH   LINE      CODE     ---       #215
      01002204H   LINE      CODE     ---       #216
      01002213H   LINE      CODE     ---       #217
      01002218H   LINE      CODE     ---       #218
      01002218H   LINE      CODE     ---       #219
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #228
      0100001AH   LINE      CODE     ---       #231
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #240
      01000022H   LINE      CODE     ---       #243
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #252
      01000026H   LINE      CODE     ---       #255
      ---         BLOCKEND  ---      ---       LVL=0

      01001EDCH   BLOCK     CODE     ---       LVL=0
      01001EDCH   LINE      CODE     ---       #264
      01001EEBH   LINE      CODE     ---       #267
      01001EEEH   LINE      CODE     ---       #270
      01001EF1H   LINE      CODE     ---       #271
      01001EF1H   LINE      CODE     ---       #273
      01001EF4H   LINE      CODE     ---       #274
      01001EF4H   LINE      CODE     ---       #276
      01001F03H   LINE      CODE     ---       #278
      01001F05H   LINE      CODE     ---       #280
      01001F07H   LINE      CODE     ---       #281
      01001F07H   LINE      CODE     ---       #282
      01001F09H   LINE      CODE     ---       #284
      01001F09H   LINE      CODE     ---       #286
      01001F0CH   LINE      CODE     ---       #287
      01001F0CH   LINE      CODE     ---       #289
      01001F27H   LINE      CODE     ---       #291
      01001F43H   LINE      CODE     ---       #292
      01001F43H   LINE      CODE     ---       #294
      01001F45H   LINE      CODE     ---       #295
      01001F45H   LINE      CODE     ---       #297
      01001F47H   LINE      CODE     ---       #298
      01001F47H   LINE      CODE     ---       #299
      01001F47H   LINE      CODE     ---       #300
      ---         BLOCKEND  ---      ---       LVL=0

      01001F54H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 76


      01001F54H   LINE      CODE     ---       #309
      01001F63H   LINE      CODE     ---       #311
      01001F66H   LINE      CODE     ---       #313
      01001F69H   LINE      CODE     ---       #314
      01001F69H   LINE      CODE     ---       #316
      01001F6CH   LINE      CODE     ---       #317
      01001F6CH   LINE      CODE     ---       #318
      01001F7BH   LINE      CODE     ---       #319
      01001F7DH   LINE      CODE     ---       #320
      01001F7FH   LINE      CODE     ---       #321
      01001F7FH   LINE      CODE     ---       #322
      01001F81H   LINE      CODE     ---       #324
      01001F81H   LINE      CODE     ---       #325
      01001F84H   LINE      CODE     ---       #326
      01001F84H   LINE      CODE     ---       #327
      01001F9FH   LINE      CODE     ---       #328
      01001FBBH   LINE      CODE     ---       #329
      01001FBBH   LINE      CODE     ---       #330
      01001FBDH   LINE      CODE     ---       #331
      01001FBDH   LINE      CODE     ---       #332
      01001FBFH   LINE      CODE     ---       #333
      01001FBFH   LINE      CODE     ---       #334
      01001FBFH   LINE      CODE     ---       #335
      ---         BLOCKEND  ---      ---       LVL=0

      01000027H   BLOCK     CODE     ---       LVL=0
      01000027H   LINE      CODE     ---       #344
      01000027H   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0

      01000028H   BLOCK     CODE     ---       LVL=0
      01000028H   LINE      CODE     ---       #356
      01000028H   LINE      CODE     ---       #359
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #368
      01000029H   LINE      CODE     ---       #371
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #380
      0100002AH   LINE      CODE     ---       #383
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #392
      0100002EH   LINE      CODE     ---       #395
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #404
      0100002FH   LINE      CODE     ---       #407
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #416
      01000030H   LINE      CODE     ---       #419
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #428
      01000031H   LINE      CODE     ---       #431
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 77


      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #440
      01000032H   LINE      CODE     ---       #443
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #452
      01000036H   LINE      CODE     ---       #455
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #464
      01000037H   LINE      CODE     ---       #467
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      0200009BH   PUBLIC    XDATA    BYTE      Data_Length
      0200007BH   PUBLIC    XDATA    ---       UART_Get_String
      01001AB1H   PUBLIC    CODE     ---       UART_Data_Process
      01001FCCH   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      010025A9H   PUBLIC    CODE     ---       UART_Data_Init
      01002720H   PUBLIC    CODE     ---       Clean_UART_Data_Length
      0100271AH   PUBLIC    CODE     ---       Return_UART_Data_Length
      01002589H   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 78


      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 79


      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 80


      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      01002742H   SYMBOL    CONST    ---       _?ix1000
      01002745H   SYMBOL    CONST    ---       _?ix1001
      01002748H   SYMBOL    CONST    ---       _?ix1002

      01002589H   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      01002589H   LINE      CODE     ---       #12
      01002589H   LINE      CODE     ---       #13
      01002589H   LINE      CODE     ---       #14
      01002596H   LINE      CODE     ---       #15
      0100259CH   LINE      CODE     ---       #16
      010025A6H   LINE      CODE     ---       #17
      010025A6H   LINE      CODE     ---       #18
      010025A8H   LINE      CODE     ---       #19
      010025A8H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      0100271AH   BLOCK     CODE     ---       LVL=0
      0100271AH   LINE      CODE     ---       #24
      0100271AH   LINE      CODE     ---       #25
      0100271AH   LINE      CODE     ---       #26
      0100271FH   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      01002720H   BLOCK     CODE     ---       LVL=0
      01002720H   LINE      CODE     ---       #30
      01002720H   LINE      CODE     ---       #31
      01002720H   LINE      CODE     ---       #32
      01002725H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      010025A9H   BLOCK     CODE     ---       LVL=0
      010025A9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010025A9H   LINE      CODE     ---       #36
      010025A9H   LINE      CODE     ---       #37
      010025A9H   LINE      CODE     ---       #39
      010025AEH   LINE      CODE     ---       #40
      010025B9H   LINE      CODE     ---       #41
      010025B9H   LINE      CODE     ---       #42
      010025C5H   LINE      CODE     ---       #43
      010025C8H   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001FCCH   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    BYTE      CMD_No
      01001FD1H   BLOCK     CODE     NEAR LAB  LVL=1
      02000012H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000015H   SYMBOL    XDATA    ---       UART_Error_Data
      02000018H   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001FCCH   LINE      CODE     ---       #61
      01001FD1H   LINE      CODE     ---       #62
      01001FD1H   LINE      CODE     ---       #63
      01001FE4H   LINE      CODE     ---       #64
      01001FF7H   LINE      CODE     ---       #65
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 81


      0100200AH   LINE      CODE     ---       #66
      01002018H   LINE      CODE     ---       #67
      01002018H   LINE      CODE     ---       #69
      01002018H   LINE      CODE     ---       #70
      01002018H   LINE      CODE     ---       #71
      0100201EH   LINE      CODE     ---       #72
      0100201EH   LINE      CODE     ---       #73
      01002020H   LINE      CODE     ---       #75
      01002020H   LINE      CODE     ---       #76
      01002020H   LINE      CODE     ---       #77
      0100202BH   LINE      CODE     ---       #78
      0100202BH   LINE      CODE     ---       #79
      0100202DH   LINE      CODE     ---       #81
      0100202DH   LINE      CODE     ---       #82
      0100202DH   LINE      CODE     ---       #83
      0100203EH   LINE      CODE     ---       #86
      0100203EH   LINE      CODE     ---       #87
      0100203EH   LINE      CODE     ---       #88
      0100203EH   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      01001AB1H   BLOCK     CODE     ---       LVL=0
      01001AB1H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      01001AB1H   LINE      CODE     ---       #92
      01001AB1H   LINE      CODE     ---       #93
      01001AB1H   LINE      CODE     ---       #96
      01001AB4H   LINE      CODE     ---       #98
      01001AB9H   LINE      CODE     ---       #99
      01001AB9H   LINE      CODE     ---       #100
      01001ABCH   LINE      CODE     ---       #101
      01001ABFH   LINE      CODE     ---       #102
      01001ABFH   LINE      CODE     ---       #104
      01001ACAH   LINE      CODE     ---       #105
      01001ACAH   LINE      CODE     ---       #106
      01001AD9H   LINE      CODE     ---       #107
      01001AD9H   LINE      CODE     ---       #109
      01001ADBH   LINE      CODE     ---       #110
      01001ADEH   LINE      CODE     ---       #111
      01001AECH   LINE      CODE     ---       #112
      01001AECH   LINE      CODE     ---       #114
      01001AEEH   LINE      CODE     ---       #115
      01001AF1H   LINE      CODE     ---       #116
      01001AFFH   LINE      CODE     ---       #117
      01001AFFH   LINE      CODE     ---       #119
      01001B01H   LINE      CODE     ---       #120
      01001B04H   LINE      CODE     ---       #121
      01001B15H   LINE      CODE     ---       #122
      01001B15H   LINE      CODE     ---       #124
      01001B17H   LINE      CODE     ---       #125
      01001B1AH   LINE      CODE     ---       #126
      01001B28H   LINE      CODE     ---       #127
      01001B28H   LINE      CODE     ---       #129
      01001B2AH   LINE      CODE     ---       #130
      01001B2DH   LINE      CODE     ---       #131
      01001B3BH   LINE      CODE     ---       #132
      01001B3BH   LINE      CODE     ---       #134
      01001B3DH   LINE      CODE     ---       #135
      01001B40H   LINE      CODE     ---       #136
      01001B51H   LINE      CODE     ---       #137
      01001B51H   LINE      CODE     ---       #139
      01001B53H   LINE      CODE     ---       #140
      01001B55H   LINE      CODE     ---       #141
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 82


      01001B63H   LINE      CODE     ---       #142
      01001B63H   LINE      CODE     ---       #144
      01001B65H   LINE      CODE     ---       #145
      01001B67H   LINE      CODE     ---       #146
      01001B75H   LINE      CODE     ---       #147
      01001B75H   LINE      CODE     ---       #149
      01001B77H   LINE      CODE     ---       #150
      01001B79H   LINE      CODE     ---       #151
      01001B8AH   LINE      CODE     ---       #152
      01001B8AH   LINE      CODE     ---       #154
      01001B8CH   LINE      CODE     ---       #155
      01001B8EH   LINE      CODE     ---       #156
      01001B9CH   LINE      CODE     ---       #157
      01001B9CH   LINE      CODE     ---       #159
      01001B9EH   LINE      CODE     ---       #160
      01001BA0H   LINE      CODE     ---       #161
      01001BAEH   LINE      CODE     ---       #162
      01001BAEH   LINE      CODE     ---       #164
      01001BB0H   LINE      CODE     ---       #165
      01001BB2H   LINE      CODE     ---       #166
      01001BBEH   LINE      CODE     ---       #167
      01001BBEH   LINE      CODE     ---       #169
      01001BC0H   LINE      CODE     ---       #170
      01001BC2H   LINE      CODE     ---       #172
      01001BC2H   LINE      CODE     ---       #174
      01001BC4H   LINE      CODE     ---       #175
      01001BC4H   LINE      CODE     ---       #176
      01001BC4H   LINE      CODE     ---       #178
      01001BC7H   LINE      CODE     ---       #180
      01001BC9H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000026H.5 PUBLIC    BIT      BIT       K5_Press
      00000026H.4 PUBLIC    BIT      BIT       K4_Press
      00000026H.3 PUBLIC    BIT      BIT       K3_Press
      00000026H.2 PUBLIC    BIT      BIT       K2_Press
      00000026H.1 PUBLIC    BIT      BIT       K1_Press
      020000C1H   PUBLIC    XDATA    BYTE      K5_Count
      020000C0H   PUBLIC    XDATA    BYTE      K4_Count
      020000BFH   PUBLIC    XDATA    BYTE      K3_Count
      020000BEH   PUBLIC    XDATA    BYTE      K2_Count
      020000BDH   PUBLIC    XDATA    BYTE      K1_Count
      020000BCH   PUBLIC    XDATA    BYTE      Key_Buff
      0100239EH   PUBLIC    CODE     ---       Key_Buff_Return
      01001987H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 83


      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 84


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 85


      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001987H   BLOCK     CODE     ---       LVL=0
      01001987H   LINE      CODE     ---       #20
      01001987H   LINE      CODE     ---       #21
      01001987H   LINE      CODE     ---       #23
      0100198AH   LINE      CODE     ---       #24
      0100198AH   LINE      CODE     ---       #25
      0100198DH   LINE      CODE     ---       #26
      0100198DH   LINE      CODE     ---       #27
      0100199AH   LINE      CODE     ---       #28
      0100199AH   LINE      CODE     ---       #29
      0100199CH   LINE      CODE     ---       #30
      0100199EH   LINE      CODE     ---       #31
      010019A0H   LINE      CODE     ---       #43
      010019A0H   LINE      CODE     ---       #44
      010019A3H   LINE      CODE     ---       #45
      010019A3H   LINE      CODE     ---       #46
      010019B0H   LINE      CODE     ---       #47
      010019B0H   LINE      CODE     ---       #48
      010019B2H   LINE      CODE     ---       #49
      010019B4H   LINE      CODE     ---       #50
      010019B6H   LINE      CODE     ---       #52
      010019B6H   LINE      CODE     ---       #53
      010019BCH   LINE      CODE     ---       #54
      010019BCH   LINE      CODE     ---       #55
      010019BEH   LINE      CODE     ---       #57
      010019BEH   LINE      CODE     ---       #58
      010019C3H   LINE      CODE     ---       #59
      010019C3H   LINE      CODE     ---       #60
      010019C3H   LINE      CODE     ---       #63
      010019C6H   LINE      CODE     ---       #64
      010019C6H   LINE      CODE     ---       #65
      010019C9H   LINE      CODE     ---       #66
      010019C9H   LINE      CODE     ---       #67
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 86


      010019D6H   LINE      CODE     ---       #68
      010019D6H   LINE      CODE     ---       #69
      010019D8H   LINE      CODE     ---       #70
      010019DAH   LINE      CODE     ---       #71
      010019DCH   LINE      CODE     ---       #83
      010019DCH   LINE      CODE     ---       #84
      010019DFH   LINE      CODE     ---       #85
      010019DFH   LINE      CODE     ---       #86
      010019ECH   LINE      CODE     ---       #87
      010019ECH   LINE      CODE     ---       #88
      010019EEH   LINE      CODE     ---       #89
      010019F0H   LINE      CODE     ---       #90
      010019F2H   LINE      CODE     ---       #92
      010019F2H   LINE      CODE     ---       #93
      010019F8H   LINE      CODE     ---       #94
      010019F8H   LINE      CODE     ---       #95
      010019FAH   LINE      CODE     ---       #97
      010019FAH   LINE      CODE     ---       #98
      010019FFH   LINE      CODE     ---       #99
      010019FFH   LINE      CODE     ---       #100
      010019FFH   LINE      CODE     ---       #103
      01001A02H   LINE      CODE     ---       #104
      01001A02H   LINE      CODE     ---       #105
      01001A05H   LINE      CODE     ---       #106
      01001A05H   LINE      CODE     ---       #107
      01001A12H   LINE      CODE     ---       #108
      01001A12H   LINE      CODE     ---       #109
      01001A14H   LINE      CODE     ---       #110
      01001A16H   LINE      CODE     ---       #111
      01001A18H   LINE      CODE     ---       #123
      01001A18H   LINE      CODE     ---       #124
      01001A1BH   LINE      CODE     ---       #125
      01001A1BH   LINE      CODE     ---       #126
      01001A28H   LINE      CODE     ---       #127
      01001A28H   LINE      CODE     ---       #128
      01001A2AH   LINE      CODE     ---       #129
      01001A2CH   LINE      CODE     ---       #130
      01001A2EH   LINE      CODE     ---       #132
      01001A2EH   LINE      CODE     ---       #133
      01001A34H   LINE      CODE     ---       #134
      01001A34H   LINE      CODE     ---       #135
      01001A36H   LINE      CODE     ---       #137
      01001A36H   LINE      CODE     ---       #138
      01001A3BH   LINE      CODE     ---       #139
      01001A3BH   LINE      CODE     ---       #140
      01001A3BH   LINE      CODE     ---       #143
      01001A3EH   LINE      CODE     ---       #144
      01001A3EH   LINE      CODE     ---       #145
      01001A41H   LINE      CODE     ---       #146
      01001A41H   LINE      CODE     ---       #147
      01001A4EH   LINE      CODE     ---       #148
      01001A4EH   LINE      CODE     ---       #149
      01001A50H   LINE      CODE     ---       #150
      01001A52H   LINE      CODE     ---       #151
      01001A54H   LINE      CODE     ---       #163
      01001A54H   LINE      CODE     ---       #164
      01001A57H   LINE      CODE     ---       #165
      01001A57H   LINE      CODE     ---       #166
      01001A64H   LINE      CODE     ---       #167
      01001A64H   LINE      CODE     ---       #168
      01001A66H   LINE      CODE     ---       #169
      01001A68H   LINE      CODE     ---       #170
      01001A6AH   LINE      CODE     ---       #172
      01001A6AH   LINE      CODE     ---       #173
      01001A70H   LINE      CODE     ---       #174
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 87


      01001A70H   LINE      CODE     ---       #175
      01001A72H   LINE      CODE     ---       #177
      01001A72H   LINE      CODE     ---       #178
      01001A77H   LINE      CODE     ---       #179
      01001A77H   LINE      CODE     ---       #180
      01001A77H   LINE      CODE     ---       #183
      01001A7AH   LINE      CODE     ---       #184
      01001A7AH   LINE      CODE     ---       #185
      01001A7DH   LINE      CODE     ---       #186
      01001A7DH   LINE      CODE     ---       #187
      01001A8AH   LINE      CODE     ---       #188
      01001A8AH   LINE      CODE     ---       #189
      01001A8CH   LINE      CODE     ---       #190
      01001A8EH   LINE      CODE     ---       #191
      01001A8FH   LINE      CODE     ---       #203
      01001A8FH   LINE      CODE     ---       #204
      01001A92H   LINE      CODE     ---       #205
      01001A92H   LINE      CODE     ---       #206
      01001A9FH   LINE      CODE     ---       #207
      01001A9FH   LINE      CODE     ---       #208
      01001AA1H   LINE      CODE     ---       #209
      01001AA3H   LINE      CODE     ---       #210
      01001AA4H   LINE      CODE     ---       #212
      01001AA4H   LINE      CODE     ---       #213
      01001AAAH   LINE      CODE     ---       #214
      01001AAAH   LINE      CODE     ---       #215
      01001AABH   LINE      CODE     ---       #217
      01001AABH   LINE      CODE     ---       #218
      01001AB0H   LINE      CODE     ---       #219
      01001AB0H   LINE      CODE     ---       #220
      01001AB0H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      0100239EH   BLOCK     CODE     ---       LVL=0
      0100239EH   LINE      CODE     ---       #224
      0100239EH   LINE      CODE     ---       #225
      0100239EH   LINE      CODE     ---       #226
      010023A3H   LINE      CODE     ---       #228
      010023AAH   LINE      CODE     ---       #229
      010023B4H   LINE      CODE     ---       #230
      010023BEH   LINE      CODE     ---       #231
      010023C8H   LINE      CODE     ---       #232
      010023D2H   LINE      CODE     ---       #234
      010023D7H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      0100171FH   PUBLIC    CODE     ---       _ADC
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 88


      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 89


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 90


      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100171FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0100171FH   BLOCK     CODE     NEAR LAB  LVL=1
      020000B1H   SYMBOL    XDATA    ---       filter_buffer
      020000BBH   SYMBOL    XDATA    BYTE      filter_index
      02000011H   SYMBOL    XDATA    DWORD     sum
      02000015H   SYMBOL    XDATA    WORD      adc_result
      02000017H   SYMBOL    XDATA    BYTE      i
      02000018H   SYMBOL    XDATA    FLOAT     v_adc
      0200001CH   SYMBOL    XDATA    FLOAT     v_bat
      ---         BLOCKEND  ---      ---       LVL=1
      0100171FH   LINE      CODE     ---       #7
      0100171FH   LINE      CODE     ---       #8
      0100171FH   LINE      CODE     ---       #12
      01001729H   LINE      CODE     ---       #18
      0100172BH   LINE      CODE     ---       #19
      0100172EH   LINE      CODE     ---       #21
      01001731H   LINE      CODE     ---       #22
      01001736H   LINE      CODE     ---       #23
      01001741H   LINE      CODE     ---       #26
      01001756H   LINE      CODE     ---       #27
      01001769H   LINE      CODE     ---       #28
      0100177BH   LINE      CODE     ---       #29
      0100177BH   LINE      CODE     ---       #30
      010017B2H   LINE      CODE     ---       #31
      010017BAH   LINE      CODE     ---       #32
      010017D9H   LINE      CODE     ---       #35
      010017FCH   LINE      CODE     ---       #36
      01001825H   LINE      CODE     ---       #38
      01001851H   LINE      CODE     ---       #39
      01001859H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 91


      ---         MODULE    ---      ---       MAIN
      00000024H.3 PUBLIC    BIT      BIT       speedup
      00000024H.2 PUBLIC    BIT      BIT       longhit
      0200007AH   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.1 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.0 PUBLIC    BIT      BIT       direction_changed
      00000023H.7 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000078H   PUBLIC    XDATA    WORD      BatV
      00000023H.6 PUBLIC    BIT      BIT       key3_long_started
      00000023H.5 PUBLIC    BIT      BIT       key1_long_started
      00000023H.4 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.3 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.2 PUBLIC    BIT      BIT       Delay_Open
      02000077H   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.1 PUBLIC    BIT      BIT       charge_flash
      02000076H   PUBLIC    XDATA    BYTE      last_direction
      00000023H.0 PUBLIC    BIT      BIT       key3_pressed
      00000022H.7 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000022H.6 PUBLIC    BIT      BIT       key1_pressed
      02000075H   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.5 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.4 PUBLIC    BIT      BIT       ledonoff
      02000073H   PUBLIC    XDATA    WORD      original_speed
      00000022H.3 PUBLIC    BIT      BIT       key3_handle
      02000072H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000070H   PUBLIC    XDATA    INT       Self_Check
      00000022H.2 PUBLIC    BIT      BIT       key1_handle
      0200006EH   PUBLIC    XDATA    WORD      key3_duration
      0200006CH   PUBLIC    XDATA    WORD      dly
      00000022H.1 PUBLIC    BIT      BIT       k3_released
      0200006AH   PUBLIC    XDATA    WORD      key1_duration
      00000022H.0 PUBLIC    BIT      BIT       k2_released
      02000068H   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000021H.7 PUBLIC    BIT      BIT       batlow1
      02000066H   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.6 PUBLIC    BIT      BIT       auto_rotate_mode
      02000065H   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.5 PUBLIC    BIT      BIT       Charg_State_Buff
      00000021H.4 PUBLIC    BIT      BIT       led_flash_state
      02000063H   PUBLIC    XDATA    WORD      led_flash_timer
      02000062H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      00000021H.3 PUBLIC    BIT      BIT       need_led_flash
      02000060H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      0200005EH   PUBLIC    XDATA    WORD      timer_1ms_count
      0200005CH   PUBLIC    XDATA    WORD      speedup_cnt
      0200005AH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      02000058H   PUBLIC    XDATA    WORD      key1_press_time
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000054H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000052H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010015B4H   PUBLIC    CODE     ---       Key_Interrupt_Process
      01001BCAH   PUBLIC    CODE     ---       LED_Control
      01002519H   PUBLIC    CODE     ---       Restore_dly
      01002472H   PUBLIC    CODE     ---       _Store_dly
      010020AFH   PUBLIC    CODE     ---       Battery_Check
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 92


      01002318H   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001E49H   PUBLIC    CODE     ---       _Motor_Step_Control
      010025C9H   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 93


      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 94


      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000024H.4 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000000H   SYMBOL    XDATA    INT       Key_Input
      02000002H   SYMBOL    XDATA    INT       Charge_Input
      02000004H   SYMBOL    XDATA    INT       Key_State
      02000006H   SYMBOL    XDATA    INT       Key_State_Save
      02000008H   SYMBOL    XDATA    INT       Charge_State_Save
      0200000AH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000024H.5 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000024H.6 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200000CH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 95


      0200000EH   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000024H.7 SYMBOL    BIT      BIT       Voltage_Low
      0200000FH   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #137
      010000B6H   LINE      CODE     ---       #138
      010000B6H   LINE      CODE     ---       #144
      010000B8H   LINE      CODE     ---       #147
      010000BAH   LINE      CODE     ---       #148
      010000C1H   LINE      CODE     ---       #151
      010000C4H   LINE      CODE     ---       #152
      010000CBH   LINE      CODE     ---       #154
      010000CEH   LINE      CODE     ---       #156
      010000D1H   LINE      CODE     ---       #157
      010000D4H   LINE      CODE     ---       #159
      010000D7H   LINE      CODE     ---       #160
      010000DAH   LINE      CODE     ---       #162
      010000DCH   LINE      CODE     ---       #163
      010000E3H   LINE      CODE     ---       #164
      010000E7H   LINE      CODE     ---       #165
      010000E9H   LINE      CODE     ---       #166
      010000EFH   LINE      CODE     ---       #167
      010000F5H   LINE      CODE     ---       #168
      010000FBH   LINE      CODE     ---       #169
      010000FDH   LINE      CODE     ---       #170
      01000108H   LINE      CODE     ---       #171
      0100011CH   LINE      CODE     ---       #172
      0100011CH   LINE      CODE     ---       #173
      01000125H   LINE      CODE     ---       #174
      0100012BH   LINE      CODE     ---       #175
      0100012BH   LINE      CODE     ---       #176
      0100012DH   LINE      CODE     ---       #179
      01000130H   LINE      CODE     ---       #181
      01000136H   LINE      CODE     ---       #182
      01000136H   LINE      CODE     ---       #183
      01000139H   LINE      CODE     ---       #184
      0100013BH   LINE      CODE     ---       #186
      0100013EH   LINE      CODE     ---       #187
      01000149H   LINE      CODE     ---       #190
      0100014FH   LINE      CODE     ---       #191
      0100014FH   LINE      CODE     ---       #192
      0100015DH   LINE      CODE     ---       #193
      0100016FH   LINE      CODE     ---       #194
      0100016FH   LINE      CODE     ---       #195
      01000173H   LINE      CODE     ---       #196
      01000175H   LINE      CODE     ---       #197
      0100017BH   LINE      CODE     ---       #198
      0100017DH   LINE      CODE     ---       #199
      0100017FH   LINE      CODE     ---       #200
      01000181H   LINE      CODE     ---       #201
      01000188H   LINE      CODE     ---       #202
      0100018EH   LINE      CODE     ---       #203
      01000190H   LINE      CODE     ---       #204
      01000192H   LINE      CODE     ---       #205
      0100019AH   LINE      CODE     ---       #206
      0100019CH   LINE      CODE     ---       #207
      0100019EH   LINE      CODE     ---       #208
      010001A0H   LINE      CODE     ---       #209
      010001A0H   LINE      CODE     ---       #210
      010001A3H   LINE      CODE     ---       #212
      010001A3H   LINE      CODE     ---       #213
      010001AAH   LINE      CODE     ---       #215
      010001C6H   LINE      CODE     ---       #216
      010001C6H   LINE      CODE     ---       #217
      010001CCH   LINE      CODE     ---       #218
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 96


      010001CCH   LINE      CODE     ---       #219
      010001DAH   LINE      CODE     ---       #220
      010001EBH   LINE      CODE     ---       #221
      010001EBH   LINE      CODE     ---       #222
      010001EFH   LINE      CODE     ---       #223
      010001F1H   LINE      CODE     ---       #224
      010001F3H   LINE      CODE     ---       #225
      010001F8H   LINE      CODE     ---       #226
      010001F8H   LINE      CODE     ---       #227
      010001FAH   LINE      CODE     ---       #228
      01000205H   LINE      CODE     ---       #229
      01000205H   LINE      CODE     ---       #230
      01000213H   LINE      CODE     ---       #231
      01000224H   LINE      CODE     ---       #232
      01000224H   LINE      CODE     ---       #233
      01000228H   LINE      CODE     ---       #234
      0100022AH   LINE      CODE     ---       #235
      0100022EH   LINE      CODE     ---       #236
      01000230H   LINE      CODE     ---       #237
      01000236H   LINE      CODE     ---       #238
      01000236H   LINE      CODE     ---       #239
      01000238H   LINE      CODE     ---       #240
      01000242H   LINE      CODE     ---       #241
      01000242H   LINE      CODE     ---       #242
      01000248H   LINE      CODE     ---       #243
      0100024AH   LINE      CODE     ---       #244
      0100024EH   LINE      CODE     ---       #245
      01000254H   LINE      CODE     ---       #246
      01000256H   LINE      CODE     ---       #247
      01000256H   LINE      CODE     ---       #248
      01000258H   LINE      CODE     ---       #250
      01000262H   LINE      CODE     ---       #251
      01000262H   LINE      CODE     ---       #252
      01000267H   LINE      CODE     ---       #253
      01000267H   LINE      CODE     ---       #254
      01000267H   LINE      CODE     ---       #256
      01000276H   LINE      CODE     ---       #257
      0100028EH   LINE      CODE     ---       #258
      01000291H   LINE      CODE     ---       #260
      01000293H   LINE      CODE     ---       #261
      01000299H   LINE      CODE     ---       #262
      010002A2H   LINE      CODE     ---       #263
      010002A5H   LINE      CODE     ---       #266
      010002AEH   LINE      CODE     ---       #267
      010002AEH   LINE      CODE     ---       #269
      010002B4H   LINE      CODE     ---       #270
      010002B4H   LINE      CODE     ---       #272
      010002B4H   LINE      CODE     ---       #274
      010002B4H   LINE      CODE     ---       #276
      010002B4H   LINE      CODE     ---       #277
      010002B4H   LINE      CODE     ---       #278
      010002B7H   LINE      CODE     ---       #279
      010002B9H   LINE      CODE     ---       #281
      010002BCH   LINE      CODE     ---       #282
      010002C7H   LINE      CODE     ---       #284
      010002D8H   LINE      CODE     ---       #285
      010002F0H   LINE      CODE     ---       #287
      010002F3H   LINE      CODE     ---       #289
      010002F6H   LINE      CODE     ---       #292
      01000303H   LINE      CODE     ---       #293
      01000303H   LINE      CODE     ---       #295
      01000306H   LINE      CODE     ---       #296
      01000306H   LINE      CODE     ---       #298
      01000309H   LINE      CODE     ---       #299
      01000309H   LINE      CODE     ---       #300
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 97


      01000311H   LINE      CODE     ---       #301
      01000313H   LINE      CODE     ---       #302
      01000313H   LINE      CODE     ---       #303
      01000313H   LINE      CODE     ---       #304
      01000315H   LINE      CODE     ---       #305
      01000317H   LINE      CODE     ---       #306
      0100031DH   LINE      CODE     ---       #307
      0100031DH   LINE      CODE     ---       #309
      01000320H   LINE      CODE     ---       #310
      01000320H   LINE      CODE     ---       #312
      01000323H   LINE      CODE     ---       #313
      01000323H   LINE      CODE     ---       #314
      0100032BH   LINE      CODE     ---       #315
      0100032DH   LINE      CODE     ---       #316
      0100032DH   LINE      CODE     ---       #317
      0100032DH   LINE      CODE     ---       #318
      0100032FH   LINE      CODE     ---       #319
      01000331H   LINE      CODE     ---       #321
      01000331H   LINE      CODE     ---       #323
      01000337H   LINE      CODE     ---       #324
      01000337H   LINE      CODE     ---       #326
      0100033FH   LINE      CODE     ---       #327
      01000341H   LINE      CODE     ---       #331
      01000341H   LINE      CODE     ---       #332
      01000343H   LINE      CODE     ---       #333
      01000343H   LINE      CODE     ---       #336
      0100034EH   LINE      CODE     ---       #337
      0100034EH   LINE      CODE     ---       #339
      01000351H   LINE      CODE     ---       #340
      01000351H   LINE      CODE     ---       #342
      01000354H   LINE      CODE     ---       #343
      01000354H   LINE      CODE     ---       #344
      01000356H   LINE      CODE     ---       #345
      01000356H   LINE      CODE     ---       #347
      01000359H   LINE      CODE     ---       #348
      01000359H   LINE      CODE     ---       #349
      0100035BH   LINE      CODE     ---       #350
      0100035BH   LINE      CODE     ---       #353
      01000361H   LINE      CODE     ---       #354
      01000361H   LINE      CODE     ---       #355
      01000363H   LINE      CODE     ---       #356
      01000365H   LINE      CODE     ---       #357
      01000367H   LINE      CODE     ---       #358
      0100036FH   LINE      CODE     ---       #359
      01000371H   LINE      CODE     ---       #361
      01000371H   LINE      CODE     ---       #362
      01000373H   LINE      CODE     ---       #364
      01000373H   LINE      CODE     ---       #368
      0100037FH   LINE      CODE     ---       #369
      0100037FH   LINE      CODE     ---       #370
      0100038DH   LINE      CODE     ---       #371
      0100039CH   LINE      CODE     ---       #372
      0100039CH   LINE      CODE     ---       #373
      0100039EH   LINE      CODE     ---       #374
      010003A3H   LINE      CODE     ---       #375
      010003A5H   LINE      CODE     ---       #376
      010003A7H   LINE      CODE     ---       #377
      010003A9H   LINE      CODE     ---       #378
      010003B1H   LINE      CODE     ---       #379
      010003B3H   LINE      CODE     ---       #380
      010003B9H   LINE      CODE     ---       #381
      010003B9H   LINE      CODE     ---       #382
      010003BBH   LINE      CODE     ---       #384
      010003BBH   LINE      CODE     ---       #385
      010003C2H   LINE      CODE     ---       #386
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 98


      010003C4H   LINE      CODE     ---       #387
      010003C4H   LINE      CODE     ---       #388
      010003C4H   LINE      CODE     ---       #391
      010003CAH   LINE      CODE     ---       #392
      010003CAH   LINE      CODE     ---       #393
      010003E7H   LINE      CODE     ---       #394
      010003E7H   LINE      CODE     ---       #395
      010003EAH   LINE      CODE     ---       #396
      010003EAH   LINE      CODE     ---       #398
      010003EAH   LINE      CODE     ---       #399
      010003EAH   LINE      CODE     ---       #400
      010003EAH   LINE      CODE     ---       #401
      010003EAH   LINE      CODE     ---       #402
      010003EAH   LINE      CODE     ---       #403
      010003EAH   LINE      CODE     ---       #404
      010003ECH   LINE      CODE     ---       #405
      010003F7H   LINE      CODE     ---       #406
      010003F7H   LINE      CODE     ---       #408
      010003F7H   LINE      CODE     ---       #409
      010003F7H   LINE      CODE     ---       #410
      010003F7H   LINE      CODE     ---       #411
      010003F7H   LINE      CODE     ---       #413
      010003F9H   LINE      CODE     ---       #414
      010003FFH   LINE      CODE     ---       #415
      010003FFH   LINE      CODE     ---       #417
      01000404H   LINE      CODE     ---       #418
      01000404H   LINE      CODE     ---       #419
      01000404H   LINE      CODE     ---       #420
      01000404H   LINE      CODE     ---       #421
      01000404H   LINE      CODE     ---       #422
      01000404H   LINE      CODE     ---       #423
      01000404H   LINE      CODE     ---       #424
      01000406H   LINE      CODE     ---       #425
      01000423H   LINE      CODE     ---       #426
      01000423H   LINE      CODE     ---       #427
      01000426H   LINE      CODE     ---       #428
      01000426H   LINE      CODE     ---       #430
      0100042BH   LINE      CODE     ---       #431
      0100042BH   LINE      CODE     ---       #432
      0100042BH   LINE      CODE     ---       #433
      0100042BH   LINE      CODE     ---       #434
      0100042BH   LINE      CODE     ---       #435
      0100042BH   LINE      CODE     ---       #436
      0100042DH   LINE      CODE     ---       #437
      01000438H   LINE      CODE     ---       #438
      01000438H   LINE      CODE     ---       #440
      0100043AH   LINE      CODE     ---       #441
      0100043CH   LINE      CODE     ---       #442
      01000444H   LINE      CODE     ---       #443
      01000446H   LINE      CODE     ---       #444
      01000448H   LINE      CODE     ---       #445
      0100044EH   LINE      CODE     ---       #446
      0100044EH   LINE      CODE     ---       #448
      01000454H   LINE      CODE     ---       #449
      0100045DH   LINE      CODE     ---       #450
      0100045FH   LINE      CODE     ---       #451
      01000461H   LINE      CODE     ---       #452
      01000463H   LINE      CODE     ---       #453
      0100046AH   LINE      CODE     ---       #454
      0100046AH   LINE      CODE     ---       #455
      0100046AH   LINE      CODE     ---       #456
      0100046AH   LINE      CODE     ---       #459
      01000470H   LINE      CODE     ---       #460
      01000470H   LINE      CODE     ---       #461
      01000472H   LINE      CODE     ---       #462
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 99


      01000480H   LINE      CODE     ---       #464
      01000491H   LINE      CODE     ---       #465
      01000491H   LINE      CODE     ---       #466
      01000499H   LINE      CODE     ---       #467
      0100049BH   LINE      CODE     ---       #469
      0100049BH   LINE      CODE     ---       #470
      010004A2H   LINE      CODE     ---       #471
      010004A2H   LINE      CODE     ---       #472
      010004A2H   LINE      CODE     ---       #475
      010004A5H   LINE      CODE     ---       #476
      010004A5H   LINE      CODE     ---       #478
      010004B3H   LINE      CODE     ---       #479
      010004C5H   LINE      CODE     ---       #480
      010004C5H   LINE      CODE     ---       #481
      010004C9H   LINE      CODE     ---       #482
      010004CBH   LINE      CODE     ---       #483
      010004CBH   LINE      CODE     ---       #484
      010004CEH   LINE      CODE     ---       #486
      010004CEH   LINE      CODE     ---       #488
      010004D0H   LINE      CODE     ---       #489
      010004D7H   LINE      CODE     ---       #490
      010004D7H   LINE      CODE     ---       #491
      010004DAH   LINE      CODE     ---       #494
      010004DAH   LINE      CODE     ---       #495
      010004DDH   LINE      CODE     ---       #497
      010004FDH   LINE      CODE     ---       #498
      010004FDH   LINE      CODE     ---       #499
      010004FFH   LINE      CODE     ---       #500
      01000501H   LINE      CODE     ---       #501
      0100051EH   LINE      CODE     ---       #502
      0100051EH   LINE      CODE     ---       #503
      01000526H   LINE      CODE     ---       #504
      01000528H   LINE      CODE     ---       #505
      01000538H   LINE      CODE     ---       #506
      01000538H   LINE      CODE     ---       #507
      01000546H   LINE      CODE     ---       #508
      01000557H   LINE      CODE     ---       #509
      01000557H   LINE      CODE     ---       #510
      0100055BH   LINE      CODE     ---       #511
      0100055EH   LINE      CODE     ---       #512
      01000560H   LINE      CODE     ---       #513
      01000562H   LINE      CODE     ---       #514
      01000565H   LINE      CODE     ---       #515
      01000565H   LINE      CODE     ---       #516
      01000565H   LINE      CODE     ---       #517
      01000565H   LINE      CODE     ---       #519
      01000574H   LINE      CODE     ---       #521
      0100057AH   LINE      CODE     ---       #522
      0100057AH   LINE      CODE     ---       #523
      01000587H   LINE      CODE     ---       #524
      01000587H   LINE      CODE     ---       #525
      01000589H   LINE      CODE     ---       #526
      0100058BH   LINE      CODE     ---       #527
      01000593H   LINE      CODE     ---       #528
      01000595H   LINE      CODE     ---       #530
      01000595H   LINE      CODE     ---       #531
      0100059BH   LINE      CODE     ---       #532
      0100059BH   LINE      CODE     ---       #534
      010005A3H   LINE      CODE     ---       #535
      010005A3H   LINE      CODE     ---       #536
      010005A4H   LINE      CODE     ---       #537
      010005A8H   LINE      CODE     ---       #538
      010005ABH   LINE      CODE     ---       #539
      010005B4H   LINE      CODE     ---       #540
      010005B4H   LINE      CODE     ---       #541
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 100


      010005B9H   LINE      CODE     ---       #543
      01000602H   LINE      CODE     ---       #544
      01000602H   LINE      CODE     ---       #545
      01000602H   LINE      CODE     ---       #546
      01000602H   LINE      CODE     ---       #547
      01000610H   LINE      CODE     ---       #548
      0100061DH   LINE      CODE     ---       #549
      0100061DH   LINE      CODE     ---       #550
      01000624H   LINE      CODE     ---       #551
      01000626H   LINE      CODE     ---       #553
      01000626H   LINE      CODE     ---       #554
      01000628H   LINE      CODE     ---       #555
      0100062FH   LINE      CODE     ---       #556
      0100062FH   LINE      CODE     ---       #557
      0100062FH   LINE      CODE     ---       #558
      0100062FH   LINE      CODE     ---       #559
      0100062FH   LINE      CODE     ---       #560
      01000631H   LINE      CODE     ---       #561
      01000631H   LINE      CODE     ---       #562
      0100063AH   LINE      CODE     ---       #563
      01000645H   LINE      CODE     ---       #564
      01000645H   LINE      CODE     ---       #565
      01000647H   LINE      CODE     ---       #566
      01000647H   LINE      CODE     ---       #567
      01000647H   LINE      CODE     ---       #568
      01000647H   LINE      CODE     ---       #569
      01000647H   LINE      CODE     ---       #570
      01000647H   LINE      CODE     ---       #571
      0100064AH   LINE      CODE     ---       #572
      0100064AH   LINE      CODE     ---       #573
      0100064AH   LINE      CODE     ---       #574
      01000658H   LINE      CODE     ---       #575
      01000665H   LINE      CODE     ---       #576
      01000665H   LINE      CODE     ---       #577
      0100066BH   LINE      CODE     ---       #578
      0100066DH   LINE      CODE     ---       #580
      0100066DH   LINE      CODE     ---       #581
      0100066FH   LINE      CODE     ---       #582
      01000676H   LINE      CODE     ---       #583
      01000676H   LINE      CODE     ---       #584
      01000681H   LINE      CODE     ---       #585
      01000686H   LINE      CODE     ---       #586
      01000686H   LINE      CODE     ---       #587
      01000689H   LINE      CODE     ---       #588
      01000689H   LINE      CODE     ---       #589
      01000692H   LINE      CODE     ---       #590
      0100069AH   LINE      CODE     ---       #591
      0100069AH   LINE      CODE     ---       #592
      0100069CH   LINE      CODE     ---       #593
      0100069CH   LINE      CODE     ---       #594
      0100069CH   LINE      CODE     ---       #595
      0100069CH   LINE      CODE     ---       #596
      0100069CH   LINE      CODE     ---       #597
      0100069CH   LINE      CODE     ---       #598
      0100069EH   LINE      CODE     ---       #599
      0100069EH   LINE      CODE     ---       #600
      010006A7H   LINE      CODE     ---       #601
      010006AFH   LINE      CODE     ---       #602
      010006AFH   LINE      CODE     ---       #603
      010006B1H   LINE      CODE     ---       #604
      010006B1H   LINE      CODE     ---       #605
      010006B6H   LINE      CODE     ---       #606
      010006B6H   LINE      CODE     ---       #607
      010006B6H   LINE      CODE     ---       #608
      010006B6H   LINE      CODE     ---       #609
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 101


      010006B8H   LINE      CODE     ---       #610
      010006B8H   LINE      CODE     ---       #611
      010006C1H   LINE      CODE     ---       #612
      010006C9H   LINE      CODE     ---       #613
      010006C9H   LINE      CODE     ---       #614
      010006CBH   LINE      CODE     ---       #615
      010006CBH   LINE      CODE     ---       #616
      010006D1H   LINE      CODE     ---       #617
      010006D3H   LINE      CODE     ---       #618
      010006D3H   LINE      CODE     ---       #619
      010006D3H   LINE      CODE     ---       #620
      010006D6H   LINE      CODE     ---       #621
      010006D6H   LINE      CODE     ---       #622
      010006DCH   LINE      CODE     ---       #623
      010006DEH   LINE      CODE     ---       #624
      010006E6H   LINE      CODE     ---       #625
      010006E8H   LINE      CODE     ---       #626
      010006EBH   LINE      CODE     ---       #627
      010006EBH   LINE      CODE     ---       #628
      010006EBH   LINE      CODE     ---       #629
      010006F4H   LINE      CODE     ---       #630
      010006FCH   LINE      CODE     ---       #631
      010006FCH   LINE      CODE     ---       #632
      010006FEH   LINE      CODE     ---       #633
      010006FEH   LINE      CODE     ---       #634
      01000704H   LINE      CODE     ---       #635
      01000706H   LINE      CODE     ---       #636
      01000706H   LINE      CODE     ---       #637
      01000706H   LINE      CODE     ---       #638
      01000706H   LINE      CODE     ---       #639
      01000708H   LINE      CODE     ---       #640
      01000708H   LINE      CODE     ---       #641
      01000708H   LINE      CODE     ---       #642
      01000711H   LINE      CODE     ---       #643
      01000719H   LINE      CODE     ---       #644
      01000719H   LINE      CODE     ---       #645
      0100071BH   LINE      CODE     ---       #646
      0100071BH   LINE      CODE     ---       #647
      01000721H   LINE      CODE     ---       #648
      01000723H   LINE      CODE     ---       #649
      01000723H   LINE      CODE     ---       #650
      01000723H   LINE      CODE     ---       #651
      01000723H   LINE      CODE     ---       #652
      01000725H   LINE      CODE     ---       #653
      01000725H   LINE      CODE     ---       #654
      01000725H   LINE      CODE     ---       #655
      0100072EH   LINE      CODE     ---       #656
      01000736H   LINE      CODE     ---       #657
      01000736H   LINE      CODE     ---       #658
      01000738H   LINE      CODE     ---       #659
      01000738H   LINE      CODE     ---       #660
      0100073EH   LINE      CODE     ---       #661
      01000740H   LINE      CODE     ---       #662
      01000740H   LINE      CODE     ---       #663
      01000740H   LINE      CODE     ---       #664
      01000740H   LINE      CODE     ---       #665
      01000742H   LINE      CODE     ---       #666
      01000742H   LINE      CODE     ---       #667
      01000742H   LINE      CODE     ---       #668
      0100074BH   LINE      CODE     ---       #669
      01000753H   LINE      CODE     ---       #670
      01000753H   LINE      CODE     ---       #671
      01000755H   LINE      CODE     ---       #672
      01000755H   LINE      CODE     ---       #673
      0100075BH   LINE      CODE     ---       #674
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 102


      0100075DH   LINE      CODE     ---       #675
      0100075FH   LINE      CODE     ---       #676
      01000765H   LINE      CODE     ---       #677
      01000765H   LINE      CODE     ---       #678
      01000767H   LINE      CODE     ---       #679
      01000767H   LINE      CODE     ---       #680
      01000767H   LINE      CODE     ---       #681
      0100076DH   LINE      CODE     ---       #682
      0100076FH   LINE      CODE     ---       #683
      0100076FH   LINE      CODE     ---       #684
      0100076FH   LINE      CODE     ---       #685
      0100076FH   LINE      CODE     ---       #686
      0100076FH   LINE      CODE     ---       #687
      0100076FH   LINE      CODE     ---       #688
      0100076FH   LINE      CODE     ---       #689
      0100076FH   LINE      CODE     ---       #692
      01000780H   LINE      CODE     ---       #693
      01000780H   LINE      CODE     ---       #694
      01000782H   LINE      CODE     ---       #695
      01000784H   LINE      CODE     ---       #696
      01000792H   LINE      CODE     ---       #698
      0100079EH   LINE      CODE     ---       #699
      0100079EH   LINE      CODE     ---       #700
      010007A0H   LINE      CODE     ---       #701
      010007A2H   LINE      CODE     ---       #702
      010007A4H   LINE      CODE     ---       #703
      010007A4H   LINE      CODE     ---       #704
      010007AAH   LINE      CODE     ---       #705
      010007AAH   LINE      CODE     ---       #706
      010007ADH   LINE      CODE     ---       #707
      010007B4H   LINE      CODE     ---       #708
      010007B6H   LINE      CODE     ---       #709
      010007B6H   LINE      CODE     ---       #710
      010007B9H   LINE      CODE     ---       #711
      010007B9H   LINE      CODE     ---       #712
      010007C2H   LINE      CODE     ---       #713
      010007C2H   LINE      CODE     ---       #714
      010007C5H   LINE      CODE     ---       #715
      010007C5H   LINE      CODE     ---       #717
      010007C8H   LINE      CODE     ---       #718
      010007C8H   LINE      CODE     ---       #719
      010007D1H   LINE      CODE     ---       #720
      010007D3H   LINE      CODE     ---       #722
      010007D3H   LINE      CODE     ---       #724
      010007E0H   LINE      CODE     ---       #725
      010007E0H   LINE      CODE     ---       #726
      010007E8H   LINE      CODE     ---       #727
      010007EAH   LINE      CODE     ---       #728
      010007F0H   LINE      CODE     ---       #729
      010007F0H   LINE      CODE     ---       #730
      010007F8H   LINE      CODE     ---       #731
      010007F8H   LINE      CODE     ---       #732
      010007F8H   LINE      CODE     ---       #733
      010007F8H   LINE      CODE     ---       #734
      010007FBH   LINE      CODE     ---       #735
      010007FBH   LINE      CODE     ---       #736
      01000801H   LINE      CODE     ---       #737
      01000801H   LINE      CODE     ---       #738
      01000812H   LINE      CODE     ---       #739
      01000812H   LINE      CODE     ---       #740
      01000812H   LINE      CODE     ---       #741
      01000814H   LINE      CODE     ---       #743
      01000814H   LINE      CODE     ---       #744
      0100081CH   LINE      CODE     ---       #745
      01000823H   LINE      CODE     ---       #746
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 103


      01000829H   LINE      CODE     ---       #747
      0100082BH   LINE      CODE     ---       #748
      0100082DH   LINE      CODE     ---       #749
      0100082FH   LINE      CODE     ---       #750
      01000831H   LINE      CODE     ---       #751
      01000833H   LINE      CODE     ---       #752
      01000833H   LINE      CODE     ---       #753
      01000836H   LINE      CODE     ---       #755
      01000836H   LINE      CODE     ---       #756
      0100083EH   LINE      CODE     ---       #757
      01000845H   LINE      CODE     ---       #758
      0100084BH   LINE      CODE     ---       #759
      0100084DH   LINE      CODE     ---       #760
      0100084FH   LINE      CODE     ---       #761
      01000851H   LINE      CODE     ---       #762
      01000853H   LINE      CODE     ---       #763
      01000853H   LINE      CODE     ---       #764
      01000856H   LINE      CODE     ---       #765
      01000867H   LINE      CODE     ---       #766
      01000867H   LINE      CODE     ---       #767
      0100086FH   LINE      CODE     ---       #768
      01000872H   LINE      CODE     ---       #770
      01000872H   LINE      CODE     ---       #771
      01000878H   LINE      CODE     ---       #772
      01000878H   LINE      CODE     ---       #773
      01000878H   LINE      CODE     ---       #774
      01000878H   LINE      CODE     ---       #775
      01000878H   LINE      CODE     ---       #776
      01000878H   LINE      CODE     ---       #777
      01000878H   LINE      CODE     ---       #778
      01000878H   LINE      CODE     ---       #779
      0100087AH   LINE      CODE     ---       #781
      0100087AH   LINE      CODE     ---       #782
      01000880H   LINE      CODE     ---       #783
      01000880H   LINE      CODE     ---       #784
      01000887H   LINE      CODE     ---       #785
      0100088AH   LINE      CODE     ---       #787
      0100088AH   LINE      CODE     ---       #788
      01000892H   LINE      CODE     ---       #789
      01000899H   LINE      CODE     ---       #790
      0100089FH   LINE      CODE     ---       #791
      010008A1H   LINE      CODE     ---       #792
      010008A3H   LINE      CODE     ---       #793
      010008A5H   LINE      CODE     ---       #794
      010008A5H   LINE      CODE     ---       #795
      010008A5H   LINE      CODE     ---       #796
      010008A5H   LINE      CODE     ---       #797
      010008A8H   LINE      CODE     ---       #798
      010008B3H   LINE      CODE     ---       #799
      010008B3H   LINE      CODE     ---       #800
      010008B5H   LINE      CODE     ---       #801
      010008B7H   LINE      CODE     ---       #802
      010008B9H   LINE      CODE     ---       #803
      010008BBH   LINE      CODE     ---       #804
      010008BDH   LINE      CODE     ---       #805
      010008C0H   LINE      CODE     ---       #806
      010008C9H   LINE      CODE     ---       #807
      010008C9H   LINE      CODE     ---       #808
      010008C9H   LINE      CODE     ---       #809
      010008D0H   LINE      CODE     ---       #810
      010008D0H   LINE      CODE     ---       #811
      010008D2H   LINE      CODE     ---       #812
      010008D4H   LINE      CODE     ---       #813
      010008D6H   LINE      CODE     ---       #814
      010008D8H   LINE      CODE     ---       #815
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 104


      010008DAH   LINE      CODE     ---       #816
      010008DCH   LINE      CODE     ---       #817
      010008DEH   LINE      CODE     ---       #820
      010008E0H   LINE      CODE     ---       #821
      010008E2H   LINE      CODE     ---       #822
      010008E4H   LINE      CODE     ---       #823
      010008EAH   LINE      CODE     ---       #824
      010008F0H   LINE      CODE     ---       #825
      010008F2H   LINE      CODE     ---       #826
      010008F4H   LINE      CODE     ---       #827
      010008F6H   LINE      CODE     ---       #828
      010008F8H   LINE      CODE     ---       #830
      010008FBH   LINE      CODE     ---       #832
      010008FEH   LINE      CODE     ---       #834
      01000901H   LINE      CODE     ---       #836
      01000904H   LINE      CODE     ---       #837
      01000907H   LINE      CODE     ---       #838
      0100090AH   LINE      CODE     ---       #839
      0100090DH   LINE      CODE     ---       #840
      01000910H   LINE      CODE     ---       #841
      01000913H   LINE      CODE     ---       #842
      01000916H   LINE      CODE     ---       #844
      0100091CH   LINE      CODE     ---       #845
      0100091CH   LINE      CODE     ---       #846
      ---         BLOCKEND  ---      ---       LVL=0

      010025C9H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      010025C9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      010025C9H   LINE      CODE     ---       #850
      010025C9H   LINE      CODE     ---       #851
      010025C9H   LINE      CODE     ---       #853
      010025D3H   LINE      CODE     ---       #854
      010025E8H   LINE      CODE     ---       #855
      ---         BLOCKEND  ---      ---       LVL=0

      01001E49H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001E49H   BLOCK     CODE     NEAR LAB  LVL=1
      02000050H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001E49H   LINE      CODE     ---       #857
      01001E49H   LINE      CODE     ---       #858
      01001E49H   LINE      CODE     ---       #861
      01001E4EH   LINE      CODE     ---       #862
      01001E4EH   LINE      CODE     ---       #863
      01001E54H   LINE      CODE     ---       #864
      01001E5EH   LINE      CODE     ---       #865
      01001E5EH   LINE      CODE     ---       #866
      01001E60H   LINE      CODE     ---       #867
      01001E60H   LINE      CODE     ---       #868
      01001E62H   LINE      CODE     ---       #870
      01001E62H   LINE      CODE     ---       #871
      01001E6FH   LINE      CODE     ---       #872
      01001E6FH   LINE      CODE     ---       #873
      01001E72H   LINE      CODE     ---       #874
      01001E72H   LINE      CODE     ---       #875
      01001E78H   LINE      CODE     ---       #876
      01001E78H   LINE      CODE     ---       #878
      01001EA0H   LINE      CODE     ---       #879
      01001EA0H   LINE      CODE     ---       #880
      01001EA4H   LINE      CODE     ---       #881
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 105


      01001EA8H   LINE      CODE     ---       #882
      01001EAEH   LINE      CODE     ---       #883
      01001EB4H   LINE      CODE     ---       #884
      01001EBDH   LINE      CODE     ---       #885
      01001EC6H   LINE      CODE     ---       #886
      01001ECAH   LINE      CODE     ---       #887
      01001ED3H   LINE      CODE     ---       #888
      01001ED3H   LINE      CODE     ---       #889
      01001EDBH   LINE      CODE     ---       #891
      01001EDBH   LINE      CODE     ---       #892
      01001EDBH   LINE      CODE     ---       #893
      ---         BLOCKEND  ---      ---       LVL=0

      01002318H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01002318H   LINE      CODE     ---       #895
      01002318H   LINE      CODE     ---       #896
      01002318H   LINE      CODE     ---       #897
      01002328H   LINE      CODE     ---       #898
      01002328H   LINE      CODE     ---       #899
      01002328H   LINE      CODE     ---       #900
      01002331H   LINE      CODE     ---       #901
      01002337H   LINE      CODE     ---       #902
      01002338H   LINE      CODE     ---       #903
      01002338H   LINE      CODE     ---       #904
      0100233BH   LINE      CODE     ---       #905
      0100233BH   LINE      CODE     ---       #906
      01002340H   LINE      CODE     ---       #907
      01002341H   LINE      CODE     ---       #909
      01002341H   LINE      CODE     ---       #910
      01002347H   LINE      CODE     ---       #911
      01002347H   LINE      CODE     ---       #912
      01002348H   LINE      CODE     ---       #913
      01002348H   LINE      CODE     ---       #914
      01002351H   LINE      CODE     ---       #915
      01002357H   LINE      CODE     ---       #916
      01002358H   LINE      CODE     ---       #917
      01002358H   LINE      CODE     ---       #918
      0100235EH   LINE      CODE     ---       #919
      0100235EH   LINE      CODE     ---       #920
      0100235EH   LINE      CODE     ---       #921
      0100235EH   LINE      CODE     ---       #922
      0100235EH   LINE      CODE     ---       #923
      ---         BLOCKEND  ---      ---       LVL=0

      010020AFH   BLOCK     CODE     ---       LVL=0
      010020AFH   BLOCK     CODE     NEAR LAB  LVL=1
      02000051H   SYMBOL    XDATA    BYTE      motor_delay_cnt
      ---         BLOCKEND  ---      ---       LVL=1
      010020AFH   LINE      CODE     ---       #926
      010020AFH   LINE      CODE     ---       #927
      010020AFH   LINE      CODE     ---       #931
      010020B2H   LINE      CODE     ---       #932
      010020B2H   LINE      CODE     ---       #933
      010020B8H   LINE      CODE     ---       #934
      010020C2H   LINE      CODE     ---       #935
      010020C2H   LINE      CODE     ---       #937
      010020C2H   LINE      CODE     ---       #938
      010020C4H   LINE      CODE     ---       #939
      010020C4H   LINE      CODE     ---       #941
      010020D1H   LINE      CODE     ---       #943
      010020D9H   LINE      CODE     ---       #944
      010020D9H   LINE      CODE     ---       #945
      010020DFH   LINE      CODE     ---       #946
      010020EAH   LINE      CODE     ---       #947
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 106


      010020ECH   LINE      CODE     ---       #949
      010020ECH   LINE      CODE     ---       #950
      010020EEH   LINE      CODE     ---       #951
      010020F3H   LINE      CODE     ---       #952
      010020F3H   LINE      CODE     ---       #954
      01002102H   LINE      CODE     ---       #955
      01002102H   LINE      CODE     ---       #956
      01002108H   LINE      CODE     ---       #957
      01002113H   LINE      CODE     ---       #958
      01002114H   LINE      CODE     ---       #960
      01002114H   LINE      CODE     ---       #961
      01002119H   LINE      CODE     ---       #962
      0100211BH   LINE      CODE     ---       #963
      0100211BH   LINE      CODE     ---       #964
      ---         BLOCKEND  ---      ---       LVL=0

      01002472H   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    WORD      dly1
      01002472H   LINE      CODE     ---       #966
      0100247AH   LINE      CODE     ---       #967
      0100247AH   LINE      CODE     ---       #968
      0100247DH   LINE      CODE     ---       #969
      01002480H   LINE      CODE     ---       #970
      01002489H   LINE      CODE     ---       #971
      01002498H   LINE      CODE     ---       #972
      0100249BH   LINE      CODE     ---       #973
      0100249EH   LINE      CODE     ---       #974
      ---         BLOCKEND  ---      ---       LVL=0

      01002519H   BLOCK     CODE     ---       LVL=0
      01002519H   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002519H   LINE      CODE     ---       #976
      01002519H   LINE      CODE     ---       #977
      01002519H   LINE      CODE     ---       #980
      0100251CH   LINE      CODE     ---       #981
      0100251FH   LINE      CODE     ---       #982
      01002530H   LINE      CODE     ---       #983
      01002533H   LINE      CODE     ---       #984
      01002536H   LINE      CODE     ---       #986
      0100253EH   LINE      CODE     ---       #987
      ---         BLOCKEND  ---      ---       LVL=0

      01001BCAH   BLOCK     CODE     ---       LVL=0
      01001BCAH   LINE      CODE     ---       #989
      01001BCAH   LINE      CODE     ---       #990
      01001BCAH   LINE      CODE     ---       #992
      01001BD5H   LINE      CODE     ---       #993
      01001BD5H   LINE      CODE     ---       #995
      01001BD8H   LINE      CODE     ---       #996
      01001BD8H   LINE      CODE     ---       #998
      01001BDAH   LINE      CODE     ---       #999
      01001BDDH   LINE      CODE     ---       #1000
      01001BDDH   LINE      CODE     ---       #1002
      01001BE4H   LINE      CODE     ---       #1003
      01001BE6H   LINE      CODE     ---       #1004
      01001BE8H   LINE      CODE     ---       #1006
      01001BE8H   LINE      CODE     ---       #1007
      01001BEAH   LINE      CODE     ---       #1008
      01001BEAH   LINE      CODE     ---       #1009
      01001BECH   LINE      CODE     ---       #1011
      01001BECH   LINE      CODE     ---       #1013
      01001BEEH   LINE      CODE     ---       #1014
      01001BF1H   LINE      CODE     ---       #1015
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 107


      01001BF1H   LINE      CODE     ---       #1017
      01001BF4H   LINE      CODE     ---       #1018
      01001BF4H   LINE      CODE     ---       #1019
      01001BF6H   LINE      CODE     ---       #1020
      01001BF8H   LINE      CODE     ---       #1022
      01001BF8H   LINE      CODE     ---       #1023
      01001BFAH   LINE      CODE     ---       #1024
      01001BFAH   LINE      CODE     ---       #1025
      01001BFCH   LINE      CODE     ---       #1027
      01001BFCH   LINE      CODE     ---       #1029
      01001BFEH   LINE      CODE     ---       #1030
      01001BFEH   LINE      CODE     ---       #1031
      01001BFEH   LINE      CODE     ---       #1032
      01001C00H   LINE      CODE     ---       #1034
      01001C00H   LINE      CODE     ---       #1036
      01001C03H   LINE      CODE     ---       #1037
      01001C03H   LINE      CODE     ---       #1038
      01001C05H   LINE      CODE     ---       #1039
      01001C08H   LINE      CODE     ---       #1040
      01001C08H   LINE      CODE     ---       #1042
      01001C0FH   LINE      CODE     ---       #1043
      01001C11H   LINE      CODE     ---       #1044
      01001C13H   LINE      CODE     ---       #1046
      01001C13H   LINE      CODE     ---       #1047
      01001C15H   LINE      CODE     ---       #1048
      01001C15H   LINE      CODE     ---       #1049
      01001C17H   LINE      CODE     ---       #1051
      01001C17H   LINE      CODE     ---       #1052
      01001C1AH   LINE      CODE     ---       #1053
      01001C1AH   LINE      CODE     ---       #1054
      01001C1CH   LINE      CODE     ---       #1055
      01001C26H   LINE      CODE     ---       #1057
      01001C26H   LINE      CODE     ---       #1058
      01001C2DH   LINE      CODE     ---       #1059
      01001C2FH   LINE      CODE     ---       #1060
      01001C2FH   LINE      CODE     ---       #1061
      01001C31H   LINE      CODE     ---       #1063
      01001C31H   LINE      CODE     ---       #1064
      01001C33H   LINE      CODE     ---       #1065
      01001C3DH   LINE      CODE     ---       #1067
      01001C3DH   LINE      CODE     ---       #1068
      01001C44H   LINE      CODE     ---       #1069
      01001C46H   LINE      CODE     ---       #1070
      01001C46H   LINE      CODE     ---       #1071
      01001C46H   LINE      CODE     ---       #1072
      01001C46H   LINE      CODE     ---       #1073
      01001C46H   LINE      CODE     ---       #1076
      01001C49H   LINE      CODE     ---       #1077
      01001C49H   LINE      CODE     ---       #1078
      01001C57H   LINE      CODE     ---       #1079
      01001C66H   LINE      CODE     ---       #1080
      01001C66H   LINE      CODE     ---       #1081
      01001C68H   LINE      CODE     ---       #1082
      01001C6AH   LINE      CODE     ---       #1084
      01001C6AH   LINE      CODE     ---       #1085
      01001C6CH   LINE      CODE     ---       #1086
      01001C6EH   LINE      CODE     ---       #1087
      01001C75H   LINE      CODE     ---       #1088
      01001C75H   LINE      CODE     ---       #1089
      01001C75H   LINE      CODE     ---       #1092
      01001C7BH   LINE      CODE     ---       #1093
      01001C85H   LINE      CODE     ---       #1094
      01001C85H   LINE      CODE     ---       #1095
      01001C87H   LINE      CODE     ---       #1096
      01001C8CH   LINE      CODE     ---       #1097
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 108


      01001C8CH   LINE      CODE     ---       #1100
      01001C9AH   LINE      CODE     ---       #1101
      01001CA9H   LINE      CODE     ---       #1102
      01001CA9H   LINE      CODE     ---       #1103
      01001CADH   LINE      CODE     ---       #1104
      01001CB2H   LINE      CODE     ---       #1105
      01001CB2H   LINE      CODE     ---       #1108
      01001CBAH   LINE      CODE     ---       #1109
      01001CBAH   LINE      CODE     ---       #1110
      01001CC0H   LINE      CODE     ---       #1111
      01001CCAH   LINE      CODE     ---       #1112
      01001CCAH   LINE      CODE     ---       #1113
      01001CCCH   LINE      CODE     ---       #1114
      01001CD1H   LINE      CODE     ---       #1115
      01001CD1H   LINE      CODE     ---       #1116
      01001CD2H   LINE      CODE     ---       #1118
      01001CD2H   LINE      CODE     ---       #1119
      01001CD7H   LINE      CODE     ---       #1120
      01001CD9H   LINE      CODE     ---       #1121
      01001CD9H   LINE      CODE     ---       #1122
      ---         BLOCKEND  ---      ---       LVL=0

      010015B4H   BLOCK     CODE     ---       LVL=0
      010015B4H   LINE      CODE     ---       #1131
      010015B4H   LINE      CODE     ---       #1132
      010015B4H   LINE      CODE     ---       #1134
      010015C0H   LINE      CODE     ---       #1135
      010015C0H   LINE      CODE     ---       #1136
      010015C3H   LINE      CODE     ---       #1137
      010015C3H   LINE      CODE     ---       #1138
      010015C5H   LINE      CODE     ---       #1139
      010015C7H   LINE      CODE     ---       #1140
      010015C9H   LINE      CODE     ---       #1141
      010015CBH   LINE      CODE     ---       #1142
      010015CDH   LINE      CODE     ---       #1143
      010015D3H   LINE      CODE     ---       #1144
      010015D3H   LINE      CODE     ---       #1145
      010015D3H   LINE      CODE     ---       #1148
      010015F4H   LINE      CODE     ---       #1149
      010015F4H   LINE      CODE     ---       #1151
      010015FAH   LINE      CODE     ---       #1152
      010015FAH   LINE      CODE     ---       #1153
      01001603H   LINE      CODE     ---       #1154
      01001609H   LINE      CODE     ---       #1155
      0100160BH   LINE      CODE     ---       #1156
      0100160DH   LINE      CODE     ---       #1157
      0100160FH   LINE      CODE     ---       #1158
      01001611H   LINE      CODE     ---       #1159
      01001615H   LINE      CODE     ---       #1160
      01001615H   LINE      CODE     ---       #1161
      01001615H   LINE      CODE     ---       #1164
      01001618H   LINE      CODE     ---       #1165
      01001618H   LINE      CODE     ---       #1166
      0100161AH   LINE      CODE     ---       #1168
      01001635H   LINE      CODE     ---       #1169
      01001635H   LINE      CODE     ---       #1171
      01001637H   LINE      CODE     ---       #1172
      01001639H   LINE      CODE     ---       #1173
      0100163BH   LINE      CODE     ---       #1176
      01001644H   LINE      CODE     ---       #1177
      0100164AH   LINE      CODE     ---       #1178
      0100164CH   LINE      CODE     ---       #1179
      0100164EH   LINE      CODE     ---       #1180
      01001652H   LINE      CODE     ---       #1181
      01001659H   LINE      CODE     ---       #1182
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 109


      0100165BH   LINE      CODE     ---       #1183
      0100166AH   LINE      CODE     ---       #1184
      0100166AH   LINE      CODE     ---       #1186
      01001670H   LINE      CODE     ---       #1187
      01001672H   LINE      CODE     ---       #1188
      01001674H   LINE      CODE     ---       #1189
      01001676H   LINE      CODE     ---       #1190
      01001678H   LINE      CODE     ---       #1191
      01001678H   LINE      CODE     ---       #1192
      01001678H   LINE      CODE     ---       #1195
      01001699H   LINE      CODE     ---       #1196
      01001699H   LINE      CODE     ---       #1198
      0100169FH   LINE      CODE     ---       #1199
      0100169FH   LINE      CODE     ---       #1200
      010016A8H   LINE      CODE     ---       #1201
      010016AEH   LINE      CODE     ---       #1202
      010016B0H   LINE      CODE     ---       #1203
      010016B2H   LINE      CODE     ---       #1204
      010016B4H   LINE      CODE     ---       #1205
      010016B6H   LINE      CODE     ---       #1206
      010016BBH   LINE      CODE     ---       #1207
      010016BBH   LINE      CODE     ---       #1208
      010016BBH   LINE      CODE     ---       #1211
      010016BEH   LINE      CODE     ---       #1212
      010016BEH   LINE      CODE     ---       #1213
      010016C0H   LINE      CODE     ---       #1215
      010016DBH   LINE      CODE     ---       #1216
      010016DBH   LINE      CODE     ---       #1218
      010016DDH   LINE      CODE     ---       #1219
      010016DFH   LINE      CODE     ---       #1220
      010016E1H   LINE      CODE     ---       #1223
      010016EAH   LINE      CODE     ---       #1224
      010016F0H   LINE      CODE     ---       #1225
      010016F2H   LINE      CODE     ---       #1226
      010016F4H   LINE      CODE     ---       #1227
      010016F9H   LINE      CODE     ---       #1228
      01001700H   LINE      CODE     ---       #1229
      01001701H   LINE      CODE     ---       #1230
      01001710H   LINE      CODE     ---       #1231
      01001710H   LINE      CODE     ---       #1233
      01001716H   LINE      CODE     ---       #1234
      01001718H   LINE      CODE     ---       #1235
      0100171AH   LINE      CODE     ---       #1236
      0100171CH   LINE      CODE     ---       #1237
      0100171EH   LINE      CODE     ---       #1238
      0100171EH   LINE      CODE     ---       #1239
      0100171EH   LINE      CODE     ---       #1240
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      0100091FH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000A28H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000ACFH   PUBLIC    CODE     ---       ?C?FCASTC
      01000ACAH   PUBLIC    CODE     ---       ?C?FCASTI
      01000AC5H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF
      02000020H   PUBLIC    XDATA    ---       ?_PRINTF?BYTE
      02000020H   PUBLIC    XDATA    ---       ?_SPRINTF?BYTE
      0100119CH   PUBLIC    CODE     ---       _PRINTF
      01001196H   PUBLIC    CODE     ---       _SPRINTF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 110



      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000B03H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000B38H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000B42H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000B1AH   PUBLIC    CODE     ---       ?C?FPRESULT
      01000B2EH   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000B3FH   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000B4DH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000B8AH   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000C96H   PUBLIC    CODE     ---       ?C?FPADD
      01000C92H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000DB7H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      01001E04H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000EC7H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000EEDH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000F06H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000F33H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000F45H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01000F67H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01000FBCH   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      0100100EH   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      010010A0H   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      010010AEH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      010010BAH   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      010010EBH   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      01001102H   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  13:47:29  PAGE 111


      0100110BH   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=21.2 xdata=198 const=41 code=9963
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
