C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE ISR
OBJECT MODULE PLACED IN .\Objects\isr.obj
COMPILER INVOKED BY: D:\KeilC51\C51\BIN\C51.EXE ..\code\isr.c LARGE OMF2 OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Libary\Devic
                    -e\CMS8S6990\Include;..\Libary\Device\CMS8S6990;..\Libary\StdDriver\inc;..\Driver;..\code) DEBUG PRINT(.\Listings\isr.lst
                    -) TABS(2) OBJECT(.\Objects\isr.obj)

line level    source

   1          /****************************************************************************/
   2          /** \file isr.c
   3          **  History:
   4          *****************************************************************************/
   5          /****************************************************************************/
   6          /*  include files
   7          *****************************************************************************/
   8          #include "cms8s6990.h"
   9          #include "define.h"
  10          #include "UART_Function.h"
  11          
  12          /****************************************************************************/
  13          /*  Local pre-processor symbols('#define')
  14          ****************************************************************************/
  15          #define stop_RF_cnts    1
  16          
  17          /****************************************************************************/
  18          /*  Global variable definitions(declared in header file with 'extern')
  19          ****************************************************************************/
  20          
  21          /****************************************************************************/
  22          /*  Local type definitions('typedef')
  23          ****************************************************************************/
  24          
  25          /****************************************************************************/
  26          /*  Local variable  definitions('static')
  27          ****************************************************************************/
  28          extern int No_Step,Speed;
  29          extern bit Direction,Get_String_Buff;
  30          extern int Led_G_Toggle,Led_R_Toggle;
  31          extern int R_STRING;
  32          extern int Len_string;
  33          extern int U_string[5];
  34          int Num_Forward_Pulse,Num_Reverse_Pulse;
  35          extern uint8_t K1_cnt,K2_cnt,K3_cnt;
  36          extern bit K1_cnt_EN;
  37          extern bit K2_cnt_EN;
  38          extern bit K3_cnt_EN;
  39          extern volatile bit longhit;
  40          extern volatile bit speedup; 
  41          extern uint16_t longhit_cnt;
  42          extern bit direction_changed;
  43          extern uint8_t last_direction;
  44          extern uint16_t speedup_cnt;
  45          extern uint16_t dly;
  46          extern volatile bit key1_handle;
  47          extern volatile bit key3_handle;
  48          extern volatile uint16_t key1_duration;
  49          extern volatile uint16_t key3_duration;
  50          extern volatile uint16_t timer_1ms_count;
  51          extern volatile uint16_t key1_press_time;
  52          extern volatile uint16_t key3_press_time;
  53          extern volatile bit key1_pressed;
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 2   

  54          extern volatile bit key3_pressed;
  55          extern volatile uint16_t precise_k2_timer;  // ???K2?????
  56          extern bit use_precise_timer;  // ?????????????
  57          
  58          /****************************************************************************/
  59          /*  Local function prototypes('static')
  60          ****************************************************************************/
  61          
  62          /****************************************************************************/
  63          /*  Function implementation - global ('extern') and local('static')
  64          ****************************************************************************/
  65          
  66          /******************************************************************************
  67           ** \brief   INT0 interrupt service function
  68           **     
  69           ** \param [in]  none   
  70           **
  71           ** \return none
  72           ******************************************************************************/
  73          void INT0_IRQHandler(void) interrupt INT0_VECTOR
  74          {
  75   1          ;
  76   1      }
  77          
  78          /******************************************************************************
  79           ** \brief   Timer 0 interrupt service function
  80           **
  81           ** \param [in]  none   
  82           **
  83           ** \return none
  84          ******************************************************************************/
  85          void Timer0_IRQHandler(void) interrupt TMR0_VECTOR 
  86          {
  87   1          TH0 = Time_1ms_Offset >> 8;
  88   1          TL0 = Time_1ms_Offset;
  89   1      
  90   1          Bit_1_ms_Buff = 1;
  91   1          timer_1ms_count++;  
  92   1          Num++;
  93   1          if (Num > Motor_Slowest_Speed + 1) Num = 0;
  94   1          
  95   1          switch (Motor_Speed_Data)
  96   1          {
  97   2              case Motor_Fast_Speed:
  98   2          {
  99   3                  if (Num == Motor_Fast_Speed)
 100   3                  {
 101   4                      Bit_N_ms_Buff = 1;
 102   4                      Num = 0;
 103   4                  }
 104   3          }
 105   2                  break;
 106   2              case Motor_Slow_Speed:
 107   2                  if (Num == Motor_Slow_Speed)
 108   2                  {
 109   3                      Bit_N_ms_Buff = 1;
 110   3                      Num = 0;
 111   3                  }
 112   2                  break;
 113   2              case Motor_Middle_Speed:
 114   2          {
 115   3                  if (Num == Motor_Middle_Speed)
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 3   

 116   3                  {
 117   4                      Bit_N_ms_Buff = 1;
 118   4                      Num = 0;
 119   4                  }
 120   3          }
 121   2                  break;
 122   2              case Motor_Auto_Rotate_Speed:
*** ERROR C175 IN LINE 122 OF ..\code\isr.c: 0x3: duplicate case value
 123   2          {
 124   3                  if (Num == Motor_Auto_Rotate_Speed)
 125   3                  {
 126   4                      Bit_N_ms_Buff = 1;
 127   4                      Num = 0;
 128   4                  }
 129   3          }
 130   2                  break;
 131   2              case Motor_Key_Control_Speed:
 132   2          {
 133   3                  if (Num == Motor_Key_Control_Speed)
 134   3                  {
 135   4                      Bit_N_ms_Buff = 1;
 136   4                      Num = 0;
 137   4                  }
 138   3          }
 139   2                  break;
 140   2              case Motor_Slowest_Speed:
 141   2          {
 142   3                  if (Num == Motor_Slowest_Speed)
 143   3                  {
 144   4                      Bit_N_ms_Buff = 1;
 145   4                      Num = 0;
 146   4                  }
 147   3          }
 148   2                  break;
 149   2              default:
 150   2                  break;
 151   2          }
 152   1          
 153   1          if(K2_cnt_EN)
 154   1          {
 155   2              if(K2 == 0) K2_cnt++;
 156   2          }
 157   1          else
 158   1          {
 159   2              K2_cnt = 0;
 160   2          }
 161   1          
 162   1          if(K2_cnt > 150)
 163   1          {
 164   2              longhit = 1;
 165   2              longhit_cnt++;
 166   2          }
 167   1          else
 168   1          {
 169   2              longhit = 0;
 170   2              longhit_cnt = 0;
 171   2          }
 172   1      
 173   1        if(speedup == 1)
 174   1        {
 175   2          speedup_cnt++;
 176   2          if(speedup_cnt >= dly)
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 4   

 177   2          {
 178   3            speedup = 0;
 179   3            speedup_cnt = 0;
 180   3          }
 181   2        }
 182   1        else
 183   1        {
 184   2          speedup_cnt = 0;
 185   2        }
 186   1      
 187   1      
 188   1      }
 189          
 190          /******************************************************************************
 191           ** \brief   INT0 interrupt service function
 192           **     
 193           ** \param [in]  none   
 194           **
 195           ** \return none
 196           ******************************************************************************/
 197          void INT1_IRQHandler(void) interrupt INT1_VECTOR
 198          {
 199   1          ;
 200   1      }
 201          
 202          /******************************************************************************
 203           ** \brief   Timer 1 interrupt service function
 204           **
 205           ** \param [in]  none   
 206           **
 207           ** \return none
 208          ******************************************************************************/
 209          void Timer1_IRQHandler(void) interrupt TMR1_VECTOR
 210          {
 211   1          // ??????????????2ms?????
 212   1          TH1 = (65536-4000)>>8;
 213   1          TL1 = 65536-4000;
 214   1      
 215   1          // ???K2???????
 216   1          if(use_precise_timer)
 217   1          {
 218   2              precise_k2_timer++;
 219   2          }
 220   1      }
 221          
 222          /******************************************************************************
 223           ** \brief   UART 0 interrupt service function
 224           **
 225           ** \param [in]  none   
 226           **
 227           ** \return none
 228          ******************************************************************************/
 229          void UART0_IRQHandler(void) interrupt UART0_VECTOR 
 230          {
 231   1          if(UART_GetReceiveIntFlag(UART0))
 232   1          {
 233   2              Get_String_Buff = 1;
 234   2              Get_String_Wait_Time = 0;
 235   2              UART_Data_Copy(UART_Get_String, UART_GetBuff(UART0));
 236   2              UART_ClearReceiveIntFlag(UART0);  
 237   2          }
 238   1      }
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 5   

 239          
 240          /******************************************************************************
 241           ** \brief   Timer 2 interrupt service function
 242           **
 243           ** \param [in]  none   
 244           **
 245           ** \return none
 246          ******************************************************************************/
 247          void Timer2_IRQHandler(void) interrupt TMR2_VECTOR 
 248          {
 249   1          ;
 250   1      }
 251          
 252          /******************************************************************************
 253           ** \brief   UART 1 interrupt service function
 254           **
 255           ** \param [in]  none   
 256           **
 257           ** \return none
 258          ******************************************************************************/
 259          void UART1_IRQHandler(void) interrupt UART1_VECTOR 
 260          {
 261   1          ;
 262   1      }
 263          
 264          /******************************************************************************
 265           ** \brief   GPIO 0 interrupt service function
 266           ** 
 267           ** \param [in]  none   
 268           **
 269           ** \return none
 270           ******************************************************************************/
 271          void P0EI_IRQHandler(void) interrupt P0EI_VECTOR 
 272          {
 273   1          ;
 274   1      }
 275          
 276          /******************************************************************************
 277           ** \brief   GPIO 1 interrupt service function
 278           **
 279           ** \param [in]  none
 280           **
 281           ** \return none
 282           ******************************************************************************/
 283          void P1EI_IRQHandler(void) interrupt P1EI_VECTOR 
 284          {
 285   1          // ???P1???????????????6???????0x40??
 286   1          P1EXTIF &= ~0x40;  
 287   1      
 288   1          // ???K1????????????????????
 289   1          if(K1 == 0)  
 290   1          {
 291   2              // ????????????key1_pressed?0??
 292   2              if(key1_pressed == 0)
 293   2              {
 294   3                  // ???????????????
 295   3                  key1_press_time = timer_1ms_count;
 296   3                  // ??????????????
 297   3                  key1_pressed = 1;
 298   3                  // ??????????????
 299   3                  key1_handle = 0; 
 300   3              }
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 6   

 301   2          }
 302   1          else  // ???????
 303   1          {
 304   2              // ????????????
 305   2              if(key1_pressed == 1) 
 306   2              {
 307   3                  // ?????????????????ms??
 308   3                  key1_duration = timer_1ms_count - key1_press_time;
 309   3                  // ????????????10-5000ms??????
 310   3                  if(key1_duration >= 10 && key1_duration <= 5000)  
 311   3                  {
 312   4                      // ??????????????????????????????????
 313   4                      key1_handle = 1; 
 314   4                  }
 315   3                  // ??????????????
 316   3                  key1_pressed = 0;
 317   3              }
 318   2          }
 319   1      }
 320          
 321          /******************************************************************************
 322           ** \brief   GPIO 2 interrupt service function
 323           **
 324           ** \param [in]  none
 325           **
 326           ** \return none
 327           ******************************************************************************/
 328          void P2EI_IRQHandler(void) interrupt P2EI_VECTOR
 329          {
 330   1          P2EXTIF &= ~0x02;  
 331   1      
 332   1          if(K3 == 0)  
 333   1          {
 334   2              
 335   2              if(key3_pressed == 0)
 336   2              {
 337   3                  key3_press_time = timer_1ms_count;
 338   3                  key3_pressed = 1;
 339   3                  key3_handle = 0; 
 340   3              }
 341   2          }
 342   1          else  
 343   1          {
 344   2              if(key3_pressed == 1) 
 345   2              {
 346   3                  key3_duration = timer_1ms_count - key3_press_time;
 347   3                  if(key3_duration >= 10 && key3_duration <= 5000)  
 348   3                  {
 349   4                      key3_handle = 1;  
 350   4                  }
 351   3                  key3_pressed = 0;
 352   3              }
 353   2          }
 354   1      }
 355          
 356          /******************************************************************************
 357           ** \brief   GPIO 3 interrupt service function
 358           **
 359           ** \param [in]  none   
 360           **
 361           ** \return none
 362           ******************************************************************************/
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 7   

 363          void P3EI_IRQHandler(void) interrupt P3EI_VECTOR 
 364          {
 365   1          ;
 366   1      }
 367          
 368          /******************************************************************************
 369           ** \brief   LVD interrupt service function
 370           **
 371           ** \param [in]  none   
 372           **
 373           ** \return none
 374           ******************************************************************************/
 375          void LVD_IRQHandler(void) interrupt LVD_VECTOR 
 376          {
 377   1          ;
 378   1      }
 379          
 380          /******************************************************************************
 381           ** \brief   LSE interrupt service function
 382           **
 383           ** \param [in]  none   
 384           **
 385           ** \return none
 386           ******************************************************************************/
 387          void LSE_IRQHandler(void) interrupt LSE_VECTOR 
 388          {
 389   1          ;
 390   1      }
 391          
 392          /********************************************************************************
 393           ** \brief   ACMP interrupt service function
 394           **     
 395           ** \param [in]  none   
 396           **
 397           ** \return none
 398           ******************************************************************************/
 399          void ACMP_IRQHandler(void) interrupt ACMP_VECTOR 
 400          {
 401   1          ;
 402   1      }
 403          
 404          /******************************************************************************
 405           ** \brief   Timer 3 interrupt service function
 406           **
 407           ** \param [in]  none   
 408           **
 409           ** \return none
 410          ******************************************************************************/
 411          void Timer3_IRQHandler(void) interrupt TMR3_VECTOR 
 412          {
 413   1          ;
 414   1      }
 415          
 416          /******************************************************************************
 417           ** \brief   Timer 4 interrupt service function
 418           **
 419           ** \param [in]  none   
 420           **
 421           ** \return none
 422          ******************************************************************************/
 423          void Timer4_IRQHandler(void) interrupt TMR4_VECTOR 
 424          {
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 8   

 425   1          ;
 426   1      }
 427          
 428          /******************************************************************************
 429           ** \brief   EPWM interrupt service function
 430           **
 431           ** \param [in]  none   
 432           **
 433           ** \return none
 434           ******************************************************************************/
 435          void EPWM_IRQHandler(void) interrupt EPWM_VECTOR
 436          {
 437   1          ;
 438   1      }
 439          
 440          /******************************************************************************
 441           ** \brief   ADC interrupt service function
 442           **
 443           ** \param [in]  none   
 444           **
 445           ** \return none
 446           ******************************************************************************/
 447          void ADC_IRQHandler(void) interrupt ADC_VECTOR 
 448          {
 449   1          ;
 450   1      }
 451          
 452          /******************************************************************************
 453           ** \brief   WDT interrupt service function
 454           **
 455           ** \param [in]  none   
 456           **
 457           ** \return none
 458           ******************************************************************************/
 459          void WDT_IRQHandler(void) interrupt WDT_VECTOR 
 460          {
 461   1          ;
 462   1      }
 463          
 464          /******************************************************************************
 465           ** \brief  I2C interrupt service function
 466           **
 467           ** \param [in]  none   
 468           **
 469           ** \return none
 470           ******************************************************************************/
 471          void I2C_IRQHandler(void) interrupt I2C_VECTOR 
 472          {
 473   1          ;
 474   1      }
 475          
 476          /******************************************************************************
 477           ** \brief  SPI interrupt service function
 478           **
 479           ** \param [in]  none   
 480           **
 481           ** \return none
 482           ******************************************************************************/
 483          void SPI_IRQHandler(void) interrupt SPI_VECTOR 
 484          {
 485   1          ;
 486   1      }
C51 COMPILER V9.60.0.0   ISR                                                               07/22/2025 16:30:27 PAGE 9   


C51 COMPILATION COMPLETE.  0 WARNING(S),  1 ERROR(S)
