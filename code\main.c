/****************************************************************************/
/** \file main.c
**	History:
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "cms8s6990.h"
#include "stdio.h"
#include "define.h"
#include "GPIO_Init.h"
#include "Timer_Init.h"
#include "ADC_Init.h"
#include "ADC_Used.h"
#include "UART_Init.h"
#include "Key.h"
#include "UART_Function.h"
#include "Battery_Function.h"
#include "flash.h"
#include "system.h"

/****************************************************************************/
/*	Local pre-processor symbols('#define')
*****************************************************************************/
#define System_Off		0x00
#define System_Run		0x01
#define System_Standby	0x02
#define System_Check	0x03
#define System_AutoRotate 0x04  // 新增自转模式

#define LED_FLASH_INTERVAL 100
#define CHARGE_FLASH_INTERVAL 250  // 2Hz闪烁(500ms周期，250ms切换一次)
#define AUTO_ROTATE_FLASH_INTERVAL 50  // 10Hz闪烁(100ms周期)

#define dly_addr  512

/****************************************************************************/
/*	Global variable definitions(declared in header file with 'extern')
*****************************************************************************/
uint8_t System_Mode_Data;
bit Charg_State_Buff;
uint8_t System_Mode_Before_Charge = System_Off;  
bit Charge_Was_Connected = 0;  

// LED
bit ledonoff = 0, ledonoff1 = 0;
uint8_t ledonoff_cnt = 0, ledonoff1_cnt = 0;
bit charge_flash = 0;  // 充电闪烁状态
uint16_t charge_flash_cnt = 0;  // 充电闪烁计时器
bit Bit_1_ms_Buff, Bit_N_ms_Buff,Bit_Toggle;
bit Delay_Open = 0 ;


extern uint8_t K1_cnt,K2_cnt,K3_cnt;
bit K1_cnt_EN;
bit K2_cnt_EN;
bit K3_cnt_EN;
volatile bit longhit;
volatile bit speedup;
extern uint16_t longhit_cnt;


bit key_control_active = 0;

volatile bit key1_handle = 0;
volatile bit key3_handle = 0;
volatile uint16_t key1_duration = 0;
volatile uint16_t key3_duration = 0;
volatile uint16_t timer_1ms_count = 0;
volatile uint16_t key1_press_time = 0;
volatile uint16_t key3_press_time = 0;
volatile bit key1_pressed = 0;
volatile bit key3_pressed = 0;
bit Key_Long_Press = 0;  // 长按标志
bit key1_long_started = 0;  // K1长按已开始标志
bit key3_long_started = 0;  // K3长按已开始标志
bit key_short_press_mode = 0;  // 短按模式标志
int Count_1_Degree_Pulse = 0;  // 1度脉冲计数

// 窮学
bit batlow = 0, batlow1 = 0;
uint8_t batlow_cnt = 0, batlow1_cnt = 0;
bit MOTOR_RUNNING_FLAG = 0;  // 窮字塰佩炎崗
uint16_t BatV;

// 新增自转模式相关变量
bit auto_rotate_mode = 0;  // 自转模式标志
bit auto_rotate_flash = 0;  // 自转时LED闪烁标志
uint16_t auto_rotate_flash_timer = 0;  // 自转LED闪烁计时器
bit auto_rotate_running = 0;  // 自转电机运行标志
uint16_t k2_long_press_timer = 0;  // K2长按计时器
bit k2_long_press_detected = 0;  // K2长按检测标志
bit k2_released = 0;  // K2松开标志
bit k3_released = 0;  // K3松开标志
bit auto_rotate_entry_complete = 0;  // 自转模式进入完成标志

// 优化相关变量
uint8_t main_loop_counter = 0;  // 主循环计数器，用于分散耗时操作
uint8_t battery_check_divider = 0;  // 电池检测分频器
uint8_t key_scan_divider = 0;  // 按键扫描分频器
volatile uint16_t precise_k2_timer = 0;  // 使用TMR1的精确K2计时器
bit use_precise_timer = 0;  // 是否使用精确计时器标志

/****************************************************************************/
/*	Local type definitions('typedef')
*****************************************************************************/

/****************************************************************************/
/*	Local variable  definitions('static')
*****************************************************************************/
uint32_t Systemclock = 24000000;
int Self_Check = 0;

/****************************************************************************/
/*	Local function prototypes('static')
*****************************************************************************/
void Delay1ms(unsigned int z);
void Motor_Step_Control(uint8_t Direction);
void Key_Function_Switch_System(uint8_t Key_Input);
void Key_Scan(void);
void Key_Interrupt_Process(void);
void Battery_Check(void);
void LED_Control(void);
void Store_dly(uint16_t dly1);
uint16_t Restore_dly();
uint8_t last_direction = 0; 
bit direction_changed = 0;  
uint16_t original_speed; 
uint16_t speedup_cnt;    
uint16_t dly;
bit led_flash_state = 0;
uint16_t led_flash_timer = 0;
bit need_led_flash = 0;

/****************************************************************************/
/*	Function implementation - global ('extern') and local('static')
*****************************************************************************/

/*****************************************************************************
 ** \brief	 main
 ** \param [in]  none   
 ** \return 0
 *****************************************************************************/
int main(void)
{
    bit Delay_Open_Buff;
    int Key_Input, Charge_Input;
    int Key_State, Key_State_Save, Charge_State_Save;
    int Key_Keep_Time_For_System_Open;
    bit Long_Press_To_Open;
    bit Blue_Teeth_Long_Press = 0;
    int Charge_Keep_Time_For_System_Open;
    uint8_t UART_Get_CMD;
    bit Voltage_Low = 0;
    uint16_t k2k3_press_time = 0;  // K2和K3同时按下的计时

    
    GPIO_Config();
    Delay1ms(20);

    ADC_Config();

    UART_1_Config(); 
    UART_0_Config(); 

    TMR0_Config();
    TMR1_Config();



    Delay_Open_Buff = 1;
    Key_Keep_Time_For_System_Open = 0;
    Charge_Keep_Time_For_System_Open = 0;
    Charg_State_Buff = 0;
    K1_cnt_EN = 1;K1_cnt = 0;
    K2_cnt_EN = 1;K2_cnt = 0;
    K3_cnt_EN = 1;K3_cnt = 0;
	longhit = 0;
    dly = Restore_dly();  
    if(dly < 20 || dly > 2500)  
    {
        dly = 200; 
        Store_dly(dly); 
    }
    speedup = 0;


    GPIO_Key_Interrupt_Config();

    while(Delay_Open_Buff)
    {
        while(Bit_1_ms_Buff == 0);
        Bit_1_ms_Buff = 0;
        
        Key_Scan();
        Key_State = (int)Key_Buff_Return();

        // 检测K2和K3同时按下0.5秒进入自转模式
        if((K2 == 0) && (K3 == 0))
        {
            k2k3_press_time++;
            if(k2k3_press_time >= 500)  // 0.5秒
            {
                k2k3_press_time = 0;
                Delay_Open_Buff = 0;
                System_Mode_Data = System_AutoRotate;  // 进入自转模式
                auto_rotate_mode = 1;  // 设置自转模式标志
                auto_rotate_running = 0;  // 初始状态不运行
                auto_rotate_flash = 0;  // 初始不闪烁
                auto_rotate_flash_timer = 0;  // 重置闪烁计时器
                k2_long_press_timer = 0;  // 重置K2长按计时器
                k2_long_press_detected = 0;  // 重置K2长按检测标志
                MOTOR_RUNNING_FLAG = 0;  // 确保电机不运行
                Coil_OFF;  // 关闭电机线圈
                k2_released = 0;
                k3_released = 0;
                auto_rotate_entry_complete = 0;  // 标记进入过程未完成
            }
        }
        else
        {
            k2k3_press_time = 0;
            
            if((Key_State == 0x02) || (Key_State == 0x08) || (Key_State == 0x10))
            {
                if(Key_State == 0x02)
                {
                    Key_Keep_Time_For_System_Open++;
                    if(Key_Keep_Time_For_System_Open >= 500)
                    {
                        Key_Keep_Time_For_System_Open = 0;
                        Delay_Open_Buff = 0;
                        Long_Press_To_Open = 1;
                        System_Mode_Data = System_Run;
                    }
                }
                else if((Key_State & 0x08) || (Key_State & 0x10))
                {
                    Charge_Keep_Time_For_System_Open++;
                    if(Charge_Keep_Time_For_System_Open >= 200)
                    {
                        Charge_Keep_Time_For_System_Open = 0;
                        Delay_Open_Buff = 0;
                        System_Mode_Before_Charge = System_Off;  
                        Charge_Was_Connected = 1; 
                        System_Mode_Data = System_Standby;
                    }
                }
                else if(Key_State == 0x00)
                {
                    Key_Keep_Time_For_System_Open = 0;
                    Long_Press_To_Open = 0;
                    Charge_Keep_Time_For_System_Open = 0;
                    Key_State_Save = 0x00;
                    Power = 0;
                }
            }
            
            else if((Key_State & 0x07) == 0x07)
            {
                Function_UART_Send_CMD(Re_Pair);
            }
        }

        Key_State_Save = Key_State & 0x07;
        Charge_State_Save = (Key_State & 0x18) >> 3;
    }

	Power = 1;
	Motor_Direction_Data = Direction_Clockwise;
	Motor_Speed_Data = Motor_Fast_Speed;
	UART_Data_Init();

    
    if(System_Mode_Before_Charge == System_Off && Charge_Was_Connected == 1)
    {
        
        System_Mode_Data = System_Standby;
    }
    else
    {
       
    }

    while(1)
    {
        while(Bit_1_ms_Buff == 0);
        Bit_1_ms_Buff = 0;

        main_loop_counter++;  // 主循环计数器递增

        // 优化按键扫描频率：每2ms扫描一次
        if(++key_scan_divider >= 2)
        {
            key_scan_divider = 0;
            Key_Scan();
        }
        Key_State = Key_Buff_Return();

        Key_Input = Key_State & 0x07;
        Charge_Input = (Key_State & 0x18) >> 3;

        // 优化电池检测频率：每10ms检测一次，分散到不同的循环周期
        if(++battery_check_divider >= 10)
        {
            battery_check_divider = 0;
            Battery_Check();
        }

        // LED控制保持每1ms执行，确保闪烁效果
        LED_Control();

        
        if(Charge_Input == 0x01)
        {
            
            if(Charg_State_Buff == 0)  
            {
                
                if(Charge_Was_Connected == 0)
                {
                    System_Mode_Before_Charge = System_Mode_Data; 
                    Charge_Was_Connected = 1;
                }
            }
            Charg_State_Buff = 1;
        }
        else if(Charge_Input == 0x02)
        {
            
            if(Charg_State_Buff == 0)  // 
            {
                
                if(Charge_Was_Connected == 0)
                {
                    System_Mode_Before_Charge = System_Mode_Data;  // 
                    Charge_Was_Connected = 1;
                }
            }
            Charg_State_Buff = 1;
        }
        else
        {
            // 
            if(Charg_State_Buff == 1 && Charge_Was_Connected == 1)  // 
            {
                // 
                System_Mode_Data = System_Mode_Before_Charge;
                Charge_Was_Connected = 0;

                // 
                // 
            }
            Charg_State_Buff = 0;
        }

        // 自转模式处理
        if(System_Mode_Data == System_AutoRotate)
        {
            // 检测K2和K3的松开状态，确保进入自转模式后的正确处理
            if(!auto_rotate_entry_complete)
            {
                // 检测K2松开
                if(K2 != 0)  // K2松开
                {
                    k2_released = 1;
                }
                // 检测K3松开
                if(K3 != 0)  // K3松开
                {
                    k3_released = 1;
                }

                // 当两个按键都松开后，标记进入过程完成，进入待机状态
                if(k2_released && k3_released)
                {
                    auto_rotate_entry_complete = 1;
                    auto_rotate_running = 0;  
                    MOTOR_RUNNING_FLAG = 0;
                    Coil_OFF;
                    auto_rotate_flash = 0;
                }
            }
            else
            {
                // 进入完成后的正常按键处理
                // 自转模式下K2长按检测 - 使用精确计时器
                if(Key_Input == 0x02)  // K2按下
                {
                    if(!use_precise_timer)
                    {
                        // 开始使用TMR1进行精确计时
                        use_precise_timer = 1;
                        precise_k2_timer = 0;
                        // 重新配置TMR1为2ms中断，用于精确计时
                        TMR_Stop(TMR1);
                        TMR_ConfigTimerPeriod(TMR1, (65536-4000)>>8, 65536-4000);  // 4000*0.5us = 2ms
                        TMR_Start(TMR1);
                    }

                    // 使用精确计时器判断长按（2000ms / 2ms = 1000次中断）
                    if(precise_k2_timer >= 1000)  // 精确2秒长按检测
                    {
                        k2_long_press_detected = 1;
                        System_Mode_Data = System_Off;
                        auto_rotate_mode = 0;
                        auto_rotate_running = 0;
                        MOTOR_RUNNING_FLAG = 0;
                        Coil_OFF;
                        auto_rotate_flash = 0;
                        use_precise_timer = 0;  // 停止精确计时
                        TMR_Stop(TMR1);
                    }
                }
                else
                {
                    // K2松开，停止精确计时
                    if(use_precise_timer)
                    {
                        use_precise_timer = 0;
                        TMR_Stop(TMR1);
                        precise_k2_timer = 0;
                    }
                    k2_long_press_detected = 0;
                }
            }

            // 自转模式下K1和K3短按处理（只有在进入完成后才响应）
            if(auto_rotate_entry_complete)
            {
                if((Key_Input == 0x04) && (Key_Input != Key_State_Save))  // K3短按
                {
                    if(!auto_rotate_running)
                    {
                        // 如果电机停转状态，开始逆时针自转
                        Motor_Direction_Data = Direction_Cclockwise;
                        Motor_Speed_Data = Motor_Auto_Rotate_Speed;  // 自转使用2倍速度
                        auto_rotate_running = 1;
                        MOTOR_RUNNING_FLAG = 1;
                        auto_rotate_flash = 1;
                        Count_1_Degree_Pulse = 0;
                    }
                    else if(Motor_Direction_Data == Direction_Cclockwise)
                    {
                        // 如果已经在逆时针自转，停止
                        auto_rotate_running = 0;
                        MOTOR_RUNNING_FLAG = 0;
                        Coil_OFF;
                        auto_rotate_flash = 0;
                        
                    }
                    else if(Motor_Direction_Data == Direction_Clockwise)
                    {
                        // 如果正在顺时针自转，立即切换到逆时针自转
                        Motor_Direction_Data = Direction_Cclockwise;
                        Motor_Speed_Data = Motor_Auto_Rotate_Speed;
                        auto_rotate_running = 1;
                        MOTOR_RUNNING_FLAG = 1;
                        auto_rotate_flash = 1;
                        Count_1_Degree_Pulse = 0;
                    }
                }
                else if((Key_Input == 0x01) && (Key_Input != Key_State_Save))  // K1短按
                {
                    if(!auto_rotate_running)
                    {
                        // 如果电机停转状态，开始顺时针自转
                        Motor_Direction_Data = Direction_Clockwise;
                        Motor_Speed_Data = Motor_Auto_Rotate_Speed;  // 自转使用2倍速度
                        auto_rotate_running = 1;
                        MOTOR_RUNNING_FLAG = 1;
                        auto_rotate_flash = 1;
                        Count_1_Degree_Pulse = 0;
                    }
                    else if(Motor_Direction_Data == Direction_Clockwise)
                    {
                        // 如果已经在顺时针自转，停止
                        auto_rotate_running = 0;
                        MOTOR_RUNNING_FLAG = 0;
                        Coil_OFF;
                        auto_rotate_flash = 0;
                    }
                    else if(Motor_Direction_Data == Direction_Cclockwise)
                    {
                        // 如果正在逆时针自转，立即切换到顺时针自转
                        Motor_Direction_Data = Direction_Clockwise;
                        Motor_Speed_Data = Motor_Auto_Rotate_Speed;  // 自转使用2倍速度
                        auto_rotate_running = 1;
                        MOTOR_RUNNING_FLAG = 1;
                        auto_rotate_flash = 1;
                        Count_1_Degree_Pulse = 0;
                    }
                }
            }

            // 自转模式下电机控制
            if(auto_rotate_running && (Bit_N_ms_Buff == 1))
            {
                Bit_N_ms_Buff = 0;
                Count_1_Degree_Pulse++;

                if(Count_1_Degree_Pulse <= 1)
                {
                    Motor_Step_Control(Motor_Direction_Data);
                }
                else
                {
                    Count_1_Degree_Pulse = 0;
                }
            }

            // 自转模式下LED闪烁控制（仅更新闪烁状态，实际LED由LED_Control函数控制）
            if(auto_rotate_running)
            {
                // 自转时红灯以10Hz频率闪烁
                auto_rotate_flash_timer++;
                if(auto_rotate_flash_timer >= AUTO_ROTATE_FLASH_INTERVAL)
                {
                    auto_rotate_flash_timer = 0;
                    auto_rotate_flash = !auto_rotate_flash;
                }
            }
            else
            {
                // 停转时重置闪烁状态
                auto_rotate_flash = 0;
                auto_rotate_flash_timer = 0;
            }
        }
        // 原有模式处理
        else
        {
            Key_Interrupt_Process();

            if((Key_Input == 0x02) && (Key_Input != Key_State_Save) && (Long_Press_To_Open == 1))
            {
                Long_Press_To_Open = 0;
            }
            else if((Key_Input == 0x05) && (Key_Input != Key_State_Save))
            {
                Key_Function_Switch_System(Key_Input);
            }
            else if((Key_Input == 0x02) && (Long_Press_To_Open == 0))
            {
                // 使用精确计时器进行关机长按检测
                if(!use_precise_timer)
                {
                    use_precise_timer = 1;
                    precise_k2_timer = 0;
                    TMR_Stop(TMR1);
                    TMR_ConfigTimerPeriod(TMR1, (65536-4000)>>8, 65536-4000);  // 2ms精确计时
                    TMR_Start(TMR1);
                }

                if(precise_k2_timer >= 1000)  // 精确2秒检测
                {
                    use_precise_timer = 0;
                    TMR_Stop(TMR1);
                    Key_Function_Switch_System(Key_Input);
                    LEDGOFF;
                    Power = 0;
                    while(K2 == 0);
                }
            }
            else if(Key_Input != 0x02 && use_precise_timer)
            {
                // K2松开，停止精确计时
                use_precise_timer = 0;
                TMR_Stop(TMR1);
                precise_k2_timer = 0;
            }
        }

        Key_State_Save = Key_Input;

        // 优化UART处理：分散到不同的循环周期，避免阻塞
        if((main_loop_counter & 0x03) == 0)  // 每4ms处理一次UART
        {
            if(Get_String_Buff)
            {
                if(Get_String_Wait_Time >= 8)
                {
                    Get_String_Wait_Time = 0;
                    Get_String_Buff = 0;
                    UART_Get_CMD = UART_Data_Process();
                }
                else
                {
                    Get_String_Wait_Time++;
                }

                if(UART_Get_CMD == 0xEE)
                {
                    UART_Get_CMD = 0x00;
                    Function_UART_Send_CMD(Data_Error);
                }
                else if(UART_Get_CMD != 0x00)
                {
                    Function_UART_Send_CMD(Data_Pass);

                switch(UART_Get_CMD)
                {
                    case 0xC1:
					{
						dly += 20;  
						if(dly >= 2500) 
                        {
							dly = 2500;  
						}
						else
						{
                            need_led_flash = 1;
                            led_flash_timer = 0;
						}	
                        Store_dly(dly);  
                        UART_Get_CMD = 0;  
					}
                        break;
                    case 0xC2:
                        original_speed = Motor_Middle_Speed;
                        if(Motor_Direction_Data != Direction_Clockwise)
                        {
							speedup = 1;
						}
                        Motor_Direction_Data = Direction_Clockwise;
                        Blue_Teeth_Long_Press = 0;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                        break;
                    case 0xC3:
					{
						dly -= 20;  
						if(dly < 20) 
                        { 
                            dly = 20; 
                        }  
						else
						{
							need_led_flash = 1;
                            led_flash_timer = 0;
						}	
                        Store_dly(dly);  
                        UART_Get_CMD = 0;  
					}
                        break;
                    case 0xC4:
                        original_speed = Motor_Middle_Speed;
                        if(Motor_Direction_Data != Direction_Cclockwise)
                        {
							speedup = 1;
						}
                        Motor_Direction_Data = Direction_Cclockwise;
                        Blue_Teeth_Long_Press = 0;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                        break;
                    case 0xC5:
                        original_speed = Motor_Slow_Speed;
                        if(Motor_Direction_Data != Direction_Cclockwise)
                       {
                           speedup = 1;
                       }
                        Motor_Direction_Data = Direction_Cclockwise;
                        Blue_Teeth_Long_Press = 0;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                        break;
                    case 0xC6:
                        original_speed = Motor_Slow_Speed;
                        if(Motor_Direction_Data != Direction_Clockwise)
                       {
                           speedup = 1;
                       }
                        Motor_Direction_Data = Direction_Clockwise;
                        Blue_Teeth_Long_Press = 0;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                        break;
                    case 0xC9:
                        System_Mode_Data = System_Standby;
                        MOTOR_RUNNING_FLAG = 0;
                        Coil_OFF;   
                        Blue_Teeth_Long_Press = 0;
                        break;
                    case 0xCA:
                    {
                        original_speed = Motor_Middle_Speed;
                        if(Motor_Direction_Data != Direction_Clockwise)
						{
							speedup = 1;
						}
                        Motor_Direction_Data = Direction_Clockwise;
                        Blue_Teeth_Long_Press = 1;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                    }
                        break;
                    case 0xCC:
                    {
                        original_speed = Motor_Middle_Speed;
                        if(Motor_Direction_Data != Direction_Cclockwise)
                        {
							speedup = 1;
						}
						Motor_Direction_Data = Direction_Cclockwise;
                        Blue_Teeth_Long_Press = 1;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                    }
                        break;
                    case 0xCD:
                    {
                        original_speed = Motor_Slow_Speed;
                        if(Motor_Direction_Data != Direction_Cclockwise)
                       {
                           speedup = 1;
                       }
                        Motor_Direction_Data = Direction_Cclockwise;
                        Blue_Teeth_Long_Press = 1;
                        key_control_active = 0; 
                        System_Mode_Data = System_Run;
                    }
                        break;
                    case 0xCE:
                    {
                        original_speed = Motor_Slow_Speed;
                        if(Motor_Direction_Data != Direction_Clockwise)
                       {
                           speedup = 1;
                       }
                        Motor_Direction_Data = Direction_Clockwise;
                        Blue_Teeth_Long_Press = 1;
                        key_control_active = 0;  
                        System_Mode_Data = System_Run;
                    }
                        break;
                    case 0xCF:
                    {
                        System_Mode_Data = System_Standby;
                        Blue_Teeth_Long_Press = 0;
                    }
                        break;
                    default:
                        break;
                }
            }
        }  // UART处理结束
        }

        
        if((System_Mode_Data == System_Run) && (Bit_N_ms_Buff == 1))
        {
            MOTOR_RUNNING_FLAG = 1;
            Bit_N_ms_Buff = 0;
            Count_1_Degree_Pulse++;
             
            if(last_direction != Motor_Direction_Data) 
            {
                direction_changed = 1;
                speedup = 1; 
                last_direction = Motor_Direction_Data;
            }
            if(Blue_Teeth_Long_Press == 1 || Key_Long_Press == 1)
            {
                Motor_Step_Control(Motor_Direction_Data);
                Count_1_Degree_Pulse = 0;
                direction_changed = 0;
            }
            if(speedup == 1)
            {
				Motor_Speed_Data = Motor_Fast_Speed;
			}
            if(speedup == 0)
            {
                
                if(key_control_active == 1)
                {
                    Motor_Speed_Data = Motor_Key_Control_Speed;  
                }
                else
                {
                    
                    if(original_speed == Motor_Middle_Speed)
                    {
                        Motor_Speed_Data = Motor_Middle_Speed;
                    }
                    else if(original_speed == Motor_Slow_Speed)
                    {
                        Motor_Speed_Data = Motor_Slow_Speed;
                    }
                }
            }
            if(key_short_press_mode == 1)
            {
                if(key1_pressed == 0 && key3_pressed == 0)
                {
                    if(Count_1_Degree_Pulse <= 3)  
                    {
                        Motor_Step_Control(Motor_Direction_Data);
                    }
                    else
                    {
                        Coil_OFF;
                        Count_1_Degree_Pulse = 0;
                        System_Mode_Data = System_Standby;
                        MOTOR_RUNNING_FLAG = 0;
                        direction_changed = 0;
                        key_short_press_mode = 0;  
                        Key_Long_Press = 0;  
                        key_control_active = 0;  
                    }
                }
                else
                {
                    Coil_OFF;
                    Count_1_Degree_Pulse = 0;
                    System_Mode_Data = System_Standby;
                    MOTOR_RUNNING_FLAG = 0;
                    direction_changed = 0;
                    key_short_press_mode = 0;
                    key_control_active = 0;  
                }
            }
            else if(Count_1_Degree_Pulse <= 1)
            {
                Motor_Step_Control(Motor_Direction_Data);
            }
            else
            {
                if(Key_Long_Press == 0 && Blue_Teeth_Long_Press == 0)
                {
                    Coil_OFF;
                    Count_1_Degree_Pulse = 0;
                    System_Mode_Data = System_Standby;
                    MOTOR_RUNNING_FLAG = 0;
                    direction_changed = 0;
                    key_control_active = 0;  
                }
                else
                {
                    if(Key_Long_Press == 1 || Blue_Teeth_Long_Press == 1)
                    {
                        Count_1_Degree_Pulse = 0;
                    }
                    else
                    {
                        Coil_OFF;
                        Count_1_Degree_Pulse = 0;
                        System_Mode_Data = System_Standby;
                        MOTOR_RUNNING_FLAG = 0;
                        direction_changed = 0;
                        key_control_active = 0;  
                    }
                }
            }
        }
        else if(System_Mode_Data == System_Standby)
        {
            MOTOR_RUNNING_FLAG = 0;  
            Motor_D = 0;
            Motor_C = 0;
            Motor_B = 0;
            Motor_A = 0;
        }
        else if(System_Mode_Data == System_Check)
        {
        }
        else if(System_Mode_Data == System_Off)
        {
            Power = 0;        // 
            LEDROFF;          // 
            LEDGOFF;          // 
            Motor_D = 1;      // 
            Motor_C = 1;
            Motor_B = 1;
            Motor_A = 1;

            // 清理自转模式相关状态
            auto_rotate_mode = 0;  // 退出自转模式
            auto_rotate_running = 0;  // 停止自转运行
            auto_rotate_flash = 0;  // 停止闪烁
            auto_rotate_flash_timer = 0;  // 重置闪烁计时器
            k2_long_press_timer = 0;  // 重置K2长按计时器
            k2_long_press_detected = 0;  // 重置K2长按检测标志
            k2_released = 0;  // 重置K2松开标志
            k3_released = 0;  // 重置K3松开标志
            auto_rotate_entry_complete = 0;  // 重置进入完成标志

            while(K2 == 0);

            SYS_EnableWakeUp();

            SYS_EnterStop();

            GPIO_Config();
            ADC_Config();
            UART_1_Config();
            UART_0_Config();
            TMR0_Config();
            TMR1_Config();
            GPIO_Key_Interrupt_Config();

            System_Mode_Data = System_Standby;
        }
    }
}


void Delay1ms(unsigned int z)
{
    unsigned int x, y;
    for(y = 0; y < z; y++)
        for(x = 0; x <= 2000; x++);
}

void Motor_Step_Control(uint8_t Direction)
{
    static uint8_t Step_No = 0;
 
    if(Direction == Direction_Clockwise)
    {
        Step_No++;
        if(Step_No >= 8)
        {
            Step_No = 0;
        }
    }
    else
    {
        if(Step_No < 1)
        {
            Step_No = 8;
        }
        Step_No--;
    }

    switch(Step_No)
    {
        case 0: Coil_A; break;
        case 1: Coil_AB; break;
        case 2: Coil_B; break;
        case 3: Coil_BC; break;
        case 4: Coil_C; break;
        case 5: Coil_CD; break;
        case 6: Coil_D; break;
        case 7: Coil_DA; break;
        case 8: 
        default: Coil_OFF; 
        
        break;
    }
}

void Key_Function_Switch_System(uint8_t Key_Input)
{
    switch(Key_Input)
    {
        case 0x01:
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            Motor_Direction_Data = Direction_Clockwise;
            break;
        case 0x02:
            if(Charg_State_Buff == 0)
            {
                System_Mode_Data = System_Off;
            }
            else
            {
                System_Mode_Data = System_Standby;
            }
            break;
        case 0x04:
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            Motor_Direction_Data = Direction_Cclockwise;
            break;
        case 0x05:
            System_Mode_Data = System_Check;
            break;
        default:
            break;
    }
}

// 电池检测函数
void Battery_Check(void)
{
    static uint8_t adc_state = 0;  // ADC状态机
    static uint8_t sample_count = 0;  // 采样计数
    static uint16_t adc_sum = 0;  // ADC累加值

    // 状态机方式处理ADC，避免阻塞
    switch(adc_state)
    {
        case 0:  // 启动ADC转换
            ADC_StartConvert(Battery_ADC);
            adc_state = 1;
            break;

        case 1:  // 检查ADC是否完成
            if(ADC_GetConvertIntFlag())
            {
                adc_sum += ADC_GetResult();
                sample_count++;
                ADC_ClearConvertIntFlag();

                if(sample_count >= 4)  // 4次采样平均
                {
                    BatV = adc_sum >> 2;  // 除以4取平均
                    adc_sum = 0;
                    sample_count = 0;
                    adc_state = 2;  // 进入处理状态
                }
                else
                {
                    adc_state = 0;  // 继续采样
                }
            }
            break;

        case 2:  // 处理电池电压判断
            if(BatV < BAT_WARNING_LEVEL)
            {
                batlow1_cnt++;
                if(batlow1_cnt > 5) batlow1 = 1;
            }
            else
            {
                batlow1 = 0;
                batlow1_cnt = 0;
            }

            if(BatV < BAT_LOW_LEVEL)
            {
                batlow_cnt++;
                if(batlow_cnt > 5) batlow = 1;
            }
            else
            {
                batlow_cnt = 0;
                batlow = 0;
            }
            adc_state = 0;  // 重置状态机
            break;

        default:
            adc_state = 0;
            break;
    }
}

void Store_dly(uint16_t dly1)
{
	IRQ_ALL_DISABLE();
	FLASH_UnLock();
	FLASH_Erase(FLASH_DATA,dly_addr);
	FLASH_Write(FLASH_DATA,dly_addr,dly1);
    FLASH_Lock();	
	IRQ_ALL_ENABLE();
}

uint16_t Restore_dly()
{
	uint16_t temp;
	
	IRQ_ALL_DISABLE();
	FLASH_UnLock();
	temp=FLASH_Read(FLASH_DATA,dly_addr);
    FLASH_Lock();	
	IRQ_ALL_ENABLE();	
	
	return temp;
}

void LED_Control(void)
{
    // 自转模式下的特殊LED控制
    if(System_Mode_Data == System_AutoRotate)
    {
        // 自转模式下的充电LED处理
        if(Charg_State_Buff)
        {
            // 充电时：绿灯闪烁，红灯灭（无论电机是否运行，都使用充电闪烁频率）
            LEDROFF;  // 红灯灭
            if(CHG_CHARGE == 0)  // 正在充电
            {
                // 使用专门的充电闪烁频率（2Hz），不受电机运行状态影响
                if(charge_flash) LEDGON;
                else LEDGOFF;
            }
            else  // 充满电
            {
                LEDGON;  // 绿灯长亮
            }
        }
        else
        {
            // 未充电时：根据自转状态显示
            LEDGOFF;  // 绿灯灭
            if(auto_rotate_running)
            {
                // 自转运行时：红灯闪烁
                if(auto_rotate_flash)
                {
                    LEDRON;  // 红灯亮
                }
                else
                {
                    LEDROFF; // 红灯灭
                }
            }
            else
            {
                // 自转待机时：红灯长亮
                LEDRON;
            }
        }
    }
    else
    {
        // 非自转模式下的原有LED控制逻辑
        if(Charg_State_Buff)
        {
            LEDROFF;
            if(CHG_CHARGE == 0)
            {
                // 使用2Hz充电闪烁频率
                if(charge_flash) LEDGON;
                else LEDGOFF;
            }
            else
            {
                LEDGON;
            }
        }
        else
        {
            if(batlow1 == 1)
            {
                LEDGOFF;
                if(System_Mode_Data == System_Standby) LEDRON;
                else
                {
                    if(ledonoff1 == 1) LEDRON;
                    else LEDROFF;
                }
            }
            else
            {
                LEDROFF;
                if(System_Mode_Data == System_Standby) LEDGON;
                else
                {
                    if(ledonoff1 == 1) LEDGON;
                    else LEDGOFF;
                }
            }
        }
    }
    
        // LED闪烁处理dly
    if(need_led_flash) 
    {
        led_flash_timer++;
        if(led_flash_timer < 80)  // 前80ms保持LED关闭
        {
            LEDGOFF;
        }
        else  // 到达150ms后恢复LED并结束闪烁
        {
            LEDGON;
            need_led_flash = 0;
            led_flash_timer = 0;
        }
    }

    // LED闪烁计时
    ledonoff_cnt++;
    if(ledonoff_cnt > LED_FLASH_INTERVAL)
    {
        ledonoff_cnt = 0;
        ledonoff ^= 1;
    }

    // 充电闪烁计时（2Hz频率）
    charge_flash_cnt++;
    if(charge_flash_cnt > CHARGE_FLASH_INTERVAL)
    {
        charge_flash_cnt = 0;
        charge_flash ^= 1;
    }
    
    // 运行状态LED闪烁计时
    if(System_Mode_Data == System_Run)
    {
        ledonoff1_cnt++;
        if(ledonoff1_cnt > LED_FLASH_INTERVAL/2)
        {
            ledonoff1_cnt = 0;
            ledonoff1 ^= 1;
        }
    }
    else
    {
        ledonoff1_cnt = 0;
        ledonoff1 = 0;
    }
}

/*****************************************************************************
 ** \brief	 Key_Interrupt_Process
 ** \param [in] none
 **          新的中断式按键处理函数
 ** \return  none
 ** \note  处理K1和K3的长短按检测
 ******************************************************************************/
void Key_Interrupt_Process(void)
{
    // 安全检查：如果K1和K3都没有被按下，强制清除所有状态
    if(!key1_pressed && !key3_pressed && !key1_handle && !key3_handle)
    {
        if(Key_Long_Press == 1)
        {
            Key_Long_Press = 0;
            key1_long_started = 0;
            key3_long_started = 0;
            key_short_press_mode = 0;
            key_control_active = 0;  // 清除按键控制标志
            System_Mode_Data = System_Standby;
        }
    }

    // K1按键处理 - 按下时开始动作
    if(key1_pressed && timer_1ms_count - key1_press_time >= 150)
    {
        // K1长按：按住150ms后开始持续动作
        if(key1_long_started == 0 && key_short_press_mode == 0)  // 确保没有其他模式
        {
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            System_Mode_Data = System_Run;
            Key_Long_Press = 1;
            key1_long_started = 1;
            key_short_press_mode = 0;  // 确保清除短按模式
            key_control_active = 1;  // 设置按键控制标志
            Key_Function_Switch_System(0x01);
        }
    }

    // K1按键松开处理
    if(key1_handle)
    {
        key1_handle = 0;  // 清除处理标志

        if(key1_duration >= 10 && key1_duration < 150)  // 短按：10ms-150ms
        {
            // 强制清除所有状态，然后设置短按
            Key_Long_Press = 0;
            key1_long_started = 0;
            key3_long_started = 0;

            // K1短按处理：执行3个脉冲
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            System_Mode_Data = System_Run;
            key_short_press_mode = 1;  // 设置短按模式
            key_control_active = 1;  // 设置按键控制标志
            Key_Function_Switch_System(0x01);
            Count_1_Degree_Pulse = 0;  // 重置计数器
        }
        else if(key1_duration >= 150)  // 长按松开
        {
            // 强制停止电机并清除所有状态
            System_Mode_Data = System_Standby;
            Key_Long_Press = 0;
            key1_long_started = 0;
            key_short_press_mode = 0;
            key_control_active = 0;  // 清除按键控制标志
        }
    }

    // K3按键处理 - 按下时开始动作
    if(key3_pressed && timer_1ms_count - key3_press_time >= 150)
    {
        // K3长按：按住150ms后开始持续动作
        if(key3_long_started == 0 && key_short_press_mode == 0)  // 确保没有其他模式
        {
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            System_Mode_Data = System_Run;
            Key_Long_Press = 1;
            key3_long_started = 1;
            key_short_press_mode = 0;  // 确保清除短按模式
            key_control_active = 1;  // 设置按键控制标志
            Key_Function_Switch_System(0x04);
        }
    }

    // K3按键松开处理
    if(key3_handle)
    {
        key3_handle = 0;  // 清除处理标志

        if(key3_duration >= 10 && key3_duration < 150)  // 短按：10ms-150ms
        {
            // 强制清除所有状态，然后设置短按
            Key_Long_Press = 0;
            key1_long_started = 0;
            key3_long_started = 0;

            // K3短按处理：执行3个脉冲
            Motor_Speed_Data = Motor_Key_Control_Speed;  // 按键控制使用1/2速度
            System_Mode_Data = System_Run;
            key_short_press_mode = 1;  // 设置短按模式
            key_control_active = 1;  // 设置按键控制标志
            Key_Function_Switch_System(0x04);
            Count_1_Degree_Pulse = 0;  // 重置计数器
        }
        else if(key3_duration >= 150)  // 长按松开
        {
            // 强制停止电机并清除所有状态
            System_Mode_Data = System_Standby;
            Key_Long_Press = 0;
            key3_long_started = 0;
            key_short_press_mode = 0;
            key_control_active = 0;  // 清除按键控制标志
        }
    }
}

