# k2_long_press_timer ???????????

## ?????????????

### 1. ??????
- k2_long_press_timer?????660?????????????2??
- ???????660ms = 0.66??
- ???????2000ms
- ?????????3??

### 2. ???????
??????��????????????????????1ms??????????????????1ms??

1. **???????????**??Key_Scan()?????1ms???
2. **?????????**??Battery_Check()?��?ADC???????
3. **UART????????**??UART_Data_Process()??????
4. **????????????????**??Battery_Check()?��?100ms???

## ?????????

### 1. ??????????��?????????

#### ??????????
```c
// ???????1ms???
Key_Scan();

// ??????2ms??????
if(++key_scan_divider >= 2)
{
    key_scan_divider = 0;
    Key_Scan();
}
```

#### ????????
```c
// ???????10ms???????????????ADC???
BatV = ADC(Battery_ADC);  // ???????ADC???

// ??????10ms??????????????????
if(++battery_check_divider >= 10)
{
    battery_check_divider = 0;
    Battery_Check();  // ?????????????
}
```

### 2. ????????????????????????

#### UART???????
```c
// ???????1ms????UART
if(Get_String_Buff) { ... }

// ??????4ms???????UART
if((main_loop_counter & 0x03) == 0)  // ?4ms???????
{
    if(Get_String_Buff) { ... }
}
```

#### ADC????????
```c
// ??????????ADC????????????????
switch(adc_state)
{
    case 0: ADC_Start(); break;           // ???????
    case 1: if(ADC_Ready()) { ... }; break;  // ??????
    case 2: ProcessResult(); break;       // ???????
}
```

### 3. ???????????????????????

#### TMR1????????
```c
// K2??????????TMR1??2ms????��?
if(Key_Input == 0x02)  // K2????
{
    if(!use_precise_timer)
    {
        use_precise_timer = 1;
        precise_k2_timer = 0;
        // ????TMR1?2ms?��?
        TMR_ConfigTimerPeriod(TMR1, (65536-4000)>>8, 65536-4000);
        TMR_Start(TMR1);
    }
    
    // ???2????2000ms / 2ms = 1000???��?
    if(precise_k2_timer >= 1000)
    {
        // ??��???????
    }
}
```

#### TMR1?��???????
```c
void Timer1_IRQHandler(void) interrupt TMR1_VECTOR 
{
    TH1 = (65536-4000)>>8;  // 2ms?????
    TL1 = 65536-4000;
    
    if(use_precise_timer)
    {
        precise_k2_timer++;  // ??????
    }
}
```

### 4. ???????????ADC?????????

#### ??????????
- **???????**????1ms???2ms????????????????
- **?????**??????????????10ms????
- **UART????**????1ms???4ms????????????????
- **LED????**??????1ms????????��????

## ???��?????

### 1. ???????????
- **??????**??k2_long_press_timer = 660 ?? 2000ms??????200%??
- **???????**??precise_k2_timer = 1000 ?? 2ms = 2000ms?????<1%??

### 2. ???????????
- ?????????????????????1ms
- ???????70%?????????
- ?????????????????

### 3. ????????????
- ??????��?????????
- ?????????????????LED????????????
- ????????????????????

## ???????????

### 1. ????????????
```c
// ???��??????
timer_test_enable = 1;

// ʹ��ʾ�����۲�P36����
// ????????0.5Hz???2?????��?
// ???????????????
```

### 2. ?????????
- ???K2????2????????
- ????????K2????2?????????
- ??????????????????????��???

### 3. ???????
- ???????????????????
- ???????????
- ???CPU?????????��??

## ??????

### 1. ????????
- ???????????????
- ???????????

### 2. ???????
- ?????? `timer_test_enable = 1;` ???��???
- ʹ��ʾ�����۲�P36������֤��ʱ׼ȷ��
- ??????????????????

### 3. ����֧��
- ������ԭ�еĵ������
- �����˶�ʱ�����Թ���
- �ɸ�����Ҫ�������ַ�Ƶ����

### 4. ��������˵��
- **��������**��P36 (TEST_PIN)
- **�����ź�**��1�뷭תһ�Σ�����Ƶ��0.5Hz
- **ʹ�÷���**��ȡ��ע�� `timer_test_enable = 1;` ���ò���
- **��֤��ʽ**��ʹ��ʾ�����۲�P36���ţ�Ӧ����0.5Hz�ķ����ź�
- **ע������**��������ɺ�������ע�͸����Խ�ʡ��Դ
